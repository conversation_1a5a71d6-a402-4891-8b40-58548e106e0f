using System.Collections.Generic;
using Xunit;
using Zin.Application.Services.ZinPag;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Movimentacoes
{ 
public class MovimentacoesServiceTests
{
    [Fact]
    public void CalcularValorAPagar_DeveRetornarValorCorreto_QuandoNaoHaExclusao()
    {
        var autorizacao = new ItemVersao
        {
            Id = 1,
            TipoMovimento = TipoMovimentoItem.Autorizacao,
            ValorTotal = 100m,
            Pagamentos = new List<PagamentoItemVersao>
            {
                new PagamentoItemVersao
                {
                    Pagamento = new Pagamento { Valor = 30m }
                }
            }
        };

        var itens = new List<ItemVersao> { autorizacao };

        var result = MovimentacoesService.CalcularValorAPagar(itens);

        Assert.Equal(70m, result);
    }

    [Fact]
    public void CalcularValorAPagar_DeveIgnorarAutorizacaoComExclusao()
    {
        var autorizacao = new ItemVersao
        {
            Id = 1,
            TipoMovimento = TipoMovimentoItem.Autorizacao,
            ValorTotal = 100m
        };
        var exclusao = new ItemVersao
        {
            Id = 2,
            TipoMovimento = TipoMovimentoItem.Exclusao,
            ValorTotal = 100m,
            IdVersaoAnterior = 1
        };

        var itens = new List<ItemVersao> { autorizacao, exclusao };

        var result = MovimentacoesService.CalcularValorAPagar(itens);

        Assert.Equal(0m, result);
    }

    [Fact]
    public void CalcularValorAPagar_DeveSubtrairTodosPagamentos()
    {
        var autorizacao = new ItemVersao
        {
            Id = 1,
            TipoMovimento = TipoMovimentoItem.Autorizacao,
            ValorTotal = 200m,
            Pagamentos = new List<PagamentoItemVersao>
            {
                new PagamentoItemVersao { Pagamento = new Pagamento { Valor = 50m } },
                new PagamentoItemVersao { Pagamento = new Pagamento { Valor = 30m } }
            }
        };

        var itens = new List<ItemVersao> { autorizacao };

        var result = MovimentacoesService.CalcularValorAPagar(itens);

        Assert.Equal(120m, result);
    }

    [Fact]
    public void CalcularValorPago_DeveSomarTodosPagamentos()
    {
        var autorizacao = new ItemVersao
        {
            Id = 1,
            TipoMovimento = TipoMovimentoItem.Autorizacao,
            ValorTotal = 100m,
            Pagamentos = new List<PagamentoItemVersao>
            {
                new PagamentoItemVersao { Pagamento = new Pagamento { Valor = 10m } },
                new PagamentoItemVersao { Pagamento = new Pagamento { Valor = 20m } }
            }
        };
        var exclusao = new ItemVersao
        {
            Id = 2,
            TipoMovimento = TipoMovimentoItem.Exclusao,
            ValorTotal = 50m,
            Pagamentos = new List<PagamentoItemVersao>
            {
                new PagamentoItemVersao { Pagamento = new Pagamento { Valor = 5m } }
            }
        };

        var itens = new List<ItemVersao> { autorizacao, exclusao };

        var result = MovimentacoesService.CalcularValorPago(itens);

        Assert.Equal(35m, result);
    }

    [Fact]
    public void CalcularValorARessarcir_DeveRetornarValorCorreto()
    {
        var exclusao = new ItemVersao
        {
            Id = 1,
            TipoMovimento = TipoMovimentoItem.Exclusao,
            ValorTotal = 80m,
            Ressarcimentos = new List<RessarcimentoItemVersao>
            {
                new RessarcimentoItemVersao { Ressarcimento = new Ressarcimento { Valor = 30m } }
            }
        };

        var itens = new List<ItemVersao> { exclusao };

        var result = MovimentacoesService.CalcularValorARessarcir(itens);

        Assert.Equal(50m, result);
    }

    [Fact]
    public void CalcularValorARessarcir_DeveSomarApenasExclusoes()
    {
        var exclusao1 = new ItemVersao
        {
            Id = 1,
            TipoMovimento = TipoMovimentoItem.Exclusao,
            ValorTotal = 60m,
            Ressarcimentos = new List<RessarcimentoItemVersao>
            {
                new RessarcimentoItemVersao { Ressarcimento = new Ressarcimento { Valor = 10m } }
            }
        };
        var exclusao2 = new ItemVersao
        {
            Id = 2,
            TipoMovimento = TipoMovimentoItem.Exclusao,
            ValorTotal = 40m,
            Ressarcimentos = new List<RessarcimentoItemVersao>
            {
                new RessarcimentoItemVersao { Ressarcimento = new Ressarcimento { Valor = 5m } }
            }
        };
        var autorizacao = new ItemVersao
        {
            Id = 3,
            TipoMovimento = TipoMovimentoItem.Autorizacao,
            ValorTotal = 100m
        };

        var itens = new List<ItemVersao> { exclusao1, exclusao2, autorizacao };

        var result = MovimentacoesService.CalcularValorARessarcir(itens);

        Assert.Equal(85m, result); // (60+40)-(10+5)
    }

    [Fact]
    public void CalcularValorRessarcido_DeveSomarTodosRessarcimentos()
    {
        var exclusao = new ItemVersao
        {
            Id = 1,
            TipoMovimento = TipoMovimentoItem.Exclusao,
            ValorTotal = 80m,
            Ressarcimentos = new List<RessarcimentoItemVersao>
            {
                new RessarcimentoItemVersao { Ressarcimento = new Ressarcimento { Valor = 30m } },
                new RessarcimentoItemVersao { Ressarcimento = new Ressarcimento { Valor = 10m } }
            }
        };
        var autorizacao = new ItemVersao
        {
            Id = 2,
            TipoMovimento = TipoMovimentoItem.Autorizacao,
            ValorTotal = 100m,
            Ressarcimentos = new List<RessarcimentoItemVersao>
            {
                new RessarcimentoItemVersao { Ressarcimento = new Ressarcimento { Valor = 5m } }
            }
        };

        var itens = new List<ItemVersao> { exclusao, autorizacao };

        var result = MovimentacoesService.CalcularValorRessarcido(itens);

        Assert.Equal(45m, result);
    }
}
}

