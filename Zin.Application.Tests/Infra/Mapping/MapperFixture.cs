using AutoMapper;
using Zin.Application.Mappings;

namespace Zin.Tests.Infrastructure.Mapping
{
    public class MapperFixture
    {
        public IMapper Mapper { get; }

        public MapperFixture()
        {
            var cfg = new MapperConfiguration(c =>
            {
                c.AddProfile<ImportarNotaFiscalDetalhadoProfile>();
            });

            cfg.AssertConfigurationIsValid();
            Mapper = cfg.CreateMapper();
        }
    }
}



