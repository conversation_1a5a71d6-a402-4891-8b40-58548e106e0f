using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers;
using Zin.Application.Tests.Helper;
using Zin.Application.Tests.Helper.Builder.Entidade;
using Zin.Application.Tests.Helper.Builder.Importar;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.ValueObject;

namespace Zin.Application.Tests.Itens.Specification
{
    public class ItemVersaoSpecificationHelperTests
    {
        [Fact(DisplayName = "ObterTodasVersoesDoFornecedorEAutorizacao: retorna vazio quando lista é null")]
        public void ObterTodas_RetornaVazio_QuandoListaNull()
        {
            var cnpjFornecedor = Cnpj.Criar("58.575.103/0001-60");
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);

            var resultado = ItemVersaoSpecificationHelper.ObterTodasVersoesDoFornecedorEAutorizacao(null, autorizacao, cnpjFornecedor);

            Assert.NotNull(resultado);
            Assert.Empty(resultado);
        }

        [Fact(DisplayName = "ObterTodasVersoesDoFornecedorEAutorizacao: filtra por mesmo fornecedor + mesma data")]
        public void ObterTodas_FiltraPorFornecedorEData()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);
            var fornecedor1 = new PessoaJuridicaBuilder().Build();
            var fornecedor2 = new PessoaJuridicaBuilder().ComCnpj("26.152.089/0001-03").Build();

            var vMatch1 = new ItemVersaoBuilder()
                                                .ComCodigo("COD-1")
                                                .ComDescricao("DESC-1")
                                                .ComFornecedor(fornecedor1)
                                                .ComAutorizacao(autorizacao)
                                                .Build();

            var vMatch2 = new ItemVersaoBuilder()
                                                .ComCodigo("COD-1")
                                                .ComDescricao("DESC-1")
                                                .ComFornecedor(fornecedor1)
                                                .ComAutorizacao(autorizacao)
                                                .Build();

            var vOutroFornecedor = new ItemVersaoBuilder()
                                                .ComCodigo("COD-1")
                                                .ComDescricao("DESC-1")
                                                .ComFornecedor(fornecedor2)
                                                .ComAutorizacao(autorizacao)
                                                .Build();

            var vOutraData = new ItemVersaoBuilder()
                                                .ComCodigo("COD-1")
                                                .ComDescricao("DESC-1")
                                                .ComFornecedor(fornecedor1)
                                                .ComAutorizacao(autorizacao.AddDays(1))
                                                .Build();

            var todas = new List<ItemVersao> { vMatch1, vMatch2, vOutroFornecedor, vOutraData };

            var resultado = ItemVersaoSpecificationHelper
                .ObterTodasVersoesDoFornecedorEAutorizacao(todas, autorizacao, Cnpj.Criar(fornecedor1.Cnpj))
                .ToList();

            Assert.Equal(2, resultado.Count);
            Assert.Contains(vMatch1, resultado);
            Assert.Contains(vMatch2, resultado);
        }

        [Fact(DisplayName = "ObterUltimaVersaoMesmaAutorizacao: retorna a versão com maior DataCriacao (entre as que casam)")]
        public void ObterUltima_RetornaMaisRecente()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);
            var fornecedor = new PessoaJuridicaBuilder().Build();

            var v1 = new ItemVersaoBuilder()
                .ComCodigo("COD-1")
                .ComDescricao("DESC-1")
                .ComFornecedor(fornecedor)
                .ComAutorizacao(autorizacao)
                .Build();

            var v2 = new ItemVersaoBuilder()
                .ComCodigo("COD-1")
                .ComDescricao("DESC-1")
                .ComFornecedor(fornecedor)
                .ComAutorizacao(autorizacao)
                .Build();

            var vOutraData = new ItemVersaoBuilder()
                .ComCodigo("COD-1")
                .ComDescricao("DESC-1")
                .ComFornecedor(fornecedor)
                .ComAutorizacao(autorizacao.AddDays(1))
                .Build();

            var todas = new List<ItemVersao> { v1, v2, vOutraData };

            var res = ItemVersaoSpecificationHelper.ObterUltimaVersaoMesmaAutorizacao(todas, autorizacao, Cnpj.Criar(fornecedor.Cnpj));

            Assert.Same(v2, res);
        }

        [Fact(DisplayName = "ObterUltimaVersaoMesmaAutorizacao: retorna null quando não há correspondência")]
        public void ObterUltima_RetornaNull_QuandoSemMatch()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);
            var fornecedor = new PessoaJuridicaBuilder().Build();
            var vOutraData = new ItemVersaoBuilder()
                .ComCodigo("COD-1")
                .ComDescricao("DESC-1")
                .ComFornecedor(fornecedor)
                .ComAutorizacao(autorizacao.AddDays(1))
                .Build();

            var resultado = ItemVersaoSpecificationHelper.ObterUltimaVersaoMesmaAutorizacao(
                new List<ItemVersao> { vOutraData },
                autorizacao,
                Cnpj.Criar("58.575.103/0001-60")
            );

            Assert.Null(resultado);
        }

        [Fact(DisplayName = "ObterUltimaVersaoMesmaAutorizacao: documenta gap de igualdade estrita de data (jitter sub-segundo)")]
        public void ObterUltima_DocumentaGap_IgualdadeDataEstrita()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);
            var fornecedor = new PessoaJuridicaBuilder().Build();

            var v = new ItemVersaoBuilder()
                .ComCodigo("COD-1")
                .ComDescricao("DESC-1")
                .ComFornecedor(fornecedor)
                .ComAutorizacao(autorizacao)
                .Build();

            var resultado = ItemVersaoSpecificationHelper.ObterUltimaVersaoMesmaAutorizacao(
                new List<ItemVersao> { v },
                autorizacao.AddMilliseconds(500),
                Cnpj.Criar(fornecedor.Cnpj)
            );

            Assert.Null(resultado);
        }

        [Fact(DisplayName = "GarantirIntegridadeDasVersoes: lança quando Código das versões diverge do DTO")]
        public void Validar_Lanca_QuandoCodigoDiverge()
        {
            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var dto = new ImportarItemDTOBuilder()
                .ComCodigo("COD-DTO")
                .ComDescricao("DESC-DTO")
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var versao = new ItemVersaoBuilder()
                .ComCodigo("OUTRO")
                .ComDescricao(dto.Descricao!)
                .ComFornecedor(fornecedor)
                .Build();

            var versoes = new List<ItemVersao> { versao };  

            var ex = Assert.Throws<ArgumentException>(() => ItemVersaoSpecificationHelper.GarantirIntegridadeDasVersoes(versoes, dto));
            Assert.Contains("não pertencem ao mesmo item", ex.Message, StringComparison.InvariantCultureIgnoreCase);
        }

        [Fact(DisplayName = "GarantirIntegridadeDasVersoes lança quando Descrição das versões diverge do DTO")]
        public void Validar_Lanca_QuandoDescricaoDiverge()
        {
            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var dto = new ImportarItemDTOBuilder()
                .ComCodigo("COD-DTO")
                .ComDescricao("DESC-DTO")
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var versao = new ItemVersaoBuilder()
                .ComCodigo(dto.Codigo)
                .ComDescricao("OUTRO")
                .ComFornecedor(fornecedor)
                .Build();

            var versoes = new List<ItemVersao> { versao };

            var ex = Assert.Throws<ArgumentException>(() => ItemVersaoSpecificationHelper.GarantirIntegridadeDasVersoes(versoes, dto));
            Assert.Contains("não pertencem ao mesmo item", ex.Message, StringComparison.InvariantCultureIgnoreCase);
        }

        [Fact(DisplayName = "GarantirIntegridadeDasVersoes não lança quando tudo está coerente")]
        public void Validar_NaoLanca_QuandoCoerente()
        {
            var autorizacao = DateTime.Now;

            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var dto = new ImportarItemDTOBuilder()
                .ComCodigo("COD-DTO")
                .ComDescricao("DESC-DTO")
                .ComAutorizacao(autorizacao)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var versao1 = new ItemVersaoBuilder()
                .ComCodigo(dto.Codigo)
                .ComDescricao(dto.Descricao)
                .ComAutorizacao(dto.DataAutorizacao)
                .ComFornecedor(fornecedor)
                .Build();

            var versao2 = new ItemVersaoBuilder()
                .ComCodigo(dto.Codigo)
                .ComDescricao(dto.Descricao)
                .ComAutorizacao(dto.DataAutorizacao)
                .ComFornecedor(fornecedor)
                .Build();

            var versoes = new List<ItemVersao> { versao1, versao2 };

            var ex = Record.Exception(() => ItemVersaoSpecificationHelper.GarantirIntegridadeDasVersoes(versoes, dto));
            Assert.Null(ex);
        }


        [Fact]
        public void ObterTodasVersoesMesmoFornecedorEAutorizacao_DeveEncontrar_QuandoMesmoInstanteMesmaPessoa()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 30, 40, DateTimeKind.Local);
            var fornecedor = new PessoaJuridicaBuilder().Build();

            var versoes = new List<ItemVersao>
                {
                    new ItemVersaoBuilder()
                        .ComAutorizacao(autorizacao)
                        .ComFornecedor(fornecedor)
                        .Build(),
                    new ItemVersaoBuilder()
                        .ComAutorizacao(autorizacao)
                        .ComFornecedor(fornecedor)
                        .Build(),
                    new ItemVersaoBuilder()
                        .ComAutorizacao(autorizacao)
                        .ComFornecedor(fornecedor)
                        .Build(),
                };

            var cnpjFornecedor = Cnpj.Criar("58.575.103/0001-60");
            var result = ItemVersaoSpecificationHelper.ObterTodasVersoesDoFornecedorEAutorizacao(versoes, autorizacao, cnpjFornecedor);

            Assert.Contains(result, v => v.PessoaFornecedora is PessoaJuridica pj && pj.Cnpj.Equals(cnpjFornecedor));
        }

        [Fact]
        public void ObterTodasVersoesMesmoFornecedorEAutorizacao_NaoDeveEncontrar_QuandoFornecedorDiferente()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 30, 40, DateTimeKind.Utc);

            var f1 = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var f2 = new PessoaJuridicaBuilder().ComCnpj("67.361.338/0001-40").Build();

            var versoes = new List<ItemVersao>
            {
                new ItemVersaoBuilder()
                    .ComAutorizacao(autorizacao)
                    .ComFornecedor(f1)
                    .Build()
            };

            var cnpjFornecedor2 = Cnpj.Criar(f2.Cnpj);
            var result = ItemVersaoSpecificationHelper.ObterTodasVersoesDoFornecedorEAutorizacao(versoes, autorizacao, cnpjFornecedor2);
            Assert.Empty(result);
        }

        [Fact]
        public void ObterUltimaVersaoMesmaAutorizacao_DeveRetornarMaisRecentePorDataCriacao()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 30, 40, DateTimeKind.Utc);
            var fornecedor = new PessoaJuridicaBuilder().Build();

            var versoes = new List<ItemVersao>() 
            {
                new ItemVersaoBuilder()
                    .ComAutorizacao(autorizacao) 
                    .ComFornecedor(fornecedor)           
                    .Build(),

                new ItemVersaoBuilder()
                    .ComAutorizacao(autorizacao)
                    .ComFornecedor(fornecedor)
                    .Build(),

                new ItemVersaoBuilder()
                    .ComAutorizacao(autorizacao)
                    .ComFornecedor(fornecedor)
                    .Build()
            };

            var cnpjFornecedor = Cnpj.Criar(fornecedor.Cnpj);
            var result = ItemVersaoSpecificationHelper.ObterUltimaVersaoMesmaAutorizacao(versoes, autorizacao, cnpjFornecedor);
            Assert.Equal(versoes[2], result);
        }
    }
}
