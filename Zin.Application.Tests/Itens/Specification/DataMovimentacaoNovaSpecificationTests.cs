using Zin.Application.Specifications.ItensVersoes;
using Zin.Application.Tests.Helper;
using Zin.Application.Tests.Helper.Builder.Entidade;
using Zin.Application.Tests.Helper.Builder.Importar;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Tests.Itens.Specification
{
    public class DataMovimentacaoNovaSpecificationTests
    {
        private readonly DataMovimentacaoNovaSpecification _spec = new();

        [Fact(DisplayName = "Deve criar nova versão quando mesma autorização e movimentação diferente")]
        public void DeveCriarNovaVersao_QuandoMesmaAutorizacao_EMovimentacaoDiferente()
        {
            var autorizacao = new DateTime(2025, 09, 01, 10, 00, 00, DateTimeKind.Utc);
            var movimentacaoAnterior = new DateTime(2025, 09, 02, 12, 00, 00, DateTimeKind.Utc);
            var movimentacaoNova = new DateTime(2025, 09, 03, 12, 00, 00, DateTimeKind.Utc);

            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var ultimaVersao = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao)
                .ComMovimentacao(movimentacaoAnterior)
                .ComFornecedor(fornecedor)
                .Build();

            var dto = new ImportarItemDTOBuilder()
                .ComCodigo(ultimaVersao.Item.Codigo)
                .ComDescricao(ultimaVersao.Item.Descricao)
                .ComAutorizacao(autorizacao)
                .ComMovimento(movimentacaoNova)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var resultado = _spec.IsSatisfiedBy(new List<ItemVersao> { ultimaVersao }, dto);

            Assert.True(resultado.DeveCriarNovaVersao);
            Assert.Equal(ultimaVersao, resultado.VersaoAnterior);
        }


        [Fact(DisplayName = "Não deve criar nova versão quando mesma autorização e movimentação igual")]
        public void NaoDeveCriarNovaVersao_QuandoMesmaAutorizacao_EMovimentacaoIgual()
        {
            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var autorizacao = new DateTime(2025, 09, 01, 10, 00, 00, DateTimeKind.Utc);
            var movimentacao = new DateTime(2025, 09, 02, 12, 00, 00, DateTimeKind.Utc);

            var ultimaVersao = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao)
                .ComMovimentacao(movimentacao)
                .ComFornecedor(fornecedor)
                .Build();

            var dto = new ImportarItemDTOBuilder()
                .ComCodigo(ultimaVersao.Item.Codigo)
                .ComDescricao(ultimaVersao.Item.Descricao)
                .ComAutorizacao(autorizacao)
                .ComMovimento(movimentacao)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var resultado = _spec.IsSatisfiedBy(new List<ItemVersao> { ultimaVersao }, dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }

        [Fact]
        public void DeveRetornarFalse_QuandoDtoForNull()
        {
            Assert.Throws<ArgumentNullException>(() => _spec.IsSatisfiedBy(new List<ItemVersao>(), null));
        }

        [Fact(DisplayName = "Não cria quando não existe versão com MESMA autorização para o fornecedor")]
        public void NaoCria_QuandoSemVersaoMesmaAutorizacao()
        {
            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var autorizacao = new DateTime(2025, 09, 01, 10, 00, 00, DateTimeKind.Utc);
            var movimentacao = new DateTime(2025, 09, 02, 12, 00, 00, DateTimeKind.Utc);

            var versao = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao.AddDays(-1))
                .ComMovimentacao(movimentacao)
                .ComFornecedor(fornecedor)
                .Build();

            var versoes = new List<ItemVersao> { versao };

            var dto = new ImportarItemDTOBuilder()
                .ComCodigo(versao.Item.Codigo)
                .ComDescricao(versao.Item.Descricao)
                .ComAutorizacao(autorizacao)
                .ComMovimento(movimentacao)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var resultado = _spec.IsSatisfiedBy(versoes, dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }

        [Fact(DisplayName = "Não cria quando DataMovimento do DTO é default")]
        public void NaoCria_QuandoDataMovimentoDefault()
        {
            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var autorizacao = new DateTime(2025, 09, 01, 10, 00, 00, DateTimeKind.Utc);
            var movimentacao = default(DateTime);

            var dto = new ImportarItemDTOBuilder()
   
                .ComAutorizacao(autorizacao)
                .ComMovimento(movimentacao)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var resultado = _spec.IsSatisfiedBy(new List<ItemVersao>(), dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }

        [Fact]
        public void DeveRetornarFalse_QuandoNaoExisteUltimaVersaoMesmaAutorizacao()
        {
            var dto = new ImportarItemDTOBuilder()
                .ComAutorizacao(DateTime.Now)
                .ComMovimento(DateTime.Now)
                .ComCnpjFornecedor("58.575.103/0001-60")
                .Build();

            var resultado = _spec.IsSatisfiedBy(new List<ItemVersao>(), dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }

        [Fact]
        public void DeveRetornarFalse_QuandoDataAutorizacaoNaoCoincide()
        {
            var autorizacao = new DateTime(2025, 09, 01, 10, 00, 00, DateTimeKind.Utc);
            var movimentacaoAnterior = new DateTime(2025, 09, 02, 12, 00, 00, DateTimeKind.Utc);
            var movimentacaoNova = new DateTime(2025, 09, 03, 12, 00, 00, DateTimeKind.Utc);

            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var ultimaVersao = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao)
                .ComMovimentacao(movimentacaoAnterior)
                .ComFornecedor(fornecedor)
                .Build();

            var dto = new ImportarItemDTOBuilder()
                .ComCodigo(ultimaVersao.Item.Codigo)
                .ComDescricao(ultimaVersao.Item.Descricao)
                .ComAutorizacao(autorizacao.AddDays(-1))
                .ComMovimento(movimentacaoNova)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var resultado = _spec.IsSatisfiedBy(new List<ItemVersao> { ultimaVersao }, dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }
    }
}
