using Zin.Application.Specifications.ItensVersoes;
using Zin.Application.Tests.Helper.Builder.Entidade;
using Zin.Application.Tests.Helper.Builder.Importar;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Itens.Specification
{
    public class AutorizacaoItemSemVersoesSpecificationTests
    {
        private readonly AutorizacaoItemSemVersoesSpecification _spec = new  ();


        [Fact(DisplayName = "Não cria nova versão quando for diferente de autorização")]
        public void NaoCria_QuandoTipoNaoEhAutorizacao()
        {
            var codigo = Guid.NewGuid().ToString();
            var descricao = Guid.NewGuid().ToString();
            var fornecedor = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();

            var versao = new ItemVersaoBuilder()
                .ComCodigo(codigo)
                .ComDescricao(descricao)
                .ComFornecedor(fornecedor)
                .ComQuantidade(5)
                .Build();

            var dto = new ImportarItemDTOBuilder()
                .ComCodigo(codigo)
                .ComDescricao(descricao)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .ComTipoMovimento(TipoMovimentoItem.Exclusao)
                .Build();

            var versoes = new List<ItemVersao> { versao };
            var resultado = _spec.IsSatisfiedBy(versoes, dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }

        [Fact(DisplayName = "Retorna TRUE quando não existem versões e o tipo é Autorizacao")]
        public void Cria_QuandoSemVersoes_ETipoAutorizacao()
        {
            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                .ComAutorizacao(new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc))
                .Build();

            var resultado1 = _spec.IsSatisfiedBy(new List<ItemVersao>(), dto);

            Assert.True(resultado1.DeveCriarNovaVersao);
            Assert.Null(resultado1.VersaoAnterior);

            var resultado2 = _spec.IsSatisfiedBy(null, dto);

            Assert.True(resultado2.DeveCriarNovaVersao);
            Assert.Null(resultado2.VersaoAnterior);
        }

        [Fact(DisplayName = "Retorna TRUE quando não há versão com MESMO fornecedor E MESMA data")]
        public void Cria_QuandoNaoHaVersaoComMesmoFornecedorEData()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);
            var fornecedor = new PessoaJuridicaBuilder().ComId(1).ComCnpj("58.575.103/0001-60").Build();

            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                .ComAutorizacao(autorizacao)
                .ComCodigo("123")
                .ComDescricao("123")
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var versaoExistente = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao.AddDays(-1))
                .ComCodigo(dto.Codigo)
                .ComDescricao(dto.Descricao)
                .ComFornecedor(fornecedor)
                .Build();

            var todas = new List<ItemVersao> { versaoExistente };

            var res = _spec.IsSatisfiedBy(todas, dto);

            Assert.True(res.DeveCriarNovaVersao);
            Assert.Null(res.VersaoAnterior);
        }

        [Fact(DisplayName = "Retorna TRUE quando fornecedor é diferente (mesma data)")]
        public void Cria_QuandoFornecedorDiferente_MesmaData()
        {
            // Arrange
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);

            var fornecedorA = new PessoaJuridicaBuilder().ComId(1).ComCnpj("58.575.103/0001-60").Build();
            var fornecedorB = new PessoaJuridicaBuilder().ComId(2).ComCnpj("79.214.432/0001-55").Build();

            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                .ComAutorizacao(autorizacao)
                .ComCnpjFornecedor(fornecedorA.Cnpj) // fornecedor A
                .Build();

            var versaoDeOutroFornecedorMesmaData = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao)
                .ComCodigo(dto.Codigo)
                .ComDescricao(dto.Descricao)
                .ComFornecedor(fornecedorB) // diferente do DTO
                .Build();

            var versoes = new List<ItemVersao> { versaoDeOutroFornecedorMesmaData };

            var res = _spec.IsSatisfiedBy(versoes, dto);

            Assert.True(res.DeveCriarNovaVersao);
            Assert.Null(res.VersaoAnterior);
        }

        [Fact(DisplayName = "Retorna FALSE quando já existe versão com MESMO fornecedor E MESMA data")]
        public void NaoCria_QuandoJaExisteVersaoComMesmoFornecedorEData()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);
            var cnpj = "58.575.103/0001-60";
            var fornecedor = new PessoaJuridicaBuilder().ComId(1).ComCnpj(cnpj).Build();

            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                .ComAutorizacao(autorizacao)
                .ComCnpjFornecedor(cnpj)
                .Build();

            var versaoComMesmoFornecedorEData = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao)
                .ComFornecedor(fornecedor)
                .ComCodigo(dto.Codigo)
                .ComDescricao(dto.Descricao)
                .Build();

            var versoes = new List<ItemVersao> { versaoComMesmoFornecedorEData };

            var res = _spec.IsSatisfiedBy(versoes, dto);

            Assert.False(res.DeveCriarNovaVersao);
            Assert.Null(res.VersaoAnterior);
        }
    }
}
