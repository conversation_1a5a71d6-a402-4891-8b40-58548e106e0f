using Zin.Application.Specifications.ItensVersoes;
using Zin.Application.Tests.Helper.Builder.Entidade;
using Zin.Application.Tests.Helper.Builder.Importar;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Itens.Specification
{
    public class MesmaDataAutorizacaoQuantidadeDiferenteSpecificationTests
    {
        private readonly MesmaDataAutorizacaoQuantidadeDiferenteSpecification _spec = new MesmaDataAutorizacaoQuantidadeDiferenteSpecification();

        [Fact(DisplayName = "Não cria nova versão quando for diferente de autorização")]
        public void NaoCria_QuandoTipoNaoEhAutorizacao()
        {
            var fornecedor = new PessoaJuridicaBuilder().Build();
            var autorizacao = new DateTime(2025, 09, 01, 10, 00, 00, DateTimeKind.Utc);

            var versao = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao)
                .ComQuantidade(5)
                .ComFornecedor(fornecedor)
                .Build();

            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Exclusao)
                .ComAutorizacao(autorizacao)
                .ComCodigo(versao.Item.Codigo)
                .ComDescricao(versao.Item.Descricao)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .ComQuantidade(10)
                .Build();

            var todasVersoes = new List<ItemVersao>() { versao };

            var resultado = _spec.IsSatisfiedBy(todasVersoes, dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }

        [Fact(DisplayName = "Não cria quando não existe versão anterior compatível")]
        public void NaoCria_QuandoSemVersaoAnterior()
        {
            var fornecedor = new PessoaJuridicaBuilder().Build();

            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                .ComAutorizacao(DateTime.Now)
                .ComQuantidade(10)
                .Build();

            var versao = new ItemVersaoBuilder()
                .ComAutorizacao(DateTime.Now.AddDays(-1))
                .ComCodigo(dto.Codigo)
                .ComDescricao(dto.Descricao)
                .ComQuantidade(5)
                .ComFornecedor(fornecedor)
                .Build();

            var todasVersoes = new List<ItemVersao>() { versao };

            var resultado = _spec.IsSatisfiedBy(todasVersoes, dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }

        [Fact(DisplayName = "Cria quando Autorização é a mesma e Quantidade mudou")]
        public void Cria_QuandoAutorizacaoIgual_EQuantidadeDiferente()
        {
            var codigo = Guid.NewGuid().ToString();
            var descricao = Guid.NewGuid().ToString();
            var autorizacao = new DateTime(2025, 09, 01, 10, 00, 00, DateTimeKind.Utc);
            var fornecedor = new PessoaJuridicaBuilder().Build();

            var versao = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao)
                .ComCodigo(codigo)
                .ComDescricao(descricao)
                .ComQuantidade(5)
                .ComFornecedor(fornecedor)
                .Build();

            var versoes = new List<ItemVersao> { versao };

            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                .ComCodigo(codigo)
                .ComDescricao(descricao)
                .ComAutorizacao(autorizacao)
                .ComQuantidade(10)
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var resultado = _spec.IsSatisfiedBy(versoes, dto);

            Assert.True(resultado.DeveCriarNovaVersao);
            Assert.NotNull(resultado.VersaoAnterior);
            Assert.Equal(versao, resultado.VersaoAnterior);
        }
    }
}
