using Zin.Application.Specifications.ItensVersoes;
using Zin.Application.Tests.Helper.Builder.Entidade;
using Zin.Application.Tests.Helper.Builder.Importar;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Itens.Specification
{
    public class ExclusaoComAutorizacaoSpecificationTests
    {
        private ExclusaoComAutorizacaoSpecification _spec = new ExclusaoComAutorizacaoSpecification();

        [Fact]
        public void RetornaFalse_QuandoTipoMovimentoNaoEhExclusao()
        {
            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                .Build();

            var todasVersoes = new List<ItemVersao>();

            var resultado = _spec.IsSatisfiedBy(todasVersoes, dto);

            Assert.False(resultado.DeveCriarNovaVersao);
            Assert.Null(resultado.VersaoAnterior);
        }

        [Fact]
        public void LancaExcecao_QuandoNaoHaVersaoAutorizacao()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);
            var fornecedor = new PessoaJuridicaBuilder().ComId(1).ComCnpj("58.575.103/0001-60").Build();

            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Exclusao)
                .ComAutorizacao(autorizacao)
                .ComCodigo("123")
                .ComDescricao("123")
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var versao = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao.AddDays(-1))
                .ComCodigo(dto.Codigo)
                .ComTipoMovimento(TipoMovimentoItem.Outro)
                .ComDescricao(dto.Descricao)
                .ComFornecedor(fornecedor)
                .Build();

            var versoes = new List<ItemVersao> { versao };

            Assert.Throws<InvalidOperationException>(() => _spec.IsSatisfiedBy(versoes, dto));
        }



        [Fact]
        public void RetornaTrue_QuandoExisteVersaoAutorizacao()
        {
            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);
            var fornecedor = new PessoaJuridicaBuilder().ComId(1).ComCnpj("58.575.103/0001-60").Build();

            var dto = new ImportarItemDTOBuilder()
                .ComTipoMovimento(TipoMovimentoItem.Exclusao)
                .ComAutorizacao(autorizacao)
                .ComCodigo("123")
                .ComDescricao("123")
                .ComCnpjFornecedor(fornecedor.Cnpj)
                .Build();

            var versao = new ItemVersaoBuilder()
                .ComAutorizacao(autorizacao)
                .ComCodigo(dto.Codigo)
                .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                .ComDescricao(dto.Descricao)
                .ComFornecedor(fornecedor)
                .Build();

            var versoes = new List<ItemVersao> { versao };

            var resultado = _spec.IsSatisfiedBy(versoes, dto);

            // Assert
            Assert.True(resultado.DeveCriarNovaVersao);
            Assert.Same(versao, resultado.VersaoAnterior);
        }
    }
}