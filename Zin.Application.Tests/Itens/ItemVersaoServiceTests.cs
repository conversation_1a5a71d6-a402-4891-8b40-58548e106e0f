using AutoMapper;
using Moq;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.ZinPag;
using Zin.Application.Tests.Helper.Builder.Entidade;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Tests.Itens
{
    public class ItemVersaoServiceTests
    {
        private readonly Mock<IItemVersaoRepository> _repoMock = new();
        private readonly Mock<IMapper> _mapperMock = new();

        private ItemVersaoService CriarService()
        {
            var service = new ItemVersaoService(_repoMock.Object, _mapperMock.Object);
            return service;
        }

        [Fact]
        public void DeveCriarNovaVersao_ItemSemVersoes_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = new PessoaJuridicaBuilder().Build();
            var todasVersoes = new List<ItemVersao>();
            var dto = new ImportarItemDTO() { TipoMovimentoItem = TipoMovimentoItem.Autorizacao };

            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_ExclusaoComAutorizacao_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = new PessoaJuridicaBuilder().Build();

            var todasVersoes = new List<ItemVersao>
            {
                new ItemVersaoBuilder()
                    .ComAutorizacao(DateTime.Today.AddDays(-1))
                    .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                    .ComFornecedor(fornecedor)
                    .Build()
            };

            var dto = new ImportarItemDTO { DataAutorizacao = DateTime.Today, TipoMovimentoItem = TipoMovimentoItem.Exclusao, CnpjFornecedor = fornecedor.Cnpj };

            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_ExclusaoSemAutorizacao_RetornaExcecao()
        {
            var service = CriarService();
            var todasVersoes = new List<ItemVersao>();
            var dto = new ImportarItemDTO { TipoMovimentoItem = TipoMovimentoItem.Exclusao };

            Assert.Throws<InvalidOperationException>(() => service.DeveCriarNovaVersao(todasVersoes, dto));
        }

        [Fact]
        public void DeveCriarNovaVersao_DataAutorizacaoNovaMesmoFornecedor_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = new PessoaJuridicaBuilder().Build();

            var todasVersoes = new List<ItemVersao>
            {
                new ItemVersaoBuilder()
                .ComAutorizacao(DateTime.Today.AddDays(-1))
                    .ComMovimentacao(DateTime.Today.AddDays(-1))
                    .ComFornecedor(fornecedor)
                    .Build()
            };


            var dto = new ImportarItemDTO { TipoMovimentoItem = TipoMovimentoItem.Autorizacao, DataAutorizacao = DateTime.Today, CnpjFornecedor = fornecedor.Cnpj };

            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_DataAutorizacaoIgualMesmoFornecedor_RetornaFalse()
        {
            var service = CriarService();
            PessoaJuridica pessoaJuridica = new PessoaJuridicaBuilder().Build();
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();
            var todasVersoes = new List<ItemVersao>
            {
                new ItemVersaoBuilder()
                    .ComAutorizacao(DateTime.Today)
                    .ComVeiculo(veiculo)
                    .ComFornecedor(pessoaJuridica)
                    .ComOficina(pessoaJuridica)
                    .ComMovimentacao(DateTime.Today)
                    .Build()
            };

            var dto = new ImportarItemDTO { TipoMovimentoItem = TipoMovimentoItem.Autorizacao, DataAutorizacao = DateTime.Today, CnpjFornecedor = pessoaJuridica.Cnpj };

            Assert.False(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_DataMovimentacaoNovaDataAutorizacaoIgual_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica pessoaJuridica = new PessoaJuridicaBuilder().Build();
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();

            var todasVersoes = new List<ItemVersao>
            {
                new ItemVersaoBuilder()
                    .ComAutorizacao( DateTime.Today)
                    .ComFornecedor(pessoaJuridica)
                    .ComOficina(pessoaJuridica)
                    .ComVeiculo(veiculo)
                    .ComMovimentacao(DateTime.Today.AddDays(-1))
                    .Build()
            };

            var dto = new ImportarItemDTO { DataAutorizacao = DateTime.Today, DataMovimento = DateTime.Today, CnpjFornecedor = pessoaJuridica.Cnpj };

            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_DataMovimentacaoEDataAutorizacaoIgual_RetornaFalse()
        {
            var service = CriarService();
            PessoaJuridica pessoaJuridica = new PessoaJuridicaBuilder().Build();
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();

            var todasVersoes = new List<ItemVersao>
            {
                new ItemVersaoBuilder()
                    .ComAutorizacao(DateTime.Today)
                    .ComMovimentacao(DateTime.Today)
                    .ComFornecedor(pessoaJuridica)
                    .ComVeiculo(veiculo)
                    .ComOficina(pessoaJuridica)
                    .Build()
            };

            var dto = new ImportarItemDTO
            {
                DataAutorizacao = DateTime.Today,
                DataMovimento = DateTime.Today,
                CnpjFornecedor = pessoaJuridica.Cnpj
            };

            Assert.False(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_MesmaDataAutorizacaoQuantidadeDiferente_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica pessoaJuridica = new PessoaJuridicaBuilder().Build();
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();

            var versoes = new List<ItemVersao>
            {
                new ItemVersaoBuilder()
                    .ComAutorizacao(DateTime.Today)
                    .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                    .ComFornecedor(pessoaJuridica)
                    .ComOficina(pessoaJuridica)
                    .ComVeiculo(veiculo)
                    .ComQuantidade(5)
                    .Build()
            };

            var dto = new ImportarItemDTO { DataAutorizacao = DateTime.Today, Quantidade = 10, CnpjFornecedor = pessoaJuridica.Cnpj, TipoMovimentoItem = TipoMovimentoItem.Autorizacao };
            Assert.True(service.DeveCriarNovaVersao(versoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_MesmaDataAutorizacaoQuantidadeIgual_RetornaFalse()
        {
            var service = CriarService();
            PessoaJuridica pessoaJuridica = new PessoaJuridicaBuilder().Build();
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();

            var todasVersoes = new List<ItemVersao>
            {
                new ItemVersaoBuilder()
                    .ComAutorizacao(DateTime.Today)
                    .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                    .ComQuantidade(5)
                    .ComFornecedor(pessoaJuridica)
                    .ComOficina(pessoaJuridica)
                    .ComVeiculo(veiculo)
                    .Build()
            };

            var dto = new ImportarItemDTO { DataAutorizacao = DateTime.Today, Quantidade = 5, CnpjFornecedor = pessoaJuridica.Cnpj };
            Assert.False(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }
    }
}