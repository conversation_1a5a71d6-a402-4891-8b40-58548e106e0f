using AutoMapper;
using Moq;
using Zin.Application.DTOs.Veiculos;
using Zin.Application.Interfaces;
using Zin.Application.Services.Importacoes;
using Zin.Application.Services.ZinPag;
using Zin.Application.Tests.Helper.Builder.Entidade;
using Zin.Application.Tests.Helper.Builder.Importar;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Factories;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Domain.ValueObject;
using Zin.Tests.Infrastructure.Mapping;

namespace Zin.Application.Tests.Importacao.Service
{
    [Collection("Mapping")]
    public class ImportarItensServiceTests
    {
        private readonly IMapper _mapper;
        public ImportarItensServiceTests(MapperFixture fx) => _mapper = fx.Mapper;

        private readonly Mock<IImportarDocumentoService> _documentoService = new();
        private readonly Mock<IPessoaJuridicaRepository> _pessoaJuridicaRepo = new();
        private readonly Mock<IVeiculoRepository> _veiculoRepo = new();
        private readonly Mock<IDocumentoRepository> _documentoRepo = new();

        [Fact(DisplayName = "Cria item com o 1º por ordem de domínio (Autorização antes de Exclusão)")]
        public async Task CriaItem_ComPrimeiroPorOrdemDominioAsync()
        {
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();
            var agregador = new AgregadorBuilder().ComAtivos(new[] { veiculo }).Build();
            var pessoaJuridica = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var documento = new DocumentoBuilder().ComNumero("DOC").ComAgregador(agregador).Build();

            _pessoaJuridicaRepo
                 .Setup(r => r.BuscarPorCnpjAsync(It.IsAny<Cnpj>()))
                 .ReturnsAsync(pessoaJuridica);

            _veiculoRepo
                .Setup(r => r.BuscarVeiculoPorPlacaAsync(It.IsAny<Placa>()))
                .ReturnsAsync(veiculo);

            _documentoRepo
                .Setup(r => r.BuscarPorIdAgregadorENumero(It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync(documento);

            var notaFiscalDTO = new ImportarNotaFiscalItemDTOBuilder()
                .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                .ComNumero("DOC")
                .Build();

            var docDTO = new ImportarDocumentoDTOBuilder()
                .ComNumero("DOC")
                .Build();

            var autorizacao = new DateTime(2025, 9, 1, 10, 0, 0, DateTimeKind.Utc);

            var codigo = "COD-1";
            var descricao = "DESC-1";

            var dtos = new[]
            {
                new ImportarItemDTOBuilder()
                    .ComCodigo(codigo)
                    .ComDescricao(descricao)
                    .ComPlaca(veiculo.Placa)
                    .ComTipoMovimento(TipoMovimentoItem.Exclusao)
                    .ComAutorizacao(autorizacao)
                    .ComDocumentosRelacionados(new [] {notaFiscalDTO})
                    .Build(),

                new ImportarItemDTOBuilder()
                    .ComCodigo(codigo)
                    .ComDescricao(descricao)
                    .ComPlaca(veiculo.Placa)
                    .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                    .ComAutorizacao(autorizacao)
                    .ComDocumentosRelacionados(new [] {notaFiscalDTO})
                    .Build()
            };

            var dto = new ImportacaoAgregadorDTOBuilder()
                .ComItens(dtos)
                .ComDocumentos(new[] { docDTO })
                .Build();

            var _service = CriarService();

            await _service.AdicionarItensEVersoesNoAgregadorAsync
            (
                agregador,
                dto
            );

            var item = Assert.Single(agregador.Itens);
            Assert.Equal(2, item.Versoes.Count());
            Assert.Equal(TipoMovimentoItem.Autorizacao, item.Versoes.First().TipoMovimento);

            ValidarVersoes(item.Versoes, new[] { 1, 2, });
        }

        [Fact(DisplayName = "Não reprocessa o 1º DTO quando acabou de criar o item (sem efeito duplicado)")]
        public async Task NaoReprocessaPrimeiroDto_QuandoCriouItem()
        {
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();
            var agregador = new AgregadorBuilder().ComAtivos(new[] { veiculo }).Build();
            var pessoaJuridica = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var autorizacao = DateTime.Now;

            var notaFiscalDTO = new ImportarNotaFiscalItemDTOBuilder()
                .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                .ComNumero("DOC")
                .Build();

            var docDTO = new ImportarDocumentoDTOBuilder()
                .ComNumero("DOC")
                .Build();

            var documento = new DocumentoBuilder()
                .ComNumero("DOC")
                .ComAgregador(agregador)
                .Build();

            _pessoaJuridicaRepo
                 .Setup(r => r.BuscarPorCnpjAsync(It.IsAny<Cnpj>()))
                 .ReturnsAsync(pessoaJuridica);

            _veiculoRepo
                .Setup(r => r.BuscarVeiculoPorPlacaAsync(It.IsAny<Placa>()))
                .ReturnsAsync(veiculo);

            _documentoRepo
                .Setup(r => r.BuscarPorIdAgregadorENumero(It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync(documento);

            // apenas 1 DTO no grupo → cria item e não roda o loop de versões
            var codigo = "COD-2";
            var descricao = "DESC-2";
            var dtos = new[]
            {
                new ImportarItemDTOBuilder()
                    .ComCodigo(codigo)
                    .ComDescricao(descricao)
                    .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                    .ComQuantidade(1)
                    .ComPlaca(veiculo.Placa)
                    .ComDocumentosRelacionados(new [] {notaFiscalDTO})
                    .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                    .ComCnpjOficina(pessoaJuridica.Cnpj)
                    .ComAutorizacao(autorizacao)
                    .Build()
            };

            var dto = new ImportacaoAgregadorDTOBuilder()
                .ComItens(dtos)
                .Build();

            var _service = CriarService();
            await _service.AdicionarItensEVersoesNoAgregadorAsync
            (
                agregador,
                dto
            );

            var item = Assert.Single(agregador.Itens);
            Assert.Single(item.Versoes);

            ValidarVersoes(item.Versoes, new[] { 1 });
        }

        [Fact(DisplayName = "Processa o 1º DTO quando o item já existia (ex.: DataMovimentacao diferente)")]
        public async void ProcessaPrimeiroDto_QuandoItemJaExiste()
        {
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();
            var agregador = new AgregadorBuilder().ComAtivos(new[] { veiculo }).Build();
            var pessoaJuridica = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();

            var docDTO = new ImportarDocumentoDTOBuilder()
                .ComNumero("DOC")
                .Build();

            var documento = new DocumentoBuilder()
                .ComNumero("DOC")
                .ComAgregador(agregador)
                .Build();

            var notaFiscalDTO = new ImportarNotaFiscalItemDTOBuilder()
                .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                .ComNumero("DOC")
                .Build();


            _pessoaJuridicaRepo.Setup(r => r.BuscarPorCnpjAsync(It.IsAny<Cnpj>())).ReturnsAsync(pessoaJuridica);
            _veiculoRepo.Setup(r => r.BuscarVeiculoPorPlacaAsync(It.IsAny<Placa>())).ReturnsAsync(veiculo);
            _documentoRepo.Setup(r => r.BuscarPorIdAgregadorENumero(It.IsAny<int>(), It.IsAny<string>())).ReturnsAsync(documento);

            var codigo = "COD-3";
            var descricao = "DESC-3";
            var autorizacao = new DateTime(2025, 9, 3, 10, 0, 0, DateTimeKind.Utc);

            // Item já existente com versão (Autorização + movimento M1)
            var itemExistente = new ItemBuilder().ComCodigo(codigo).ComDescricao(descricao).ComQuantidade(1).Build();

            var versaoExistente = new ItemVersaoBuilder()
                                            .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                                            .ComFornecedor(pessoaJuridica)
                                            .ComOficina(pessoaJuridica)
                                            .ComAutorizacao(autorizacao)
                                            .ComVeiculo(veiculo)
                                            .ComItem(itemExistente)
                                            .ComMovimentacao(new DateTime(2025, 9, 3, 12, 0, 0, DateTimeKind.Utc))
                                            .Build();

            itemExistente.AdicionarVersao(versaoExistente);

            agregador.AdicionarItem(itemExistente);

            // DTO com MESMA autorização e movimento diferente → DataMovimentacaoNovaSpecification deve criar
            var dtos = new[]
            {
                new ImportarItemDTOBuilder()
                    .ComCodigo(codigo)
                    .ComDescricao(descricao)
                    .ComPlaca(veiculo.Placa)
                    .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                    .ComMovimento(new DateTime(2025, 9, 4, 12, 0, 0, DateTimeKind.Utc))
                    .ComQuantidade(1)
                    .ComDocumentosRelacionados(new [] { notaFiscalDTO })
                    .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                    .ComCnpjOficina(pessoaJuridica.Cnpj)
                    .ComAutorizacao(autorizacao)
                    .Build()
            };

            var dto = new ImportacaoAgregadorDTOBuilder()
                .ComItens(dtos)
                .Build();

            var _service = CriarService();

            await _service.AdicionarItensEVersoesNoAgregadorAsync
            (
                agregador,
                dto
            );

            var item = Assert.Single(agregador.Itens);
            Assert.Equal(2, item.Versoes.Count);
            var nova = item.Versoes.Last();
            Assert.NotNull(nova.VersaoAnterior);

            ValidarVersoes(itemExistente.Versoes, new int[] { 1, 2 });
        }

        [Fact(DisplayName = "Agrupa por (Código|Descrição) e não duplica item")]
        public async void AgrupaPorChave_NaoDuplicaItem()
        {
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();
            var agregador = new AgregadorBuilder().ComAtivos(new[] { veiculo }).Build();
            var pessoaJuridica = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var documento = new DocumentoBuilder().ComNumero("DOC").ComAgregador(agregador).Build();

            _pessoaJuridicaRepo
                 .Setup(r => r.BuscarPorCnpjAsync(It.IsAny<Cnpj>()))
                 .ReturnsAsync(pessoaJuridica);

            _veiculoRepo
                .Setup(r => r.BuscarVeiculoPorPlacaAsync(It.IsAny<Placa>()))
                .ReturnsAsync(veiculo);

            _documentoRepo
                .Setup(r => r.BuscarPorIdAgregadorENumero(It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync(documento);

            var notaFiscalDTO = new ImportarNotaFiscalItemDTOBuilder()
                .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                .ComNumero("DOC")
                .Build();

            var docDTO = new ImportarDocumentoDTOBuilder()
                .ComNumero("DOC")
                .Build();

            var codigo = "COD-4";
            var descricao = "DESC-4";

            var autorizacao = new DateTime(2025, 9, 4, 10, 0, 0, DateTimeKind.Utc);

            var dtos = new[]
            {
                new ImportarItemDTOBuilder()
                        .ComCodigo(codigo)
                        .ComDescricao(descricao)
                        .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                        .ComCnpjOficina(pessoaJuridica.Cnpj)
                        .ComPlaca(veiculo.Placa)
                        .ComAutorizacao(autorizacao)
                        .ComDocumentosRelacionados(new[] { notaFiscalDTO })
                        .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                        .Build(),

                new ImportarItemDTOBuilder()
                        .ComCodigo(codigo)
                        .ComDescricao(descricao)
                        .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                        .ComCnpjOficina(pessoaJuridica.Cnpj)
                        .ComPlaca(veiculo.Placa)
                        .ComAutorizacao(autorizacao)
                        .ComDocumentosRelacionados(new[] { notaFiscalDTO })
                        .ComTipoMovimento(TipoMovimentoItem.Exclusao)
                        .Build()
            };

            var dto = new ImportacaoAgregadorDTOBuilder()
                .ComItens(dtos)
                .Build();

            var _service = CriarService();
            await _service.AdicionarItensEVersoesNoAgregadorAsync
            (
                agregador,
                dto
            );

            Assert.Single(agregador.Itens);

            ValidarVersoes(agregador.Itens.First().Versoes, new[] { 1, 2 });
        }

        [Fact(DisplayName = "Lança quando Código/Descrição inválidos ao gerar chave de agrupamento")]
        public async void Lanca_QuandoChaveAgrupamentoInvalida()
        {
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();
            var agregador = new AgregadorBuilder().ComAtivos(new[] { veiculo }).Build();
            var pessoaJuridica = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();
            var documento = new DocumentoBuilder().ComNumero("DOC").ComAgregador(agregador).Build();

            _pessoaJuridicaRepo
                 .Setup(r => r.BuscarPorCnpjAsync(It.IsAny<Cnpj>()))
                 .ReturnsAsync(pessoaJuridica);

            _veiculoRepo
                .Setup(r => r.BuscarVeiculoPorPlacaAsync(It.IsAny<Placa>()))
                .ReturnsAsync(veiculo);

            _documentoRepo
                .Setup(r => r.BuscarPorIdAgregadorENumero(It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync(documento);


            var notaFiscalDTO = new ImportarNotaFiscalItemDTOBuilder()
                .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                .ComNumero("DOC")
                .Build();

            var docDTO = new ImportarDocumentoDTOBuilder()
                .ComNumero("DOC")
                .Build();

            var autorizacao = new DateTime(2025, 9, 5, 10, 0, 0, DateTimeKind.Utc);

            var dtos = new[]
            {
                new ImportarItemDTOBuilder()
                    .ComCodigo("  ") // inválido
                    .ComDescricao("DESC")
                    .ComPlaca(veiculo.Placa)
                    .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                    .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                    .ComDocumentosRelacionados(new [] { notaFiscalDTO })
                    .ComCnpjOficina(pessoaJuridica.Cnpj)
                    .ComAutorizacao(autorizacao)
                    .Build()
            };

            var dto = new ImportacaoAgregadorDTOBuilder()
                .ComItens(dtos)
                .ComDocumentos(new[] { docDTO })
                .Build();

            var _service = CriarService();
            await Assert.ThrowsAsync<ArgumentException>(async () => await _service.AdicionarItensEVersoesNoAgregadorAsync(
                agregador,
                dto
            ));
        }

        [Fact(DisplayName = "[MapearItensImportacao] Deve associar fornecedor e oficina")]
        public async void MapearItensImportacao_DeveAssociarFornecedorEOficina()
        {
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();
            var agregador = new AgregadorBuilder().ComAtivos(new[] { veiculo }).Build();
            var documento = new DocumentoBuilder().ComNumero("DOC").ComAgregador(agregador).Build();
            var pessoaJuridica = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();

            _pessoaJuridicaRepo
                 .Setup(r => r.BuscarPorCnpjAsync(It.IsAny<Cnpj>()))
                 .ReturnsAsync(pessoaJuridica);

            _veiculoRepo
                .Setup(r => r.BuscarVeiculoPorPlacaAsync(It.IsAny<Placa>()))
                .ReturnsAsync(veiculo);

            _documentoRepo
                .Setup(r => r.BuscarPorIdAgregadorENumero(It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync(documento);

            var notaFiscalDTO = new ImportarNotaFiscalItemDTOBuilder()
                .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                .ComNumero("DOC")
                .Build();

            var docDTO = new ImportarDocumentoDTOBuilder()
                .ComNumero("DOC")
                .Build();

            var itensDTO = new[]
            {
                new ImportarItemDTOBuilder()
                    .ComCodigo("COD1")
                    .ComDescricao("DESC1")
                    .ComPlaca(veiculo.Placa)
                    .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                    .ComCnpjOficina(pessoaJuridica.Cnpj)
                    .ComDocumentosRelacionados(new [] {notaFiscalDTO})
                    .Build()
            };

            var veiculoDTO = new ImportarAtivoVeiculoDTOBuilder()
                .ComPlaca(veiculo.Placa)
                .Build();

            var dto = new ImportacaoAgregadorDTOBuilder()
                            .ComItens(itensDTO)
                            .ComVeiculos(new[] { veiculoDTO })
                            .Build();

            var _service = CriarService();
            await _service.AdicionarItensEVersoesNoAgregadorAsync(
                 agregador,
                 dto
             );

            var documentoItemVersao = new DocumentoItemVersao()
            { 
                Documento = documento,
                ItemVersao = agregador.Itens.First().Versoes.First()
            };

            Assert.Single(agregador.Itens);
            Assert.NotNull(agregador.Itens.First().Versoes.First().PessoaFornecedora);
            Assert.NotNull(agregador.Itens.First().Versoes.First().Oficina);
            Assert.Equal("COD1", agregador.Itens.First().Codigo);
            Assert.Equal("DESC1", agregador.Itens.First().Descricao);
            Assert.Equal(documento, agregador.Itens.First().Versoes.First().Documentos.First().Documento);
        }

        [Fact]
        public async void Deve_Ignorar_ItemExclusao_Repetido()
        {
            var veiculo = new VeiculoBuilder().ComPlaca("DRP7B68").Build();            
            var agregador = new AgregadorBuilder().ComAtivos(new[] { veiculo }).Build();
            var documento = new DocumentoBuilder().ComNumero("DOC").ComAgregador(agregador).Build();
            var pessoaJuridica = new PessoaJuridicaBuilder().ComCnpj("58.575.103/0001-60").Build();

            _pessoaJuridicaRepo.Setup(r => r.BuscarPorCnpjAsync(It.IsAny<Cnpj>())).ReturnsAsync(pessoaJuridica);
            _veiculoRepo.Setup(r => r.BuscarVeiculoPorPlacaAsync(It.IsAny<Placa>())).ReturnsAsync(veiculo);
            _documentoRepo.Setup(r => r.BuscarPorIdAgregadorENumero(It.IsAny<int>(), It.IsAny<string>())).ReturnsAsync(documento);

            var autorizacao = DateTime.Now;

            var notaFiscalDTO = new ImportarNotaFiscalItemDTOBuilder()
                .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                .ComNumero("DOC")
                .Build();

            var docDTO = new ImportarDocumentoDTOBuilder()
                .ComNumero("DOC")
                .Build();

            var itensDTO = new[]
             {
                new ImportarItemDTOBuilder()
                    .ComCodigo("COD1")
                    .ComTipoMovimento(TipoMovimentoItem.Autorizacao)
                    .ComDescricao("DESC1")
                    .ComAutorizacao(autorizacao)
                    .ComPlaca(veiculo.Placa)
                    .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                    .ComCnpjOficina(pessoaJuridica.Cnpj)
                    .ComDocumentosRelacionados(new [] {notaFiscalDTO})
                    .Build(),
                new ImportarItemDTOBuilder()
                    .ComCodigo("COD1")
                    .ComTipoMovimento(TipoMovimentoItem.Exclusao)
                    .ComDescricao("DESC1")
                    .ComAutorizacao(autorizacao)
                    .ComPlaca(veiculo.Placa)
                    .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                    .ComCnpjOficina(pessoaJuridica.Cnpj)
                    .ComDocumentosRelacionados(new [] {notaFiscalDTO})
                    .Build(),
                new ImportarItemDTOBuilder()
                    .ComCodigo("COD1")
                    .ComTipoMovimento(TipoMovimentoItem.Exclusao)
                    .ComDescricao("DESC1")
                    .ComAutorizacao(autorizacao)
                    .ComPlaca(veiculo.Placa)
                    .ComCnpjFornecedor(pessoaJuridica.Cnpj)
                    .ComCnpjOficina(pessoaJuridica.Cnpj)
                    .ComDocumentosRelacionados(new [] {notaFiscalDTO})
                    .Build()
            };

            var veiculoDTO = new ImportarAtivoVeiculoDTOBuilder().ComPlaca(veiculo.Placa) .Build();

            var dtoAgregador = new ImportacaoAgregadorDTOBuilder()
                            .ComItens(itensDTO)
                            .ComVeiculos(new[] { veiculoDTO })
                            .Build();

            var _service = CriarService();
            await _service.AdicionarItensEVersoesNoAgregadorAsync(
                 agregador,
                 dtoAgregador
             );

            Assert.Single(agregador.Itens);
            Assert.NotNull(agregador.Itens.First().Versoes.First().PessoaFornecedora);
            Assert.NotNull(agregador.Itens.First().Versoes.First().Oficina);
            Assert.Equal("COD1", agregador.Itens.First().Codigo);
            Assert.Equal("DESC1", agregador.Itens.First().Descricao);
            Assert.Equal(documento, agregador.Itens.First().Versoes.First().Documentos.First().Documento);

            var versoes = agregador.Itens.SelectMany(i => i.Versoes).ToList();

            Assert.Equal(2, versoes.Count);
            Assert.True(versoes.Any(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao));
            Assert.True(versoes.Any(v => v.TipoMovimento == TipoMovimentoItem.Exclusao));
            ValidarVersoes(versoes, new[] { 1, 2 });
        }

        private void ValidarVersoes(ICollection<ItemVersao> versoes, int[] numerosEsperados)
        {
            var numeros = versoes.Select(v => v.NumeroVersao).ToArray();
            Array.Sort(numeros);
            Assert.Equal(numerosEsperados, numeros);
        }

        private ImportarItensService CriarService()
        {
            var repo = new Mock<IItemVersaoRepository>();
            var _factory = new ItemVersaoFactory();

            var _itemVersaoService = new ItemVersaoService(repo.Object, _mapper);
            return new ImportarItensService(
                _mapper,
                _pessoaJuridicaRepo.Object,
                _veiculoRepo.Object,
                _documentoRepo.Object,
                _itemVersaoService,
                _factory
            );
        }
    }
}

