using Zin.Application.DTOs.Importacao;
using Zin.Application.Tests.Helper.Builder.Importar;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Helper
{
    public static class TesteFactory
    {
        public static ImportacaoAgregadorDTO CriarImportacaValida(
            bool incluirAlgumaColecao = true)
        {
            var dto = new ImportacaoAgregadorDTO
            {
                TipoAgregador = TipoAgregador.Sinistro, // ajuste se necessário
                IdCliente = Guid.NewGuid().ToString(),
                Numero = "ABC-123",
                Oficinas = new List<ImportarPessoaJuridicaDTO>() 
                {
                    new ImportarPessoaJuridicaDTOBuilder().Build()
                },
                Fornecedores = new List<ImportarPessoaJuridicaDTO>() { CriarFornecedorValido() },
                Itens = new List<ImportarItemDTO> { CriarItemValido() }
            };

            if (incluirAlgumaColecao)
            {
                dto.Ativos = new ImportarAtivosDTO
                { 
                    Veiculos = new List<ImportarAtivoVeiculoDTO>
                    {
                        CriarVeiculoValido()
                    }
                };
            }

            return dto;
        }

        public static ImportarAtivoVeiculoDTO CriarVeiculoValido()
        {
            return new ImportarAtivoVeiculoDTO
            {
                Placa = "ABC1D23",
                Chassi = "9BWZZZ377VT004251",
                Modelo = "Fox 1.6",
                Marca = "VW",
                AnoModelo = 2017,
                CnpjOficina = "11222333000181" // CNPJ válido para regra do seu CNPJ validator
            };
        }

        public static ImportarItemDTO CriarItemValido()
        {
            var now = DateTime.UtcNow.Date;

            return new ImportarItemDTO
            {
                TipoMovimentoItem = TipoMovimentoItem.Autorizacao, // ajuste conforme enum
                TipoItem = TipoItemVersao.Venda,
                IdItemPedidoFornecedor = 1,
                IdFornecedor = 10,
                Codigo = "PECA-001",
                Descricao = "Pivô de suspensão",
                CnpjFornecedor = "91.318.816/0001-13",
                CnpjOficina = "91.318.816/0001-13",
                VeiculoPlaca = "ABC1D23",
                DataCriacao = now,
                DataAutorizacao = now,
                DataMovimento = now,
                DataEntrega = now.AddDays(1),
                Quantidade = 2,
                ValorUnitario = 50m,
                ValorTotal = 100m
            };
        }



        public static ImportarPessoaJuridicaDTO CriarFornecedorValido()
        {
            return new ImportarPessoaJuridicaDTO
            {
                RazaoSocial = "Fornecedor Exemplo Ltda",
                NomeFantasia = "Fornecedor Exemplo",
                Cnpj = "11222333000181",
                Endereco = new ImportarEnderecoDTO
                {
                    Cep = "94828-140",
                    Bairro = "Centro",
                    Logradouro = "Rua Exemplo",
                    Numero = "100",
                    Complemento = "Sala 1",
                    CodIbgeCidade = "3550308" // Código IBGE válido para São Paulo
                },
                Contatos = new List<ImportarContatoDTO>
                {
                    new ImportarContatoDTO
                    {
                        Nome = "João da Silva",
                        MeioContato = MeioDeContato.Telefone,
                        Valor = "11999999999"
                    },
                    new ImportarContatoDTO
                    {
                        Nome = "João da Silva",
                        MeioContato = MeioDeContato.Email,
                        Valor = "<EMAIL>"
                    }
                },
                DadosBancarios = new List<ImportarDadosBancariosZinDTO>
                {
                    new ImportarDadosBancariosZinDTO
                    {
                        BancoCodigo = "001",
                        BancoNome = "Banco do Brasil",
                        BancoAgencia = "1234",
                        BancoConta = "56789-0",
                        BancoCpfCnpjTitular = "11222333000181"
                    }
                }
            };
        }

        public static ImportarItemDTO CriarImportarItensValidos(Action<ImportarItemDTO>? action = null,
                                                 int qtd = 2, decimal valorUnit = 10.50m)
        {
            var dto = new ImportarItemDTO
            {
                TipoMovimentoItem = TipoMovimentoItem.Autorizacao,
                TipoItem = TipoItemVersao.Venda,
                IdItemPedidoFornecedor = 1,
                IdFornecedor = 1,
                Codigo = "P-001",
                Descricao = "Peça de teste",

                DataCriacao = new DateTime(2025, 8, 20, 10, 0, 0, DateTimeKind.Utc),
                DataAutorizacao = new DateTime(2025, 8, 21, 10, 0, 0, DateTimeKind.Utc),
                DataMovimento = new DateTime(2025, 8, 22, 10, 0, 0, DateTimeKind.Utc),
                DataEntrega = new DateTime(2025, 8, 23, 10, 0, 0, DateTimeKind.Utc),

                Quantidade = qtd,
                ValorUnitario = valorUnit,
                ValorTotal = decimal.Round(qtd * valorUnit, 2, MidpointRounding.AwayFromZero),

                // Campos opcionais no validador
                CnpjFornecedor = "63.874.828/0001-99",
                CnpjOficina = "63.874.828/0001-99",
                VeiculoPlaca = "ABC-1234",
                DocumentosRelacionados = null
            };
            action?.Invoke(dto);
            return dto;
        }
    }
}
