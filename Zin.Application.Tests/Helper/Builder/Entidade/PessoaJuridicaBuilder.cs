using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.ValueObject;

namespace Zin.Application.Tests.Helper.Builder.Entidade
{


    public class PessoaJuridicaBuilder
    {
        private int _id = 1;
        private string _cnpj = "58.575.103/0001-60"; 
        private bool _matriz = true;

        public PessoaJuridicaBuilder ComId(int id)
        {
            _id = id;
            return this;
        }

        public PessoaJuridicaBuilder ComCnpj(string cnpj)
        {
            _cnpj = cnpj;
            return this;
        }

        public PessoaJuridica Build()
        {
            var fornecedor = new PessoaJuridica(Cnpj.Criar(_cnpj), _cnpj, _cnpj)
            {
                Id = _id
            };

            return fornecedor;
        }
    }
}
