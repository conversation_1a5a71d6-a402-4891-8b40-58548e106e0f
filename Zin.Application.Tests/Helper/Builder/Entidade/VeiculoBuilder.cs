using Zin.Domain.Entidades.ZinPag.Ativos;

namespace Zin.Application.Tests.Helper.Builder.Entidade
{
    public class VeiculoBuilder
    { 
        private int _id = 1;
        private string _placa = "ABC-1234";
        private string _chassi = "9BWZZZ377VT004251";

        public VeiculoBuilder ComId(int id)
        {
            _id = id;
            return this;
        }

        public VeiculoBuilder ComPlaca(string placa)
        {
            _placa = placa;
            return this;
        }

        public Veiculo Build()
        {
            return new Veiculo(_placa)
            { 
               Chassi = _chassi 
            };
        }
    }
}
