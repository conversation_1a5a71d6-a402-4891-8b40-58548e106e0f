using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;

namespace Zin.Application.Tests.Helper.Builder.Entidade
{
    public class DocumentoBuilder
    {
        private string _numero;
        private string _serie;
        private Pessoa _emitente;
        private Pessoa _destinatario;
        private Agregador _agregador;

        public DocumentoBuilder ComNumero(string numero)
        {
            _numero = numero;
            return this;
        }

        public DocumentoBuilder ComSerie(string serie)
        {
            _serie = serie;
            return this;
        }

        public DocumentoBuilder ComEmitente(Pessoa emitente)
        {
            _emitente = emitente;
            return this;
        }

        public DocumentoBuilder ComDestinatario(Pessoa destinatario)
        {
            _destinatario = destinatario;
            return this;
        }

        public DocumentoBuilder ComAgregador(Agregador agregador)
        {
            _agregador = agregador;
            return this;
        }

        public Documento Build()
        {
            var documento = new Documento
            {
                Agregador = _agregador,
                Numero = _numero,
                Serie = _serie,
                Emitente = _emitente,
                Destinatario = _destinatario,
                ValorTotal = 1000
            };

            return documento;
        }
    }
}
