using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.ValueObject;

namespace Zin.Application.Tests.Helper.Builder.Entidade
{
    public class ClienteEmpresaBuilder
    {
        private int _id = 1;
        private string _cnpj = "58.575.103/0001-60";

        public ClienteEmpresaBuilder ComId(int id)
        {
            _id = id;
            return this;
        }

        public ClienteEmpresaBuilder ComCnpj(string cnpj)
        {
            _cnpj = cnpj;
            return this;
        }

        public ClienteEmpresa Build()
        {
            var cliente = new ClienteEmpresa(Cnpj.Criar(_cnpj),true, _cnpj, _cnpj)
            {
                Id = _id
            };

            return cliente;
        }
    }
}
