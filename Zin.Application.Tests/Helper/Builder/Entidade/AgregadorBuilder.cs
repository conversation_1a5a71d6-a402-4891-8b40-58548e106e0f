using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Helper.Builder.Entidade
{
    public class AgregadorBuilder
    {
        private string _numero = "AG-12345";
        private ClienteEmpresa _clienteEmpresa = new ClienteEmpresaBuilder().Build();
        private Veiculo[] _veiculos;
        private Documento[] _documentos;

        public AgregadorBuilder ComNumero(string numero)
        {
            _numero = numero;
            return this;
        }

        public AgregadorBuilder ComAtivos(Veiculo[] veiculos)
        {
            _veiculos = veiculos;
            return this;
        }

        public AgregadorBuilder ComClienteEmpresa(ClienteEmpresa cliente)
        {
            _clienteEmpresa = cliente;
            return this;
        }

        public AgregadorBuilder ComDocumentos(Documento[] documentos)
        {
            _documentos = documentos;
            return this;
        }

        public Agregador Build()
        {
            var agregador = new Agregador
                (
                TipoAgregador.Sinistro,
                _numero
                );

            if (_veiculos != null && _veiculos.Any())
            {
                foreach (var veiculo in _veiculos)
                    agregador.Ativos.Add(veiculo);
            }

            if (_documentos != null && _documentos.Any())
            {
                foreach (var documento in _documentos)
                    agregador.Documentos.Add(documento);
            }

            agregador.DefinirClienteEmpresa(_clienteEmpresa);

            return agregador;
        }
    }
}
