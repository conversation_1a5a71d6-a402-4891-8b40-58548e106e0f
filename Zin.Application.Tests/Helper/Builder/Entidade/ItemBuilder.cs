using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Tests.Helper.Builder.Entidade
{
    public class ItemBuilder
    {
        private string _codigo = Guid.NewGuid().ToString();
        private string _descricao = Guid.NewGuid().ToString();

        private int _quantidade = 1;

        public ItemBuilder ComCodigo(string codigo)
        {
            _codigo = codigo;
            return this;
        }
        public ItemBuilder ComDescricao(string descricao)
        {
            _descricao = descricao;
            return this;
        }

        public ItemBuilder ComQuantidade(int quantidade)
        {
            _quantidade = quantidade;
            return this;
        }

        public Item Build()
        {
            return new Item
            (
                _codigo, 
                _descricao, 
                _quantidade, 
                0,
                0
            );
        }
    }
}
