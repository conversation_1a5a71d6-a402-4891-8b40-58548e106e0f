using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Factories;

namespace Zin.Application.Tests.Helper.Builder.Entidade
{
    public class ItemVersaoBuilder
    {
        private Item _item = null!;

        private TipoMovimentoItem _tipoMovimento = TipoMovimentoItem.Autorizacao;
        private TipoItemVersao _tipo = TipoItemVersao.Venda;
        private DateTime? _autorizacao = DateTime.UtcNow.AddHours(-1);
        private DateTime? _entrega = DateTime.UtcNow.AddHours(-1);
        private string _codigo = Guid.NewGuid().ToString();
        private string _descricao = Guid.NewGuid().ToString();

        private PessoaJuridica? _fornecedor;
        private PessoaJuridica? _oficina;
        private int _quantidade = 1;
        private DateTime _movimentacao;
        private ItemVersao _versaoAnterior;
        private Veiculo _veiculo;

        public ItemVersaoBuilder ComTipoMovimento(TipoMovimentoItem tipo)
        {
            _tipoMovimento = tipo;
            return this;
        }

        public ItemVersaoBuilder ComAutorizacao(DateTime? data)
        {
            _autorizacao = data;
            return this;
        }

        public ItemVersaoBuilder ComCodigo(string codigo)
        {
            _codigo = codigo;
            return this;
        }
        public ItemVersaoBuilder ComDescricao(string descricao)
        {
            _descricao = descricao;
            return this;
        }

        public ItemVersaoBuilder ComFornecedor(PessoaJuridica fornecedor)
        {
            _fornecedor = fornecedor;
            return this;
        }

        public ItemVersaoBuilder ComItem(Item item)
        {
            _item = item;
            return this;
        }

        public ItemVersaoBuilder ComOficina(PessoaJuridica oficina)
        {
            _oficina = oficina;
            return this;
        }

        public ItemVersaoBuilder ComQuantidade(int quantidade)
        {
            _quantidade = quantidade;
            return this;
        }


        public ItemVersaoBuilder ComMovimentacao(DateTime movimentacao)
        {
            _movimentacao = movimentacao;
            return this;
        }

        public ItemVersaoBuilder ComVersaoAnterior(ItemVersao versaoAnterior)
        {
            _versaoAnterior = versaoAnterior;
            return this;
        }

        public ItemVersaoBuilder ComVeiculo(Veiculo veiculo)
        {
            _veiculo = veiculo;
            return this;
        }

        public ItemVersao Build()
        {
            var item = _item 
                            ?? new ItemBuilder()
                                .ComCodigo(_codigo)
                                .ComDescricao(_descricao)
                                .Build();

            var versao = new ItemVersaoFactory().Criar(
                item, 
                _versaoAnterior,
                _fornecedor,
                _oficina,
                _veiculo,
                0,
                0,
                0,
                _tipoMovimento,
                _tipo,
                DateTime.UtcNow,
                _autorizacao,
                _movimentacao,
                _entrega,
                null
            );

            return versao;
        }
    }
}
