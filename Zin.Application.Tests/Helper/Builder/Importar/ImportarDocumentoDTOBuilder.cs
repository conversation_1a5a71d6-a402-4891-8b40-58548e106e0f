using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Tests.Helper.Builder.Importar
{
    public class ImportarDocumentoDTOBuilder
    {
        private string _cnpjFornecedor = "58.575.103/0001-60";
        private string _codigo = Guid.NewGuid().ToString();
        private string _descricao = Guid.NewGuid().ToString();
        private string _numero = Guid.NewGuid().ToString();
        private string _serie;

        public ImportarDocumentoDTOBuilder ComCodigo(string codigo)
        {
            _codigo = codigo;
            return this;
        }

        public ImportarDocumentoDTOBuilder ComDescricao(string descricao)
        {
            _descricao = descricao;
            return this;
        }

        public ImportarDocumentoDTOBuilder ComNumero(string numero)
        {
            _numero = numero;
            return this;
        }

        public ImportarDocumentoDTOBuilder ComSerie(string serie)
        {
            _serie = serie;
            return this;
        }


        public ImportarDocumentoDTO Build()
        {
            return new ImportarDocumentoDTO
            {
                CnpjFornecedor = _cnpjFornecedor,
                NumeroDocumento = _numero,
                Serie = _serie
            };
        }
    }
}
