using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Tests.Helper.Builder.Importar
{
    /*
             public static ImportarEnderecoDTO CriarEndereco()
        {
            return new ImportarEnderecoDTO
            {
                Logradouro = "Rua A",
                Numero = "123",
                CodIbgeCidade = "3550308", // Código IBGE válido para São Paulo
                Cep = "12345-678"
            };
        }
     */
    public class ImportarEnderecoDTOBuilder
    {
        private string _cep = "94828-140";

        public ImportarEnderecoDTOBuilder ComCep(string cep)
        {
            _cep = cep;
            return this;
        }

        public ImportarEnderecoDTO Build()
        {
            var endereco = new ImportarEnderecoDTO
            {
                Logradouro = "Rua Exemplo",
                Numero = "123",
                Complemento = "Apto 45",
                Bairro = "Centro",
                CodIbgeCidade = "123456",
                Cep = _cep
            };

            return endereco;
        }
    }
}
