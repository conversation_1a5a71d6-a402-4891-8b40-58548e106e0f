using Zin.Application.DTOs.Importacao;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Helper.Builder.Importar
{
    public class ImportarNotaFiscalItemDTOBuilder
    {
        private string _cnpjFornecedor = "58.575.103/0001-60";
        private string _numero = Guid.NewGuid().ToString();
        private string _serie;
        private TipoDocumento _tipo = TipoDocumento.Outro;

        public ImportarNotaFiscalItemDTOBuilder ComCnpjFornecedor(string cnpj)
        {
            _cnpjFornecedor = cnpj;
            return this;
        }

        public ImportarNotaFiscalItemDTOBuilder ComNumero(string numero)
        {
            _numero = numero;
            return this;
        }

        public ImportarNotaFiscalItemDTOBuilder ComSerie(string serie)
        {
            _serie = serie;
            return this;
        }

        public ImportarNotaFiscalItemDTOBuilder ComTipo(TipoDocumento tipo)
        {
            _tipo = tipo;
            return this;
        }


        public ImportarNotaFiscalItemDTO Build()
        {
            return new ImportarNotaFiscalItemDTO
            {
                CnpjFornecedor = _cnpjFornecedor,
                NumeroNota = _numero,
                Serie = _serie,
                Tipo = _tipo
            };
        }
    }
}
