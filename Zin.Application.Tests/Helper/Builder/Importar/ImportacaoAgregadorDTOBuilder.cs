using Zin.Application.DTOs.Importacao;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Helper.Builder.Importar
{
    public class ImportacaoAgregadorDTOBuilder
    {
        private TipoAgregador _tipo = TipoAgregador.Sinistro;
        private string _numero = Guid.NewGuid().ToString();
        private IEnumerable<ImportarPessoaJuridicaDTO> _fornecedores;
        private IEnumerable<ImportarPessoaJuridicaDTO> _oficinas;
        private IEnumerable<ImportarDocumentoDTO> _documentos;
        private IEnumerable<ImportarItemDTO> _itens;
        private ImportarAtivoVeiculoDTO[] _veiculos;

        public ImportacaoAgregadorDTOBuilder ComTipo(TipoAgregador tipo)
        {
            _tipo = tipo;
            return this;
        }

        public ImportacaoAgregadorDTOBuilder ComNumero(string numero)
        {
            _numero = numero;
            return this;
        }

        public ImportacaoAgregadorDTOBuilder ComItens(IEnumerable<ImportarItemDTO> itens)
        {
            _itens = itens;
            return this;
        }

        public ImportacaoAgregadorDTOBuilder ComFornecedores(IEnumerable<ImportarPessoaJuridicaDTO> fornecedores)
        {
            _fornecedores = fornecedores;
            return this;
        }

        public ImportacaoAgregadorDTOBuilder ComOficinas(IEnumerable<ImportarPessoaJuridicaDTO> oficinas)
        {
            _oficinas = oficinas;
            return this;
        }

        public ImportacaoAgregadorDTOBuilder ComDocumentos(IEnumerable<ImportarDocumentoDTO> documentos)
        {
            _documentos = documentos;
            return this;
        }

        public ImportacaoAgregadorDTOBuilder ComVeiculos(ImportarAtivoVeiculoDTO[] veiculos)
        {
            _veiculos = veiculos;
            return this;
        }

        public ImportacaoAgregadorDTO Build()
        {
            return new ImportacaoAgregadorDTO
            {
                TipoAgregador = _tipo,
                Numero = _numero,
                Itens = _itens?.ToList() ?? new List<ImportarItemDTO>(),
                Fornecedores = _fornecedores?.ToList() ?? new List<ImportarPessoaJuridicaDTO>(),
                Oficinas = _oficinas?.ToList() ?? new List<ImportarPessoaJuridicaDTO>(),
                Documentos = _documentos?.ToList(),
                Ativos = new ImportarAtivosDTO 
                {
                    Veiculos = _veiculos?.ToList() ?? new List<ImportarAtivoVeiculoDTO>()
                }
            };
        }

      
    }
}
