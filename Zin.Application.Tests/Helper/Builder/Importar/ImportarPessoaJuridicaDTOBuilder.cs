using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Tests.Helper.Builder.Importar
{

    public class ImportarPessoaJuridicaDTOBuilder
    {
        private string _cnpj = "58.575.103/0001-60";

        public ImportarPessoaJuridicaDTOBuilder ComCnpj(string cnpj)
        {
            _cnpj = cnpj;
            return this;
        }

        public ImportarPessoaJuridicaDTO Build()
        {
            var oficina = new ImportarPessoaJuridicaDTO
            {
                Cnpj = _cnpj,
                RazaoSocial = _cnpj,
                NomeFantasia = _cnpj,
                Endereco = new ImportarEnderecoDTOBuilder().Build()
            };

            return oficina;
        }
    }
}
