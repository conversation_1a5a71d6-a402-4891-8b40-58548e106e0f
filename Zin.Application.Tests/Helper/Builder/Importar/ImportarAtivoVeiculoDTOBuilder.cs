using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Tests.Helper.Builder.Importar
{
    public class ImportarAtivoVeiculoDTOBuilder
    { 
        private string _placa = "ABC-1234";
        private string _chassi = "9BWZZZ377VT004251";


        public ImportarAtivoVeiculoDTOBuilder ComPlaca(string placa)
        {
            _placa = placa;
            return this;
        }

        public ImportarAtivoVeiculoDTO Build()
        {
            return new ImportarAtivoVeiculoDTO
            {
                Placa = _placa,
                Chassi = _chassi
            };
        }
    }
}
