using Zin.Application.DTOs.Importacao;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Helper.Builder.Importar
{

    public class ImportarItemDTOBuilder
    {
        private TipoMovimentoItem _tipo = TipoMovimentoItem.Exclusao;
        private string _cnpjFornecedor = "58.575.103/0001-60";
        private string _cnpjOficina = "58.575.103/0001-60";
        private DateTime _dataAutorizacao = DateTime.UtcNow;
        private string _codigo = Guid.NewGuid().ToString();
        private string _descricao = Guid.NewGuid().ToString();
        private int _quantidade = 1;
        private DateTime _movimentacao;
        private string _placa;
        private IEnumerable<ImportarNotaFiscalItemDTO> _documentos = Enumerable.Empty<ImportarNotaFiscalItemDTO>();

        public ImportarItemDTOBuilder ComTipoMovimento(TipoMovimentoItem tipo)
        {
            _tipo = tipo;
            return this;
        }

        public ImportarItemDTOBuilder ComCnpjFornecedor(string cnpj)
        {
            _cnpjFornecedor = cnpj;
            return this;
        }

        public ImportarItemDTOBuilder ComCnpjOficina(string cnpj)
        {
            _cnpjOficina = cnpj;
            return this;
        }

        public ImportarItemDTOBuilder ComAutorizacao(DateTime data)
        {
            _dataAutorizacao = data;
            return this;
        }

        public ImportarItemDTOBuilder ComCodigo(string codigo)
        {
            _codigo = codigo;
            return this;
        }

        public ImportarItemDTOBuilder ComDescricao(string descricao)
        {
            _descricao = descricao;
            return this;
        }

        public ImportarItemDTOBuilder ComQuantidade(int quantidade)
        {
            _quantidade = quantidade;
            return this;
        }

        public ImportarItemDTOBuilder ComMovimento(DateTime movimentacao)
        {
            _movimentacao = movimentacao;
            return this;
        }

        public ImportarItemDTOBuilder ComPlaca(string placa)
        {
            _placa = placa;
            return this;
        }

        internal ImportarItemDTOBuilder ComDocumentosRelacionados(ImportarNotaFiscalItemDTO[] documentos)
        {
            _documentos = documentos;
            return this;
        }

        public ImportarItemDTO Build()
        {
            return new ImportarItemDTO
            {
                TipoMovimentoItem = _tipo,
                CnpjFornecedor = _cnpjFornecedor,
                CnpjOficina = _cnpjOficina,
                DataAutorizacao = _dataAutorizacao,
                Codigo = _codigo,
                Descricao = _descricao,
                Quantidade = _quantidade,
                DataMovimento = _movimentacao,
                VeiculoPlaca = _placa,
                DocumentosRelacionados = _documentos.ToList()
            };
        }
    }
}
