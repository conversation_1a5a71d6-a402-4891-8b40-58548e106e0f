using FluentValidation.TestHelper;
using Zin.Application.DTOs.Importacao;        
using Zin.Application.Helpers.Validador;
using Zin.Application.Tests.Helper;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Validador
{

    public sealed class ValidadorImportarItensTests
    {
        private readonly ValidadorImportarItens _validator = new();

        private static TEnum AnyEnum<TEnum>() where TEnum : struct, Enum
            => Enum.GetValues(typeof(TEnum)).Cast<TEnum>().First();

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("    ")]
        public void Codigo_Obrigatorio_Deve_Falhar(string? codigo)
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.Codigo = codigo);
            var res = _validator.TestValidate(dto);
            res.ShouldHaveValidationErrorFor(x => x.Codigo);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("    ")]
        public void Descricao_Obrigatoria_Deve_Falhar(string? descricao)
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.Descricao = descricao);
            var res = _validator.TestValidate(dto);
            res.ShouldHaveValidationErrorFor(x => x.Descricao);
        }

        [Fact]
        public void Deve_Passar_Quando_MinimoValido()
        {
            var dto = TesteFactory.CriarImportarItensValidos();
            var res = _validator.TestValidate(dto);
            res.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void Enums_Invalidos_Devem_Falhar()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d =>
            {
                d.TipoMovimentoItem = (TipoMovimentoItem)99999;
                d.TipoItem = (TipoItemVersao)99999;
            });
            var res = _validator.TestValidate(dto);
            res.ShouldHaveValidationErrorFor(x => x.TipoMovimentoItem);
            res.ShouldHaveValidationErrorFor(x => x.TipoItem);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public void IdItemPedidoFornecedor_Invalido(int id)
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.IdItemPedidoFornecedor = id);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.IdItemPedidoFornecedor);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-10)]
        public void IdFornecedor_Invalido(int id)
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.IdFornecedor = id);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.IdFornecedor);
        }

        [Fact]
        public void DataCriacao_Obrigatoria()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.DataCriacao = DateTime.MinValue);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.DataCriacao);
        }

        [Fact]
        public void DataAutorizacao_Deve_Ser_MaiorOuIgual_DataCriacao()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.DataAutorizacao = d.DataCriacao.AddMinutes(-1));
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.DataAutorizacao);
        }

        [Fact]
        public void DataMovimento_Deve_Ser_MaiorOuIgual_DataAutorizacao()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.DataMovimento = d.DataAutorizacao.AddSeconds(-1));
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.DataMovimento);
        }

        [Fact]
        public void DataEntrega_Se_Informada_Deve_Ser_MaiorOuIgual_DataMovimento_Data()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.DataEntrega = d.DataMovimento.AddDays(-1)); // dia anterior
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.DataEntrega);
        }

        [Fact]
        public void DataEntrega_Pode_Ser_Nula()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.DataEntrega = null);
            _validator.TestValidate(dto).ShouldNotHaveValidationErrorFor(x => x.DataEntrega);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public void Quantidade_Invalida(int qtd)
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.Quantidade = qtd);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.Quantidade);
        }

        [Fact]
        public void ValorUnitario_Nao_Pode_Ser_Negativo()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.ValorUnitario = -0.01m);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.ValorUnitario);
        }

        [Fact]
        public void ValorTotal_Nao_Pode_Ser_Negativo()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.ValorTotal = -0.01m);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.ValorTotal);
        }

        [Fact]
        public void ValorTotal_Deve_Ser_Qtd_Vezes_Unitario_Arrend_2_AwayFromZero()
        {
            var dto = TesteFactory.CriarImportarItensValidos(qtd: 1, valorUnit: 10.005m);
            dto.ValorTotal = 10.00m; // errado de propósito (esperado 10,01)
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x);
        }

        [Fact]
        public void ValorTotal_Consistente_Deve_Passar()
        {
            var dto = TesteFactory.CriarImportarItensValidos(qtd: 3, valorUnit: 12.345m);
            dto.ValorTotal = decimal.Round(dto.Quantidade * dto.ValorUnitario, 2, MidpointRounding.AwayFromZero);
            _validator.TestValidate(dto).ShouldNotHaveValidationErrorFor(x => x);
        }

        [Fact]
        public void DocumentosRelacionados_Com_ItemNulo_Deve_Falhar()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.DocumentosRelacionados = new List<ImportarNotaFiscalItemDTO?> { null! });
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.DocumentosRelacionados);
        }

        [Fact]
        public void DocumentosRelacionados_Nulo_ou_Vazio_Deve_Passar()
        {
            _validator.TestValidate(TesteFactory.CriarImportarItensValidos(d => d.DocumentosRelacionados = null))
                      .ShouldNotHaveValidationErrorFor(x => x.DocumentosRelacionados);

            _validator.TestValidate(TesteFactory.CriarImportarItensValidos(d => d.DocumentosRelacionados = new List<ImportarNotaFiscalItemDTO>()))
                      .ShouldNotHaveValidationErrorFor(x => x.DocumentosRelacionados);
        }

        [Fact]
        public void CnpjsEPlaca_Obrigatorios()
        {
            var dto = TesteFactory.CriarImportarItensValidos(d =>
            {
                d.CnpjFornecedor = null;
                d.CnpjOficina = null;
                d.VeiculoPlaca = null;
            });
            var res = _validator.TestValidate(dto);
            res.ShouldHaveValidationErrorFor(x => x.CnpjFornecedor);
            res.ShouldHaveValidationErrorFor(x => x.CnpjOficina);
            res.ShouldHaveValidationErrorFor(x => x.VeiculoPlaca);
        }

        [Theory]
        [InlineData("11111111111111")]
        [InlineData("00.000.000/0000-00")]
        public void CnpjFornecedor_Invalido_Deve_Falhar(string cnpj)
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.CnpjFornecedor = cnpj);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.CnpjFornecedor);
        }

        [Theory]
        [InlineData("00000000000000")]
        [InlineData("12.345.678/9012-34")]
        public void CnpjOficina_Invalido_Deve_Falhar(string cnpj)
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.CnpjOficina = cnpj);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.CnpjOficina);
        }

        [Theory]
        [InlineData("ABC-12")]   // curto
        [InlineData("1234567")]  // só dígitos
        public void VeiculoPlaca_Invalida_Deve_Falhar(string placa)
        {
            var dto = TesteFactory.CriarImportarItensValidos(d => d.VeiculoPlaca = placa);
            _validator.TestValidate(dto).ShouldHaveValidationErrorFor(x => x.VeiculoPlaca);
        }
    }
}
