using FluentValidation.TestHelper;
using Xunit;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorDadosBancariosTests
    {
        private readonly ValidadorDadosBancarios _validador;

        public ValidadorDadosBancariosTests()
        {
            _validador = new ValidadorDadosBancarios();
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_BancoCodigo_Estiver_Vazio_Ou_Nulo()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoCodigo = null };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoCodigo);

            dto.BancoCodigo = "";
            resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoCodigo);
        }

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_BancoCodigo_Preenchido()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoCodigo = "001" };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.BancoCodigo);
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_BancoNome_Estiver_Vazio_Ou_Nulo()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoNome = null };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoNome);

            dto.BancoNome = "";
            resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoNome);
        }

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_BancoNome_Preenchido()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoNome = "Banco do Brasil" };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.BancoNome);
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_BancoAgencia_Estiver_Vazio_Ou_Nulo()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoAgencia = null };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoAgencia);

            dto.BancoAgencia = "";
            resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoAgencia);
        }

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_BancoAgencia_Preenchido()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoAgencia = "1234" };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.BancoAgencia);
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_BancoConta_Estiver_Vazio_Ou_Nulo()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoConta = null };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoConta);

            dto.BancoConta = "";
            resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoConta);
        }

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_BancoConta_Preenchido()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoConta = "56789-0" };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.BancoConta);
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_BancoCpfCnpjTitular_Estiver_Vazio_Ou_Nulo()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoCpfCnpjTitular = null };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoCpfCnpjTitular);

            dto.BancoCpfCnpjTitular = "";
            resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.BancoCpfCnpjTitular);
        }

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_BancoCpfCnpjTitular_Preenchido()
        {
            var dto = new ImportarDadosBancariosZinDTO { BancoCpfCnpjTitular = "12345678901" };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.BancoCpfCnpjTitular);
        }
    }
}