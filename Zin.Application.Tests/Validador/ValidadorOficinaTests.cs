using FluentValidation.TestHelper;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorOficinaTests
    {
        private readonly ValidadorOficina _validador = new ValidadorOficina();

        [Fact]
        public void DeveRetornarErroQuandoNomeForNuloOuVazio()
        {
            var dto = new ImportarPessoaJuridicaDTO { RazaoSocial = null };
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.RazaoSocial);
        }

        [Fact]
        public void DeveSerValidoQuandoTodosOsCamposForemPreenchidos()
        {
            var dto = new ImportarPessoaJuridicaDTO 
            {
                RazaoSocial = "Oficina 1",
                NomeFantasia = "Oficina 1",
                Cnpj = "63.874.828/0001-99",
                Endereco = new ImportarEnderecoDTO
                {
                    Logradouro = "Rua A",
                    Numero = "123",
                    CodIbgeCidade = "3550308", // Código IBGE válido para São Paulo
                    Cep = "12345-678"
                },
                Contatos = new List<ImportarContatoDTO>()
                {
                    new ImportarContatoDTO()
                    {
                        Nome = "Oficina 1",
                        MeioContato = MeioDeContato.Email,
                        Valor = "<EMAIL>"
                    }
                }
            };

            var resultado = _validador.TestValidate(dto);

            resultado.ShouldNotHaveAnyValidationErrors();
        }
    }
}
