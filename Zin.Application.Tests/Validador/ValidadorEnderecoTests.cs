using FluentValidation.TestHelper;
using Zin.Application.Helpers.Validador;
using Zin.Application.Tests.Helper;
using Zin.Application.Tests.Helper.Builder.Importar;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorEnderecoTests
    {
        private readonly ValidadorEndereco _validador = new ValidadorEndereco();

        [Fact]
        public void DeveRetornarValido_ParaEnderecoCorreto()
        {
            var endereco = new ImportarEnderecoDTOBuilder()
                .ComCep("94828-140")
                
                .Build();

            var resultado = _validador.TestValidate(endereco);

            resultado.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void DeveRetornarInvalido_ParaCEPIncorreto()
        {
            var endereco = new ImportarEnderecoDTOBuilder().Build();
            endereco.Cep = "00000000";
            
            var resultado = _validador.TestValidate(endereco);
            resultado.ShouldHaveValidationErrorFor(x => x.Cep);
        }

        [Fact]
        public void DeveRetornarInvalido_ParaCamposObrigatoriosVazios()
        {
            var endereco = new ImportarEnderecoDTOBuilder().Build();
            endereco.Logradouro = "";
            endereco.Numero = "";
            endereco.CodIbgeCidade = "";
            endereco.Cep = "";

            var resultado = _validador.TestValidate(endereco);

            resultado.ShouldHaveValidationErrors();
        }
    }
}
