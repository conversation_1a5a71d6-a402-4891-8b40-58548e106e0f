using Zin.Application.Helpers.Validador.Extension;
using Zin.Domain.ValueObject;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorCnpjTests
    {
        // V<PERSON>lidos (sem máscara)
        [Theory]
        [InlineData("04252011000110")] // Microsoft Brasil Serviços...
        [InlineData("19131243000197")]
        [InlineData("33400689000109")]
        [InlineData("27865757000102")]
        public void EhCnpjValido_DeveRetornarTrue_ParaCnpjValidos(string cnpj)
        {
            Assert.True(Cnpj.EhValido(cnpj));
        }

        // Válido (com máscara)
        [Theory]
        [InlineData("04.252.011/0001-10")]
        [InlineData("19.131.243/0001-97")]
        [InlineData("33.400.689/0001-09")]
        [InlineData("27.865.757/0001-02")]
        public void EhCnpjValido_DeveIgnorarMascara(string cnpjComMascara)
        {
            Assert.True(Cnpj.<PERSON><PERSON><PERSON><PERSON>(cnpjComMascara));
        }

        // Inválidos (formato/tamanho/sequência dígitos)
        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        [InlineData("1234567890123")]     // 13 dígitos
        [InlineData("123456789012345")]   // 15 dígitos
        [InlineData("00000000000000")]    // sequência repetida
        [InlineData("11111111111111")]    // sequência repetida
        public void EhCnpjValido_DeveRetornarFalse_ParaEntradasInvalidas(string? cnpj)
        {
            Assert.False(Cnpj.EhValido(cnpj));
        }

        // Inválido (dígito verificador alterado)
        [Fact]
        public void EhCnpjValido_DeveRetornarFalse_ParaDigitoVerificadorInvalido()
        {
            var invalido = "04252011000111"; // último dígito propositalmente trocado
            Assert.False(Cnpj.EhValido(invalido));
        }
    }
}
