using FluentValidation.TestHelper;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorFornecedorTests
    {
        private readonly ValidadorFornecedor _validador = new();

        [Fact]
        public void Deve_Falhar_Quando_RazaoSocial_Eh_Nula()
        {
            var dto = GerarDTOValido();
            dto.RazaoSocial = null;
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.RazaoSocial);
        }

        [Fact]
        public void Deve_Falhar_Quando_NomeFantasia_Eh_Nula()
        {
            var dto = GerarDTOValido();
            dto.NomeFantasia = null;
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.NomeFantasia);
        }

        [Fact]
        public void Deve_Falhar_Quando_Cnpj_Eh_Nulo()
        {
            var dto = GerarDTOValido();
            dto.Cnpj = null;
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.Cnpj);
        }

        [Fact]
        public void Deve_Falhar_Quando_Endereco_Eh_Nulo()
        {
            var dto = GerarDTOValido();
            dto.Endereco = null;
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.Endereco);
        }

        [Fact]
        public void Deve_Falhar_Quando_Contatos_Eh_Nulo_Ou_Vazio()
        {
            var dtoNulo = GerarDTOValido();
            dtoNulo.Contatos = null;
            var resultadoNulo = _validador.TestValidate(dtoNulo);
            resultadoNulo.ShouldHaveValidationErrorFor(x => x.Contatos);

            var dtoVazio = GerarDTOValido();
            dtoVazio.Contatos = new List<ImportarContatoDTO>();
            var resultadoVazio = _validador.TestValidate(dtoVazio);
            resultadoVazio.ShouldHaveValidationErrorFor(x => x.Contatos);
        }

        [Fact]
        public void Deve_Falhar_Quando_DadosBancarios_Eh_Nulo_Ou_Vazio()
        {
            var dtoNulo = GerarDTOValido();
            dtoNulo.DadosBancarios = null;
            var resultadoNulo = _validador.TestValidate(dtoNulo);
            resultadoNulo.ShouldHaveValidationErrorFor(x => x.DadosBancarios);

            var dtoVazio = GerarDTOValido();
            dtoVazio.DadosBancarios = new List<ImportarDadosBancariosZinDTO>();
            var resultadoVazio = _validador.TestValidate(dtoVazio);
            resultadoVazio.ShouldHaveValidationErrorFor(x => x.DadosBancarios);
        }

        [Fact]
        public void Deve_Passar_Quando_DTO_Valido()
        {
            var dto = GerarDTOValido();
            var resultado = _validador.TestValidate(dto);
            resultado.ShouldNotHaveAnyValidationErrors();
        }

        private ImportarPessoaJuridicaDTO GerarDTOValido()
        {
            return new ImportarPessoaJuridicaDTO
            {
                RazaoSocial = "Empresa Teste",
                NomeFantasia = "Fantasia Teste",
                Cnpj = "91.318.816/0001-13",
                Endereco = new ImportarEnderecoDTO
                {
                    Cep = "12345678",
                    Bairro = "Centro",
                    Logradouro = "Rua Teste",
                    Numero = "100",
                    Complemento = "Sala 1",
                    CodIbgeCidade = "3550308" // C�digo IBGE v�lido para S�o Paulo
                },
                Contatos = new List<ImportarContatoDTO>
                {
                    new ImportarContatoDTO
                    {
                        Nome = "Contato Teste",
                        MeioContato = MeioDeContato.Telefone,
                        Valor = "5199926-8641"
                    },
                    new ImportarContatoDTO
                    {
                        Nome = "Contato Teste",
                        MeioContato = MeioDeContato.Email,
                        Valor = "<EMAIL>"
                    }
                },
                DadosBancarios = new List<ImportarDadosBancariosZinDTO>
                {
                    new ImportarDadosBancariosZinDTO
                    {
                        BancoTitular = "Titular",
                        BancoCpfCnpjTitular = "12345678900",
                        BancoCodigo = "001",
                        BancoNome = "Banco Teste",
                        BancoAgencia = "1234",
                        BancoConta = "56789-0"
                    }
                }
            };
        }
    }
}