using FluentValidation.TestHelper;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorVeiculoTests
    {
        private readonly ValidadorVeiculo _validador = new ValidadorVeiculo();

        [Fact]
        public void ImportarAtivoVeiculoDTO_DeveSerValido_QuandoDadosCorretos()
        {
            var dto = new ImportarAtivoVeiculoDTO
            {
                Placa = "ABC1234",
                Chassi = "9BWZZZ377VT004251",
                AnoModelo = 2023,
                Modelo = "Gol",
                Marca = "Volkswagen",
                CnpjOficina = "12.345.678/0001-95"
            };
            var resultado = _validador.TestValidate(dto);

            resultado.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void ImportarAtivoVeiculoDTO_DeveSerInvalido_QuandoPlacaVazia()
        {
            var dto = new ImportarAtivoVeiculoDTO
            {
                Placa = "",
                Chassi = "9BWZZZ377VT004251",
                AnoModelo = 2023,
                Modelo = "Gol",
                Marc<PERSON> = "Volkswagen",
                CnpjOficina = "12.345.678/0001-95"
            };

            var resultado = _validador.TestValidate(dto);

            resultado.ShouldHaveValidationErrorFor(x => x.Placa);
        }
    }
}
