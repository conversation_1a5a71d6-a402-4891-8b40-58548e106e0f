using FluentValidation.TestHelper;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorContatoTests
    {
        private readonly ValidadorContato _validator = new();

        [Fact]
        public void Deve_Retornar_Erro_Quando_Nome_Estiver_Nulo_Ou_Vazio()
        {
            var dto = new ImportarContatoDTO { Nome = null };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.Nome);

            dto.Nome = "";
            resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.Nome);
        }

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_Nome_Preenchido()
        {
            var dto = new ImportarContatoDTO { Nome = "Jo�o Silva" };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.Nome);
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_Email_Estiver_Nulo_Ou_Vazio()
        {
            var dto = new ImportarContatoDTO { Valor = null, MeioContato = MeioDeContato.Email };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.Valor);

            //dto.Email = "";
            resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.Valor);
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_Email_Invalido()
        {
            var dto = new ImportarContatoDTO { Valor = "email-invalido", MeioContato = MeioDeContato.Email };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.Valor);
        }

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_Email_Valido()
        {
            var dto = new ImportarContatoDTO { Valor = "<EMAIL>", MeioContato = MeioDeContato.Email };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.Valor);
        }

        //[Fact]
        //public void Deve_Retornar_Erro_Quando_Tipos_Nulo_Ou_Vazio()
        //{
        //    var dto = new ImportarContatoDTO { Tipos = null };
        //    var resultado = _validator.TestValidate(dto);
        //    resultado.ShouldHaveValidationErrorFor(x => x.Tipos);

        //    //dto.Tipos = new List<TipoContato>();
        //    resultado = _validator.TestValidate(dto);
        //    resultado.ShouldHaveValidationErrorFor(x => x.Tipos);
        //}

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_Tipos_Com_Elementos()
        {
            var dto = new ImportarContatoDTO { MeioContato = MeioDeContato.Telefone };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.MeioContato);
        }
    }
}