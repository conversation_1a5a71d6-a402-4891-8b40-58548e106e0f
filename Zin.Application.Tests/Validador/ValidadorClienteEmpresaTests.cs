using FluentValidation.TestHelper;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorClienteEmpresaTests
    {
        private readonly ValidadorClienteEmpresa _validator = new();

        [Fact]
        public void Deve_Retornar_Erro_Quando_Cnpj_For_Invalido()
        {
            var dto = new ImportarClienteEmpresaDTO { Cnpj = "12345678901234" }; // CNPJ inválido
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.Cnpj);
        }

        [Fact]
        public void Nao_Deve_Retornar_Erro_Quando_Cnpj_For_Valido()
        {
            var dto = new ImportarClienteEmpresaDTO { Cnpj = "12.345.678/0001-95" }; // CNPJ válido
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldNotHaveValidationErrorFor(x => x.Cnpj);
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_RazaoSocial_For_Nula_Ou_Vazia()
        {
            var dto = new ImportarClienteEmpresaDTO { RazaoSocial = "" };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.RazaoSocial);
        }

        [Fact]
        public void Deve_Retornar_Erro_Quando_NomeFantasia_For_Nulo_Ou_Vazio()
        {
            var dto = new ImportarClienteEmpresaDTO { NomeFantasia = "" };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor(x => x.NomeFantasia);
        }

        [Fact]
        public void Deve_Validar_Endereco()
        {
            var dto = new ImportarClienteEmpresaDTO
            {
                Endereco = new ImportarEnderecoDTO
                {
                    Cep = "",
                    Bairro = "",
                    Logradouro = "",
                    Numero = "",
                    CodIbgeCidade = ""
                }
            };
            var resultado = _validator.TestValidate(dto);
            resultado.ShouldHaveValidationErrorFor("Endereco.Cep");
            resultado.ShouldHaveValidationErrorFor("Endereco.Logradouro");
            resultado.ShouldHaveValidationErrorFor("Endereco.Numero");
            resultado.ShouldHaveValidationErrorFor("Endereco.CodIbgeCidade");
        }
    }
}
