using FluentValidation.TestHelper;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador;
using Zin.Application.Tests.Helper;
using Zin.Domain.Enums;

namespace Zin.Application.Tests.Validador
{
    public class ValidadorCriaImportacaoAgregadorDTO_Tests
    {
        private readonly ValidadorCriaImportacaoAgregador _sut;

        public ValidadorCriaImportacaoAgregadorDTO_Tests()
        {
            _sut = new ValidadorCriaImportacaoAgregador();
        }

        #region Campos Simples

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        [InlineData("not-a-guid")]
        public void IdCliente_Invalido_Deve_RetornarErro(string? id)
        {
            var dto = TesteFactory.CriarImportacaValida();
            dto.IdCliente = id;

            var res = _sut.TestValidate(dto);
            res.ShouldHaveValidationErrorFor(x => x.IdCliente);
        }

        [Fact]
        public void IdCliente_GuidValido_Deve_Passar()
        {
            var dto = TesteFactory.CriarImportacaValida();
            dto.IdCliente = Guid.NewGuid().ToString();

            var res = _sut.TestValidate(dto);
            res.ShouldNotHaveValidationErrorFor(x => x.IdCliente);
        }

        [Fact]
        public void Numero_ComMaisDe100Caracteres_Deve_RetornarErro()
        {
            var dto = TesteFactory.CriarImportacaValida();
            dto.Numero = new string('X', 101);

            var res = _sut.TestValidate(dto);
            res.ShouldHaveValidationErrorFor(x => x.Numero);
        }

        #endregion

        #region Coleções: nulas ou vazias são aceitas (regra de obrigatoriedade é global)

        [Fact]
        public void Ativo_Obrigatorio()
        {
            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: false);
            dto.Ativos = null;

            var res1 = _sut.TestValidate(dto);
            res1.ShouldHaveValidationErrorFor(x => x.Ativos);

            //dto.Ativos = new List<ImportarAtivosDTO>();
            var res2 = _sut.TestValidate(dto);
            res2.ShouldHaveValidationErrorFor(x => x.Ativos);
        }

        [Fact]
        public void Fornecedor_Obrigatorio()
        {
            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: false);
            dto.Fornecedores = null;

            var res1 = _sut.TestValidate(dto);
            res1.ShouldHaveValidationErrorFor(x => x.Fornecedores);

            dto.Fornecedores = new List<ImportarPessoaJuridicaDTO>();
            var res2 = _sut.TestValidate(dto);
            res2.ShouldHaveValidationErrorFor(x => x.Fornecedores);
        }

        [Fact]
        public void Oficina_Obrigatoria()
        {
            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: false);
            dto.Oficinas = null;

            var res1 = _sut.TestValidate(dto);
            res1.ShouldHaveValidationErrorFor(x => x.Oficinas);

            dto.Oficinas = new List<ImportarPessoaJuridicaDTO>();
            var res2 = _sut.TestValidate(dto);
            res2.ShouldHaveValidationErrorFor(x => x.Oficinas);
        }

        [Fact]
        public void Item_Obrigatorio()
        {
            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: false);
            dto.Itens = null;

            var res1 = _sut.TestValidate(dto);
            res1.ShouldHaveValidationErrorFor(x => x.Itens);

            dto.Itens = new List<ImportarItemDTO>();
            var res2 = _sut.TestValidate(dto);
            res2.ShouldHaveValidationErrorFor(x => x.Itens);
        }

        #endregion

        #region AlgumaColecaoInformada (guarda global)

        [Fact]
        public void Colecoes_Obrigatorias()
        {
            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: false);
            //dto.Ativos = new List<ImportarAtivosDTO>();     // vazio
            dto.Fornecedores = new List<ImportarPessoaJuridicaDTO>();
            dto.Oficinas = new List<ImportarPessoaJuridicaDTO>();
            dto.Itens = new List<ImportarItemDTO>();

            var res = _sut.TestValidate(dto);

            res.ShouldHaveValidationErrorFor(x => x.Ativos);
            res.ShouldHaveValidationErrorFor(x => x.Fornecedores);
            res.ShouldHaveValidationErrorFor(x => x.Oficinas);
            res.ShouldHaveValidationErrorFor(x => x.Itens);
        }

        [Fact]
        public void PeloMenosUmaColecaoComItens_Deve_PassarRegraGlobal()
        {
            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: false);
            dto.Itens = new List<ImportarItemDTO> { TesteFactory.CriarItemValido() };

            var res = _sut.TestValidate(dto);
            res.ShouldNotHaveValidationErrorFor(x => x);
        }

        #endregion

        #region Encadeamentos / Aninhados

        [Fact]
        public void Ativos_ComVeiculoInvalido_Deve_PuxarErroDoValidadorVeiculo()
        {
            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: false);
            dto.Ativos = new ImportarAtivosDTO
            {
                Veiculos = new List<ImportarAtivoVeiculoDTO>
                    {
                        new ImportarAtivoVeiculoDTO
                        {
                            Placa = "XYZ1234",
                            Chassi = "5pG M70y0j 80 Af7220",
                            Modelo = "Onix 1.0",
                            Marca = "GM",
                            AnoModelo = 2022,
                            CnpjOficina = "11222333000181"
                        }
                    }
            };

            var res = _sut.TestValidate(dto);

            res.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void Itens_ComValorTotalInconsistente_Deve_ApontarErroDoValidadorItens()
        {
            var item = TesteFactory.CriarItemValido();
            item.ValorTotal = 123.45m; // forçando inconsistência

            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: false);
            dto.Itens = new List<ImportarItemDTO> { item };

            var res = _sut.TestValidate(dto);
            res.ShouldHaveValidationErrorFor(r => r.Itens);
        }

        #endregion

        #region Happy Path

        [Fact]
        public void DTO_ComTudoValido_Deve_Passar()
        {
            var dto = TesteFactory.CriarImportacaValida(incluirAlgumaColecao: true);
            dto.Itens = new List<ImportarItemDTO> { TesteFactory.CriarItemValido() };

            var res = _sut.TestValidate(dto);
            res.ShouldNotHaveAnyValidationErrors();
        }
        #endregion
    }
}
