using AutoMapper;
using Moq;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Application.Services.ZinPag;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Repositorios.ZinPag;

namespace Zin.Application.Tests.Pagamentos
{
    public class PagamentoServiceTests
    {
        private readonly Mock<IAtivoRepository> _ativoRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IPagamentoRepository> _pagamentoRepositoryMock;
        private readonly Mock<IDocumentoRepository> _documentoRepositoryMock;
        private readonly Mock<IPessoaRepository> _pessoaRepositoryMock;
        private readonly Mock<IDocumentoPagamentoRepository> _documentoPagamentoRepositoryMock;
        private readonly Mock<IFilaProcessamentoRepository> _filaProcessamentoRepositoryMock;
        private readonly Mock<ILiquidacaoPagamentoRepository> _liquidacaoPagamentoRepositoryMock;
        private readonly Mock<IProcessosService> _processosServiceMock;
        private readonly Mock<IAgregadorRepository> _agregadorRepositoryMock;
        private readonly Mock<IFilaProcessamentoRepository> _filaProcessamentoRepository;
        private readonly PagamentoService _pagamentoService;
        private readonly Mock<FilaProcessamentoRepository> _fila;

        public PagamentoServiceTests()
        {
            _ativoRepositoryMock = new Mock<IAtivoRepository>();
            _mapperMock = new Mock<IMapper>();
            _pagamentoRepositoryMock = new Mock<IPagamentoRepository>();
            _documentoRepositoryMock = new Mock<IDocumentoRepository>();
            _pessoaRepositoryMock = new Mock<IPessoaRepository>();
            _documentoPagamentoRepositoryMock = new Mock<IDocumentoPagamentoRepository>();
            _liquidacaoPagamentoRepositoryMock = new Mock<ILiquidacaoPagamentoRepository>();
            _processosServiceMock = new Mock<IProcessosService>();
            _filaProcessamentoRepositoryMock = new Mock<IFilaProcessamentoRepository>();
            _agregadorRepositoryMock = new Mock<IAgregadorRepository>();
            _filaProcessamentoRepository = new Mock<IFilaProcessamentoRepository>();

            _pagamentoService = new PagamentoService(
                _pagamentoRepositoryMock.Object,
                _documentoRepositoryMock.Object,
                _pessoaRepositoryMock.Object,
                _documentoPagamentoRepositoryMock.Object,
                _agregadorRepositoryMock.Object,
                _processosServiceMock.Object,
                _filaProcessamentoRepositoryMock.Object,
                _liquidacaoPagamentoRepositoryMock.Object
            );
        }

        [Fact]
        public async Task CriarPagamentoAsync_ComPessoaBeneficiariaValida_RetornaPagamentoId()
        {
            // Arrange
            var dto = new CriaPagamentoDTO
            {
                IdPessoaBeneficiaria = 1,
                IdAgregador = 2,
                ValorTotal = 100.50m,
                FormaPagamento = FormaPagamento.Pix,
                StatusPagamento = StatusPagamento.Pendente,
                ItensVersoesIds = new List<int> { 10, 20 }
            };

            _pagamentoRepositoryMock
                .Setup(r => r.InserirAsync(It.IsAny<Pagamento>()))
                .ReturnsAsync(123);

            // Act
            var result = await _pagamentoService.CriarPagamentoAsync(dto);

            // Assert
            Assert.Equal(123, result);
            _pagamentoRepositoryMock.Verify(r => r.InserirAsync(It.Is<Pagamento>(p =>
                p.IdPessoaBeneficiaria == dto.IdPessoaBeneficiaria &&
                p.IdAgregador == dto.IdAgregador &&
                p.Valor == 0 && // Valor não está sendo setado no método original
                p.StatusPagamento == StatusPagamento.Pendente &&
                p.PagamentoItemVersoes.Count == 2
            )), Times.Once);
        }

        [Fact]
        public async Task CriarPagamentoAsync_ComPessoaBeneficiariaNula_ThrowsArgumentNullException()
        {
            // Arrange
            var dto = new CriaPagamentoDTO
            {
                IdPessoaBeneficiaria = null,
                IdAgregador = 2,
                ValorTotal = 100.50m,
                FormaPagamento = FormaPagamento.Pix,
                StatusPagamento = StatusPagamento.Pendente,
                ItensVersoesIds = new List<int> { 10, 20 }
            };

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _pagamentoService.CriarPagamentoAsync(dto));
        }
    }
}
