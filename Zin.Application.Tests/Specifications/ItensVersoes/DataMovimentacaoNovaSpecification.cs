using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.ValueObject;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Decide se deve criar uma nova versão com base em mudança na Data de Movimentação
    /// para a mesma autorização e fornecedor.
    /// </summary>
    public class DataMovimentacaoNovaSpecification : IItemVersaoSpecification
    {
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> todasVersoes, ImportarItemDTO dto)
        {
            ItemVersaoSpecificationHelper.GarantirIntegridadeDasVersoes(todasVersoes, dto);

            // Se a data de movimento não veio (ou veio default), não há base para “nova versão por movimento”
            if (dto.DataMovimento == default)
                return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(false, null);

            // CNPJ pode lançar se inválido; se preferir “tentar criar”, adapte para um TryCriar
            var cnpjFornecedor = Cnpj.Criar(dto.CnpjFornecedor);

            var ultimaVersaoMesmaAutorizacao = ItemVersaoSpecificationHelper.ObterUltimaVersaoMesmaAutorizacao(
                todasVersoes,
                dto.DataAutorizacao,
                cnpjFornecedor
            );

            var deveCriar = DeveCriarNovaVersaoPorMudancaDeMovimentacao(ultimaVersaoMesmaAutorizacao, dto);

            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(
                deveCriar,
                deveCriar ? ultimaVersaoMesmaAutorizacao : null
            );
        }

        private static bool DeveCriarNovaVersaoPorMudancaDeMovimentacao(ItemVersao? ultima, ImportarItemDTO dto)
        {
            if (ultima is null || ultima.DataHoraAutorizacao is null) return false;
            if (ultima.DataHoraAutorizacao.Value != dto.DataAutorizacao) return false;

            var movimentacaoDiferente = ultima.DataHoraMovimentacao.Value != dto.DataMovimento;

            return movimentacaoDiferente;
        }
    }
}
