using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.ValueObject;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Specification para verificar se é possível criar uma versão de exclusão de um item
    /// </summary>
    public class ExclusaoComAutorizacaoSpecification : IItemVersaoSpecification
    {
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> todasVersoes, ImportarItemDTO dto)
        {
            ItemVersaoSpecificationHelper.GarantirIntegridadeDasVersoes(todasVersoes, dto);

            if (dto.TipoMovimentoItem != TipoMovimentoItem.Exclusao)
                return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(false, null);

            if (todasVersoes is null || todasVersoes.Count == 0)
                throw new InvalidOperationException(ItemVersaoSpecificationHelper.MensagemErroExclusaoSemAutorizacao(dto));

            var cnpj = Cnpj.Criar(dto.CnpjFornecedor);

            var candidatas = ItemVersaoSpecificationHelper
                .ObterTodasVersoesDoFornecedorEAutorizacao(todasVersoes, dto.DataAutorizacao, cnpj);

            if (!(candidatas?.Any() ?? false))
                throw new InvalidOperationException(ItemVersaoSpecificationHelper.MensagemErroExclusaoSemAutorizacao(dto));

            // TODO: (FILIPE) Validar se a exclusão não está repetida
            var versaoMaisAntiga = candidatas
                .Where(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao)
                .OrderBy(v => v.DataCriacao)
                .FirstOrDefault();

            if (versaoMaisAntiga is null)
                throw new InvalidOperationException(ItemVersaoSpecificationHelper.MensagemErroExclusaoSemAutorizacao(dto));

            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(true, versaoMaisAntiga);
        }
    }
}
