using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.ValueObject;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Specification para verificar se a quantidade de um item recebido é a diferente
    /// da última versão com a mesma autorização e fornecedor
    /// devendo ser criada uma nova versão do item.
    /// </summary>
    public class MesmaDataAutorizacaoQuantidadeDiferenteSpecification : IItemVersaoSpecification
    {
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> versoes, ImportarItemDTO dto)
        {
            ItemVersaoSpecificationHelper.GarantirIntegridadeDasVersoes(versoes, dto);

            if (dto.TipoMovimentoItem != TipoMovimentoItem.Autorizacao)
                return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(false, null);

            var cnpjFornecedor = Cnpj.Criar(dto.CnpjFornecedor);
            var ultimaVersao = ItemVersaoSpecificationHelper.ObterUltimaVersaoMesmaAutorizacao(
                versoes,
                dto.DataAutorizacao,
                cnpjFornecedor);

            if (ultimaVersao == null)
                return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(false, null);

            var mudouQuantidade = ultimaVersao.Quantidade != dto.Quantidade;

            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(
                mudouQuantidade, 
                mudouQuantidade ? ultimaVersao : null
            );
        }
    }
}
