using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System.Text;

namespace Zin.Helpers.Security
{
    public static class JwtHelper
    {
        public static SymmetricSecurityKey BuscaChaveJwt(string jwtSecretVarName, IConfigurationSection jwtSettings)
        {
            // Busca o nome da variável de ambiente a partir da configuração
            var secretVariableName = jwtSecretVarName;
            string? secretString = null;

            if (!string.IsNullOrEmpty(secretVariableName))
            {
                secretString = Environment.GetEnvironmentVariable(secretVariableName);
            }

            // Se não encontrou via variável de ambiente, tenta pegar do appsettings normalmente
            if (string.IsNullOrEmpty(secretString))
            {
                secretString = jwtSettings["Secret"];
                Log.Warning("Chave secreta do JWT configurada no appsettings.json. Não recomendado para produção!");
            }

            if (string.IsNullOrEmpty(secretString))
            {
                throw new Exception("Chave Secreta do JWT não está configurada nem na variável de ambiente nem no appsettings.json");
            }

            return new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretString));
        }
    }
}
