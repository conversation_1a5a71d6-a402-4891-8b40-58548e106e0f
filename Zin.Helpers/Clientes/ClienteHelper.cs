using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Serilog;
using Zin.Helpers.Clientes.Models;

namespace Zin.Helpers.Clientes
{
    public static class ClienteHelper
    {
        public static Cliente BuscarClienteSelecionado(HttpContext context)
        {
            if (context.Items.TryGetValue("ClienteSelecionado", out var clienteObj) && clienteObj is Cliente cliente)
            {
                return cliente;
            }

            throw new Exception("Cliente selecionado não encontrado no contexto HTTP. Certifique-se de que o middleware ClienteFromClaimMiddleware foi executado corretamente.");
        }

        public static List<Cliente> ObterClientes(HttpContext context)
        {
            var clientesClaim = context?.User.FindFirst("clientes")?.Value;

            if (string.IsNullOrEmpty(clientesClaim))
            {
                Log.Information("Claim 'clientes' não encontrada no token.");
                return [];
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            };

            return JsonSerializer
                .Deserialize<List<Cliente>>(clientesClaim, options) ?? [];
        }
    }
}
