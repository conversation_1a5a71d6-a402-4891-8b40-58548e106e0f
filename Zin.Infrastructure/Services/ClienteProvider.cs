using Zin.Helpers.Clientes.Models;
using Microsoft.AspNetCore.Http;
using Zin.Helpers.Clientes;

namespace Zin.Infrastructure.Services
{
    public class ClienteProvider(IHttpContextAccessor httpContextAccessor) : IClienteProvider
    {
        private Cliente? _cliente;

        public void SetCliente(Cliente cliente)
        {
            _cliente = cliente;
        }

        public Cliente GetCliente()
        {
            if (_cliente == null)
            {
                return ClienteHelper.BuscarClienteSelecionado(httpContextAccessor.HttpContext!)
                ?? throw new InvalidOperationException("Cliente selecionado não encontrado no contexto HTTP. " +
                    "Certifique-se de que o middleware ClienteFromClaimMiddleware foi executado corretamente.");
            }

            return _cliente;
        }
    }
}
