using Microsoft.Extensions.Configuration;

namespace Zin.Infrastructure.Services
{
    public class ClienteConnectionService(IConfiguration configuration) : IClienteConnectionService
    {
        private readonly IConfiguration _configuration = configuration;

        public string BuscaClienteConnectionString(string nomeBaseDados)
        {
            return _configuration.GetConnectionString($"{nomeBaseDados}")!;
        }
    }
}