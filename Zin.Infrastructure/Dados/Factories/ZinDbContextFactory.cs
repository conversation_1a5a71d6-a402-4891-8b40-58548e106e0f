using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Zin.Helpers.Clientes;

namespace Zin.Infrastructure.Dados.Factories
{
    public class ZinDbContextFactory(
        IHttpContextAccessor httpContextAccessor, 
        IClienteConnectionService clienteConnectionService) : IDbContextFactory<ZinDbContext>
    {
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly IClienteConnectionService _clienteConnectionService = clienteConnectionService;

       public ZinDbContext CreateDbContext()
        {
            var connectionString = "Server=10.7.30.35;port=5433;Database=zin_desenv_cliente2;User ID=zin;Password=*"5o#|_C?4&em;L>Qz5c-I`=%9}#*";";

            var optionsBuilder = new DbContextOptionsBuilder<ZinDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new ZinDbContext(optionsBuilder.Options);
        }
    }
}
