using Microsoft.EntityFrameworkCore;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Entidades.Cadastros.Contatos;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Entidades.Cadastros.Enderecos;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.Importacoes;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;

namespace Zin.Infrastructure.Dados
{
    public class ZinDbContext(DbContextOptions<ZinDbContext> options) : DbContext(options)
    {
        public DbSet<Pessoa> Pessoas { get; set; }
        public DbSet<PessoaFisica> PessoasFisicas { get; set; }
        public DbSet<PessoaJuridica> PessoasJuridicas { get; set; }
        public DbSet<ClienteEmpresa> ClienteEmpresas { get; set; }

        public DbSet<Contato> Contatos { get; set; }
        public DbSet<Banco> Bancos { get; set; }
        public DbSet<DadoBancario> DadosBancarios { get; set; }
        public DbSet<Endereco> Enderecos { get; set; }
        public DbSet<Estado> Estados { get; set; }
        public DbSet<Cidade> Cidades { get; set; }

        public DbSet<PessoaContato> PessoasContatos { get; set; }
        public DbSet<PessoaDadoBancario> PessoasDadosBancarios { get; set; }
        public DbSet<PessoaEndereco> PessoasEnderecos { get; set; }

        // ContatoTelefone foi descontinuada pois agora o Telefone esta diretamente na entidade Contato
        // Deve ser utilizada o MeioDeContato na entidade Contato para definir o tipo do contato (Telefone, Email, etc)
        //public DbSet<ContatoTelefone> ContatosTelefones { get; set; }

        public DbSet<Agregador> Agregadores { get; set; }

        public DbSet<Ativo> Ativos { get; set; }
        public DbSet<Veiculo> Veiculos { get; set; }
        public DbSet<VeiculoOficina> VeiculoOficinas { get; set; }

        public DbSet<Pagamento> Pagamentos { get; set; }
        public DbSet<PagamentoItemVersao> PagamentoItemVersoes { get; set; }
        public DbSet<LiquidacaoPagamento> LiquidacoesPagamentos { get; set; }

        public DbSet<Item> Itens { get; set; }
        public DbSet<ItemVersao> ItensVersoes { get; set; }

        public DbSet<Documento> Documentos { get; set; }
        public DbSet<NotaFiscal> NotasFiscais { get; set; }
        public DbSet<DocumentoPagamento> DocumentoPagamentos { get; set; }
        public DbSet<DocumentoItemVersao> NotasFiscalItemVesrao { get; set; }
        public DbSet<DocumentoItemVersao> NotasFiscalItemVersao { get; set; }

        public DbSet<Ressarcimento> Ressarcimentos { get; set; }
        public DbSet<RessarcimentoItemVersao> RessarcimentosItemVersoes { get; set; }
        public DbSet<LiquidacaoRessarcimento> LiquidacoesRessarcimentos { get; set; }
        public DbSet<DocumentoRessarcimento> DocumentosRessarcimento { get; set; }

        public DbSet<ProcessamentoAgregadorStatus> ProcessamentoStatus { get; set; }
        public DbSet<RegistroProcessamentoItemVersao> RegistroProcessamento { get; set; }

        public DbSet<Movimentacao> Movimentacoes { get; set; }
        public DbSet<MovimentacaoItemVersao> MovimentacoesItensVersoes { get; set; }

        public DbSet<Configuracao> Configuracoes { get; set; }
        public DbSet<Regra> Regras { get; set; }

        public DbSet<ItemVersaoCondicao> ItensVersaoCondicoes { get; set; }
        public DbSet<ItemVersaoDivergencia> ItensVersaoDivergencias { get; set; }
        public DbSet<Divergencia> Divergencias { get; set; }
        public DbSet<Condicao> Condicoes { get; set; }
        public DbSet<FilaProcessamento> FilasProcessamento { get; set; }
        public DbSet<FilaProcessamentoHistorico> FilasProcessamentoHistorico { get; set; }
        public DbSet<ProcessamentoDashboard> ProcessamentoDashboard { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.HasDefaultSchema("public");

            //lacionamentos muitos-para - muitos sem entidade de junção explícita
            modelBuilder.Entity<Agregador>()
                .HasMany(a => a.Ativos)
                .WithMany(b => b.Agregadores)
                .UsingEntity<Dictionary<string, object>>(
                    "agregadores_ativos",
                    j => j
                        .HasOne<Ativo>()
                        .WithMany()
                        .HasForeignKey("id_ativo")
                        .HasConstraintName("FK_agregadores_ativos_ativos_id_ativo"),
                    j => j
                        .HasOne<Agregador>()
                        .WithMany()
                        .HasForeignKey("id_agregador")
                        .HasConstraintName("FK_agregadores_ativos_agregadores_id_agregador"),
                    j =>
                    {
                        j.HasKey("id_agregador", "id_ativo");
                        j.ToTable("agregadores_ativos", "zinpag");
                    }
                );

            // Chaves compostas
            modelBuilder.Entity<PessoaEndereco>()
                .HasKey(pe => new { pe.IdPessoa, pe.IdEndereco });
            modelBuilder.Entity<PessoaDadoBancario>()
                .HasKey(pd => new { pd.IdPessoa, pd.IdDadoBancario });
            modelBuilder.Entity<PessoaContato>()
                .HasKey(pc => new { pc.IdPessoa, pc.IdContato });

            modelBuilder.Entity<ItemVersaoCondicao>()
                .HasOne(x => x.ItemVersao)
                .WithMany(x => x.Condicoes)
                .HasForeignKey(x => x.IdItemVersao);

            modelBuilder.Entity<ItemVersaoCondicao>()
                .HasOne(x => x.Condicao)
                .WithMany(x => x.ItensVersao)
                .HasForeignKey(x => x.IdCondicao);

            modelBuilder.Entity<ItemVersaoDivergencia>()
                .HasOne(x => x.ItemVersao)
                .WithMany(x => x.Divergencias)
                .HasForeignKey(x => x.IdItemVersao);

            modelBuilder.Entity<ItemVersaoDivergencia>()
                .HasOne(x => x.Divergencia)
                .WithMany(x => x.ItensVersao)
                .HasForeignKey(x => x.IdDivergencia);

            modelBuilder.Entity<MovimentacaoItemVersao>()
                .HasOne(iv => iv.ItemVersao)
                .WithMany()
                .HasForeignKey(iv => iv.IdItemVersao)
                .HasConstraintName("fk_movitemversao_itemversao"); // nome opcional do FK

            modelBuilder.Entity<MovimentacaoItemVersao>()
                .Property(iv => iv.IdItemVersao)
                .HasColumnName("id_item_versao");

            modelBuilder.Entity<ProcessamentoDashboard>()
                .Property(e => e.Dados)
                .HasColumnType("jsonb");
        }
    }
}