using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class LiquidacaoRessarcimentoRepository(IUnitOfWork unitOfWork) : ILiquidacaoRessarcimentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(LiquidacaoRessarcimento entidade)
        {
            Context.LiquidacoesRessarcimentos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<LiquidacaoRessarcimento>> BuscarAsync(Expression<Func<LiquidacaoRessarcimento, bool>> predicado)
        {
            return await Context.LiquidacoesRessarcimentos.Where(predicado).ToListAsync();
        }

        public async Task<LiquidacaoRessarcimento?> BuscarPorIdAsync(int id)
        {
            return await Context.LiquidacoesRessarcimentos.FindAsync(id).AsTask();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<LiquidacaoRessarcimento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(LiquidacaoRessarcimento entidade)
        {
            await Context.LiquidacoesRessarcimentos.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<LiquidacaoRessarcimento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<LiquidacaoRessarcimento>> ListarAsync()
        {
            return await Context.LiquidacoesRessarcimentos.ToListAsync();
        }

        public async Task<LiquidacaoRessarcimento?> BuscarLiquidacaoComRessarcimentoEDocumentoAsync(int id)
        {
            return await Context.LiquidacoesRessarcimentos
                .Include(lr => lr.Ressarcimento)
                    .ThenInclude(r => r.DocumentoRessarcimentos)
                        .ThenInclude(dr => dr.Documento)
                .FirstOrDefaultAsync(lr => lr.Id == id);
        }
    }
}
