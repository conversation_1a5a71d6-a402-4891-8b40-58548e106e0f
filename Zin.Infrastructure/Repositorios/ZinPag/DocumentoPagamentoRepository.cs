using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class DocumentoPagamentoRepository(IUnitOfWork unitOfWork) : IDocumentoPagamentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;

        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(DocumentoPagamento entidade)
        {
            Context.DocumentoPagamentos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public Task<IEnumerable<DocumentoPagamento>> BuscarAsync(Expression<Func<DocumentoPagamento, bool>> predicado)
        {
            throw new NotImplementedException();
        }

        public Task<DocumentoPagamento?> BuscarPorIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<DocumentoPagamento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(DocumentoPagamento entidade)
        {
            await Context.DocumentoPagamentos.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<DocumentoPagamento> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<DocumentoPagamento>> ListarAsync()
        {
            throw new NotImplementedException();
        }
    }
}
