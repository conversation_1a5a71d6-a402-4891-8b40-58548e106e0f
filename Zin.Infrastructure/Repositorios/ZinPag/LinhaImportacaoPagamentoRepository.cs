using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag;

public class LinhaImportacaoPagamentoRepository : ILinhaImportacaoPagamentoRepository
{
    private readonly IUnitOfWork _unitOfWork;
    private ZinDbContext Context => _unitOfWork.Context;

    public LinhaImportacaoPagamentoRepository(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<int> InserirAsync(LinhaImportacaoPagamento entidade)
    {
        await Context.LinhasImportacaoPagamentos.AddAsync(entidade);
        await _unitOfWork.CommitAsync();
        return entidade.Id;
    }

    public async Task AtualizarAsync(LinhaImportacaoPagamento entidade)
    {
        Context.LinhasImportacaoPagamentos.Update(entidade);
        await _unitOfWork.CommitAsync();
    }

    public async Task<LinhaImportacaoPagamento> BuscarPorIdAsync(int id)
    {
        return await Context.LinhasImportacaoPagamentos.FindAsync(id)
            ?? throw new Exception($"LinhaImportacaoPagamento com ID {id} não encontrada.");
    }

    public async Task<IEnumerable<LinhaImportacaoPagamento>> BuscarAsync(Expression<Func<LinhaImportacaoPagamento, bool>> predicado)
    {
        return await Context.LinhasImportacaoPagamentos.Where(predicado).ToListAsync();
    }

    public async Task<IEnumerable<LinhaImportacaoPagamento>> ListarAsync()
    {
        return await Context.LinhasImportacaoPagamentos.ToListAsync();
    }

    public async Task<IEnumerable<LinhaImportacaoPagamento>> BuscarPorLoteAsync(int idLote)
    {
        return await Context.LinhasImportacaoPagamentos
            .Where(l => l.IdLoteImportacao == idLote)
            .OrderBy(l => l.NumeroLinha)
            .ToListAsync();
    }

    public async Task<IEnumerable<LinhaImportacaoPagamento>> BuscarPaginadoPorLoteAsync(
        int idLote,
        int page,
        int pageSize,
        StatusLinhaPagamento? status = null,
        OperacaoLinhaPagamento? operacao = null)
    {
        var query = Context.LinhasImportacaoPagamentos
            .Where(l => l.IdLoteImportacao == idLote);

        query = AplicarFiltros(query, status, operacao);

        return await query
            .OrderBy(l => l.NumeroLinha)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<int> ContarPorLoteAsync(
        int idLote,
        StatusLinhaPagamento? status = null,
        OperacaoLinhaPagamento? operacao = null)
    {
        var query = Context.LinhasImportacaoPagamentos
            .Where(l => l.IdLoteImportacao == idLote);

        query = AplicarFiltros(query, status, operacao);

        return await query.CountAsync();
    }

    public async Task<IEnumerable<LinhaImportacaoPagamento>> BuscarLinhasParaReprocessamentoAsync(int idLote)
    {
        return await Context.LinhasImportacaoPagamentos
            .Where(l => l.IdLoteImportacao == idLote && 
                       (l.Status == StatusLinhaPagamento.Erro || l.OperacaoAplicada == OperacaoLinhaPagamento.Ignorado))
            .OrderBy(l => l.NumeroLinha)
            .ToListAsync();
    }

    public async Task<LinhaImportacaoPagamento?> BuscarPorHashAsync(string hash)
    {
        return await Context.LinhasImportacaoPagamentos
            .FirstOrDefaultAsync(l => l.HashLinha == hash);
    }

    public async Task<bool> ExisteHashAsync(string hash)
    {
        return await Context.LinhasImportacaoPagamentos
            .AnyAsync(l => l.HashLinha == hash);
    }

    private static IQueryable<LinhaImportacaoPagamento> AplicarFiltros(
        IQueryable<LinhaImportacaoPagamento> query,
        StatusLinhaPagamento? status,
        OperacaoLinhaPagamento? operacao)
    {
        if (status.HasValue)
            query = query.Where(l => l.Status == status.Value);

        if (operacao.HasValue)
            query = query.Where(l => l.OperacaoAplicada == operacao.Value);

        return query;
    }

    public Task DeletarAsync(int id)
    {
        throw new NotImplementedException();
    }

    public Task DeletarVariosAsync(IEnumerable<LinhaImportacaoPagamento> entidades)
    {
        throw new NotImplementedException();
    }

    public Task<int[]> InserirVariosAsync(IEnumerable<LinhaImportacaoPagamento> entidades)
    {
        throw new NotImplementedException();
    }
}
