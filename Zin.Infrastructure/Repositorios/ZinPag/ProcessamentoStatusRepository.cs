using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class ProcessamentoStatusRepository(IUnitOfWork unitOfWork) : IProcessamentoStatusRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;
        public async Task AtualizarStatusAsync(ProcessamentoAgregadorStatus processamentoStatus)
        {
            Context.ProcessamentoStatus.Update(processamentoStatus);
            await _unitOfWork.CommitAsync();
        }
        public async Task<IList<ProcessamentoAgregadorStatus>> BuscarPorAgregadorIdAsync(int idAgregador)
        {
            return await Context.ProcessamentoStatus
                .Where(ps => ps.IdAgregador == idAgregador)
                .ToListAsync();
        }
        public async Task<ProcessamentoAgregadorStatus?> BuscarPorAgregadorTipoAsync(int idAgregador, TipoProcessamento tipo)
        {
            return await Context.ProcessamentoStatus
                .FirstOrDefaultAsync(ps => ps.IdAgregador == idAgregador && ps.Tipo == tipo);
        }
        public async Task InserirAsync(ProcessamentoAgregadorStatus entidade)
        {
            Context.ProcessamentoStatus.Add(entidade);
            await _unitOfWork.CommitAsync();
        }
    }
}
