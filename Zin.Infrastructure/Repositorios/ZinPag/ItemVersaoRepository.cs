using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class ItemVersaoRepository(IUnitOfWork unitOfWork) : IItemVersaoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task<IEnumerable<ItemVersao>> ListarAsync()
        {
            return await Context.ItensVersoes
                .Include(iv => iv.Item)
                .Include(iv => iv.PessoaFornecedora)
                .Include(iv => iv.Documento)
                .ToListAsync();
        }

        public async Task<ItemVersao?> BuscarPorIdAsync(int id)
        {
            return await Context.ItensVersoes
                .Include(iv => iv.Item)
                .Include(iv => iv.PessoaFornecedora)
                .Include(iv => iv.Documento)
                .FirstOrDefaultAsync(iv => iv.Id == id)
                ?? throw new EntidadeNaoEncontradaExcecao($"ItemVersao com ID {id} não encontrado.");
        }

        public async Task<IEnumerable<ItemVersao>> BuscarAsync(Expression<Func<ItemVersao, bool>> predicado)
        {
            return await Context.ItensVersoes
                .Where(predicado)
                .ToListAsync();
        }

        public async Task<IEnumerable<ItemVersao>> BuscarPorItemIdAsync(int idItem)
        {
            return await Context.ItensVersoes
                .Where(iv => iv.IdItem == idItem)
                .Include(iv => iv.Item)
                .Include(iv => iv.PessoaFornecedora)
                .Include(iv => iv.Documento)
                .ToListAsync();
        }


        public async Task<int> InserirAsync(ItemVersao entidade)
        {
            await Context.ItensVersoes.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public async Task AtualizarAsync(ItemVersao entidade)
        {
            Context.ItensVersoes.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeletarAsync(int id)
        {
            var entidade = await Context.ItensVersoes.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"ItemVersao com ID {id} não encontrado.");

            Context.ItensVersoes.Remove(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<ItemVersao> entidades)
        {
            await Context.ItensVersoes.AddRangeAsync(entidades);
            var result = await _unitOfWork.CommitAsync();
            return entidades.Select(e => e.Id).ToArray();
        }

        public async Task DeletarVariosAsync(IEnumerable<ItemVersao> entidades)
        {
            Context.ItensVersoes.RemoveRange(entidades);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<ItemVersao>> BuscarPorAgregadorIdAsync(int idAgregador)
        {
            return await Context.ItensVersoes
                .Include(iv => iv.Item)
                .Include(iv => iv.PessoaFornecedora)
                .Include(iv => iv.Documento)
                .Where(iv =>
                    (iv.Item != null && iv.Item.IdAgregador == idAgregador) ||
                    (iv.Documento != null && iv.Documento.IdAgregador == idAgregador)
                )
                .ToListAsync();
        }
    }
}
