using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class PagamentoRepository(IUnitOfWork unitOfWork) : IPagamentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;

        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(Pagamento entidade)
        {
            Context.Pagamentos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Pagamento>> BuscarPagamentosEmAbertoPorAgregadorAsync(int idAgregador)
        {
            return await Context.Pagamentos
                .Where(p => p.IdAgregador == idAgregador && p.StatusPagamento == StatusPagamento.Pendente)
                .ToListAsync();
        }

        public async Task<IEnumerable<Pagamento>> BuscarAsync(Expression<Func<Pagamento, bool>> predicado)
        {
            return await Context.Pagamentos.Where(predicado).ToListAsync();
        }

        public async Task<Pagamento?> BuscarPorIdAsync(int id)
        {
            return await Context.Pagamentos.FindAsync(id)
                .AsTask() ?? throw new Exception($"Pagamento com ID {id} não encontrado.");
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<Pagamento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(Pagamento entidade)
        {
            await Context.Pagamentos.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<Pagamento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<Pagamento?> ObterPorDocumentoIdAsync(int documentoId)
        {
            return await Context.Pagamentos
                .Include(p => p.LiquidacoesPagamentos)
                .FirstOrDefaultAsync(p => p.DocumentoPagamentos.Any(dp => dp.IdDocumento == documentoId));
        }

        public async Task<IEnumerable<Pagamento>> ListarAsync()
        {
            return await Context.Pagamentos
                .Include(p => p.LiquidacoesPagamentos)
                .ToListAsync();
        }

        public async Task<IEnumerable<Pagamento>> BuscarPagamentosPorItemVersaoIdAsync(int idItemVersao)
        {          
            var pagamentos = await Context.PagamentoItemVersoes
                .Where(piv => piv.IdItemVersao == idItemVersao)
                .Select(piv => piv.Pagamento)
                .Where(p => p != null)
                .ToListAsync();

            return pagamentos!;
        }

        public async Task<IEnumerable<Pagamento>> BuscarPorAgregadorIdAsync(int agregadorId)
        {
            return await Context.Pagamentos
                .Where(p => p.IdAgregador == agregadorId)
                .ToListAsync();
        }
    }
}
