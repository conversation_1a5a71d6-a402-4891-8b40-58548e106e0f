using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class MovimentacoesRepository(IUnitOfWork unitOfWork) : IMovimentacoesRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext _context => _unitOfWork.Context;

        public async Task<Movimentacao?> BuscarPorIdAsync(Guid id)
        {
            return await _context.Set<Movimentacao>()
                .Include(m => m.Agregador)
                .Include(m => m.MovimentacoesItensVersoes)
                .Include(m => m.Agregador)
                .FirstOrDefaultAsync(m => m.Id == id);
        }
        public async Task<Movimentacao?> BuscarAsync(Expression<Func<Movimentacao, bool>> predicate)
        {
            return await _context.Set<Movimentacao>()
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(iv => iv.ItemVersao)
                .Include(m => m.Documentos)
                .FirstOrDefaultAsync(predicate);
        }

        public async Task<List<Movimentacao>> BuscarMovimentacoesPorItemVersaoAsync(int idItemVersao)
        {
            return await _context.Set<Movimentacao>()
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(iv => iv.ItemVersao)
                .Where(m => m.MovimentacoesItensVersoes.Any(iv => iv.IdItemVersao == idItemVersao))
                .ToListAsync();
        }

        public async Task<List<Movimentacao>> BuscarMovimentacoesPorItemVersaoEAgrupamentoAsync(int idItemVersao, bool agrupamentoPorData)
        {
            return await _context.Set<Movimentacao>()
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(iv => iv.ItemVersao)
                .Where(m =>
                    m.MovimentacoesItensVersoes.Any(iv => iv.IdItemVersao == idItemVersao && iv.Ativo) &&
                    ((agrupamentoPorData && m.Documentos.Count == 0) ||
                    (!agrupamentoPorData && m.Documentos.Count > 0)))
                .ToListAsync();
        }

        public async Task<Movimentacao?> BuscarMovimentacaoPorDocumento(int idDocumento)
        {
            return await _context.Set<Movimentacao>()
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(iv => iv.ItemVersao)
                .Include(m => m.Documentos)
                .FirstOrDefaultAsync(m => m.Documentos.Any(d => d.Id == idDocumento));
        }

        public async Task<Movimentacao?> BuscarMovimentacaoAtivaPorDocumento(int idDocumento)
        {
            return await _context.Set<Movimentacao>()
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(iv => iv.ItemVersao)
                .Include(m => m.Documentos)
                .FirstOrDefaultAsync(m => m.Documentos.Any(d => d.Id == idDocumento) && m.Ativo);
        }

        public async Task AtualizarAsync(Movimentacao movimentacao)
        {
            _context.Set<Movimentacao>().Update(movimentacao);
            await _context.SaveChangesAsync();
        }

        public async Task InserirAsync(Movimentacao movimentacao)
        {
            await _context.Set<Movimentacao>().AddAsync(movimentacao);
            await _context.SaveChangesAsync();
        }

        public async Task<Movimentacao?> BuscarDetalhesMovimentacaoAsync(Guid id)
        {
            return await _context.Set<Movimentacao>()
                .Include(m => m.Agregador)
                    .ThenInclude(a => a.Ativos)
                        .ThenInclude(a => (a as Veiculo).Oficinas)
                            .ThenInclude(vo => vo.Oficina)
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(miv => miv.ItemVersao)
                        .ThenInclude(iv => iv.Pagamentos)
                            .ThenInclude(piv => piv.Pagamento)
                                .ThenInclude(p => p.LiquidacoesPagamentos)
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(miv => miv.ItemVersao)
                        .ThenInclude(iv => iv.Pagamentos)
                            .ThenInclude(piv => piv.Pagamento)
                                .ThenInclude(p => p.DocumentoPagamentos)
                                    .ThenInclude(dp => dp.Documento)
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(miv => miv.ItemVersao)
                        .ThenInclude(iv => iv.Ressarcimentos)
                            .ThenInclude(riv => riv.Ressarcimento)
                                .ThenInclude(r => r.LiquidacoesRessarcimentos)
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(miv => miv.ItemVersao)
                        .ThenInclude(iv => iv.Ressarcimentos)
                            .ThenInclude(riv => riv.Ressarcimento)
                                .ThenInclude(r => r.DocumentoRessarcimentos)
                                    .ThenInclude(dr => dr.Documento)
                .FirstOrDefaultAsync(m => m.Id == id);
        }

        public async Task<bool> ExistemMovimentacoesPorAgregadorExcluindoMovimentacaoAsync(int idAgregador, Guid movIdExcluir)
        {
            return await _context.Set<Movimentacao>()
                .AnyAsync(m => m.IdAgregador == idAgregador && m.Id != movIdExcluir);
        }

        public Task<List<Movimentacao>> BuscarVariosAsync(Expression<Func<Movimentacao, bool>> predicate)
        {
            return _context.Set<Movimentacao>()
                .Include(m => m.Agregador)
                    .ThenInclude(a => a.Ativos)
                .Include(m => m.MovimentacoesItensVersoes)
                    .ThenInclude(miv => miv.ItemVersao)
                        .ThenInclude(iv => iv.PessoaFornecedora)
                .Where(predicate)
                .ToListAsync();
        }
    }
}
