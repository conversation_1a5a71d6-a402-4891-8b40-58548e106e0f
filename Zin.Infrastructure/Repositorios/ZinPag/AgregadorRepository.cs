using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class AgregadorRepository(IUnitOfWork unitOfWork) : IAgregadorRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(Agregador entidade)
        {
            Context.Agregadores.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Agregador>> BuscarAsync(Expression<Func<Agregador, bool>> predicado)
        {
            return await Context.Agregadores.Where(predicado).ToListAsync();
        }

        public async Task<Agregador?> BuscarPorIdAsync(int id)
        {
            return await Context.Agregadores.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Agregador com ID {id} n�o encontrado.");
        }
        
        public async Task<Agregador?> BuscarPorNumero(string numero)
        {
            return await Context.Agregadores.FirstOrDefaultAsync(a => a.Numero == numero);
        }

        public async Task DeletarAsync(int id)
        {
            //var possuiPagamentos = await Context.Pagamentos.AnyAsync(p => p.IdAgregador == id);
            //if (possuiPagamentos)
            //    throw new InvalidOperationException("N�o � poss�vel excluir o agregador pois existem pagamentos vinculados.");

            var entidade = await Context.Agregadores.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Agregador com ID {id} n�o encontrado.");

            Context.Agregadores.Remove(entidade);
            await _unitOfWork.CommitAsync();
        }


        public async Task DeletarVariosAsync(IEnumerable<Agregador> entidades)
        {
            Context.Agregadores.RemoveRange(entidades);
            await _unitOfWork.CommitAsync();
        }

        public async Task<int> InserirAsync(Agregador entidade)
        {
            await Context.Agregadores.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<Agregador> entidades)
        {
            await Context.Agregadores.AddRangeAsync(entidades);
            var result = await _unitOfWork.CommitAsync();
            return [.. entidades.Select(e => e.Id)];
        }

        public async Task<IEnumerable<Agregador>> ListarAsync()
        {
            return await Context.Agregadores.ToListAsync();
        }

        public async Task<Agregador?> BuscarPorNumeroAsync(string numeroAgregador)
        {
            return await Context.Agregadores
                .FirstOrDefaultAsync(a => a.Numero == numeroAgregador);
        }

        public async Task<IEnumerable<Agregador>> BuscarPorStatusAsync(string status)
        {
            return await Context.Agregadores
                .Where(a => EF.Property<string>(a, "Status") == status)
                .ToListAsync();
        }

        public async Task AtualizarStatusAsync(int idAgregador, StatusProcessamento status)
        {
            var agregador = await Context.Agregadores.FindAsync(idAgregador)
                ?? throw new EntidadeNaoEncontradaExcecao($"Agregador com ID {idAgregador} n�o encontrado.");

            agregador.StatusProcessamento = status;
            Context.Agregadores.Update(agregador);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Agregador>> BuscarPorStatusAsync(StatusProcessamento status)
        {
            return await Context.Agregadores
                .Where(a => a.StatusProcessamento == status)
                .Include(a => a.Ativos)
                .Include(a => a.Itens)
                .Include(a => a.Documentos)
                .Include(a => a.ClienteEmpresa)
                .ToListAsync();
        }
    }
}
