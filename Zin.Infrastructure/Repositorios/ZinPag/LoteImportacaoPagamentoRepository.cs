using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag;

public class LoteImportacaoPagamentoRepository : ILoteImportacaoPagamentoRepository
{
    private readonly IUnitOfWork _unitOfWork;
    private ZinDbContext Context => _unitOfWork.Context;

    public LoteImportacaoPagamentoRepository(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<int> InserirAsync(LoteImportacaoPagamento entidade)
    {
        await Context.LotesImportacaoPagamentos.AddAsync(entidade);
        await _unitOfWork.CommitAsync();
        return entidade.Id;
    }

    public async Task AtualizarAsync(LoteImportacaoPagamento entidade)
    {
        Context.LotesImportacaoPagamentos.Update(entidade);
        await _unitOfWork.CommitAsync();
    }

    public async Task<LoteImportacaoPagamento> BuscarPorIdAsync(int id)
    {
        return await Context.LotesImportacaoPagamentos.FindAsync(id)
            ?? throw new Exception($"LoteImportacaoPagamento com ID {id} não encontrado.");
    }

    public async Task<IEnumerable<LoteImportacaoPagamento>> BuscarAsync(Expression<Func<LoteImportacaoPagamento, bool>> predicado)
    {
        return await Context.LotesImportacaoPagamentos.Where(predicado).ToListAsync();
    }

    public async Task<IEnumerable<LoteImportacaoPagamento>> ListarAsync()
    {
        return await Context.LotesImportacaoPagamentos.ToListAsync();
    }

    public async Task<IEnumerable<LoteImportacaoPagamento>> BuscarPorClienteAsync(
        string idCliente,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null)
    {
        var query = Context.LotesImportacaoPagamentos
            .Where(l => l.IdCliente == idCliente);

        query = AplicarFiltros(query, status, dataInicio, dataFim, usuario);

        return await query
            .OrderByDescending(l => l.DataProcessamento)
            .ToListAsync();
    }

    public async Task<LoteImportacaoPagamento?> BuscarComLinhasPorIdAsync(int id, string idCliente)
    {
        return await Context.LotesImportacaoPagamentos
            .Include(l => l.Linhas)
            .FirstOrDefaultAsync(l => l.Id == id && l.IdCliente == idCliente);
    }

    public async Task<int> ContarPorClienteAsync(
        string idCliente,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null)
    {
        var query = Context.LotesImportacaoPagamentos
            .Where(l => l.IdCliente == idCliente);

        query = AplicarFiltros(query, status, dataInicio, dataFim, usuario);

        return await query.CountAsync();
    }

    public async Task<IEnumerable<LoteImportacaoPagamento>> BuscarPaginadoPorClienteAsync(
        string idCliente,
        int page,
        int pageSize,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null)
    {
        var query = Context.LotesImportacaoPagamentos
            .Where(l => l.IdCliente == idCliente);

        query = AplicarFiltros(query, status, dataInicio, dataFim, usuario);

        return await query
            .OrderByDescending(l => l.DataProcessamento)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    private static IQueryable<LoteImportacaoPagamento> AplicarFiltros(
        IQueryable<LoteImportacaoPagamento> query,
        StatusLoteImportacao? status,
        DateTime? dataInicio,
        DateTime? dataFim,
        string? usuario)
    {
        if (status.HasValue)
            query = query.Where(l => l.Status == status.Value);

        if (dataInicio.HasValue)
            query = query.Where(l => l.DataProcessamento >= dataInicio.Value);

        if (dataFim.HasValue)
            query = query.Where(l => l.DataProcessamento <= dataFim.Value);

        if (!string.IsNullOrEmpty(usuario))
            query = query.Where(l => l.UsuarioProcessamento.Contains(usuario));

        return query;
    }

    public Task DeletarAsync(int id)
    {
        throw new NotImplementedException();
    }

    public Task DeletarVariosAsync(IEnumerable<LoteImportacaoPagamento> entidades)
    {
        throw new NotImplementedException();
    }

    public Task<int[]> InserirVariosAsync(IEnumerable<LoteImportacaoPagamento> entidades)
    {
        throw new NotImplementedException();
    }
}
