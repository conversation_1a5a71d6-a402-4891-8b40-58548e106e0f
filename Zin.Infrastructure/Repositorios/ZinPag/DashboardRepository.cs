using System;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Repositorios;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class DashboardRepository(IUnitOfWork unitOfWork) : IDashboardRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;

        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(ProcessamentoDashboard entidade)
        {
            Context.ProcessamentoDashboard.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<ProcessamentoDashboard>> BuscarAsync(Expression<Func<ProcessamentoDashboard, bool>> predicado)
        {
            return await Context.ProcessamentoDashboard.Where(predicado).ToListAsync();
        }

        public async Task<ProcessamentoDashboard?> BuscarPorIdAsync(int id)
        {
            return await Context.ProcessamentoDashboard.FindAsync(id)
                .AsTask() ?? throw new Exception($"Dashboard com ID {id} não encontrado.");
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<ProcessamentoDashboard> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<int> InserirAsync(ProcessamentoDashboard entidade)
        {
            throw new NotImplementedException();
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<ProcessamentoDashboard> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<ProcessamentoDashboard>> ListarAsync()
        {
            return await Context.ProcessamentoDashboard.ToListAsync();
        }
    }
}
