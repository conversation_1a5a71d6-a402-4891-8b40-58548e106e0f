using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class DivergenciaRepository(IUnitOfWork unitOfWork) : IDivergenciaRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext _context => _unitOfWork.Context;

        public async Task<Divergencia?> BuscarPorIdAsync(Guid id)
        {
            return await _context.Divergencias.FindAsync(id);
        }

        public async Task AtualizarAsync(Divergencia divergencia)
        {
            _context.Divergencias.Update(divergencia);
            await _context.SaveChangesAsync();
        }
    }
}
