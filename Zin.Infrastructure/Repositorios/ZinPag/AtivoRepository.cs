using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class AtivoRepository(IUnitOfWork unitOfWork) : IAtivoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;
        public async Task AtualizarAsync(Ativo entidade)
        {
            Context.Ativos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Ativo>> BuscarAsync(Expression<Func<Ativo, bool>> predicado)
        {
            return await Context.Ativos.Where(predicado).ToListAsync();
        }

        public async Task<IList<Ativo>> BuscarPorAgregadorAsync(int agregadorId)
        {
            return await Context.Ativos
                .Where(a => a.Id == agregadorId)
                .ToListAsync();
        }

        public async Task<Ativo?> BuscarPorIdAsync(int id)
        {
            return await Context.Ativos.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Ativo com ID {id} não encontrado.");
        }

        public async Task DeletarAsync(int id)
        {
            var entidade = await Context.Ativos.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Ativo com ID {id} não encontrado.");
        }

        public async Task DeletarVariosAsync(IEnumerable<Ativo> entidades)
        {
            Context.Ativos.RemoveRange(entidades);
            await _unitOfWork.CommitAsync();
        }

        public async Task<int> InserirAsync(Ativo entidade)
        {
            await Context.Ativos.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<Ativo> entidades)
        {
            await Context.Ativos.AddRangeAsync(entidades); 
            var result = await _unitOfWork.CommitAsync();
            return [.. entidades.Select(e => e.Id)];
        }

        public async Task<IEnumerable<Ativo>> ListarAsync()
        {
            return await Context.Ativos.ToListAsync();
        }
    }
}

