using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class RessarcimentoItemVersaoRepository(IUnitOfWork unitOfWork) : IRessarcimentoItemVersaoRepository
    {
        public Task AtualizarAsync(RessarcimentoItemVersao entidade)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<RessarcimentoItemVersao>> BuscarAsync(Expression<Func<RessarcimentoItemVersao, bool>> predicado)
        {
            throw new NotImplementedException();
        }

        public Task<RessarcimentoItemVersao?> BuscarPorIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<RessarcimentoItemVersao> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(RessarcimentoItemVersao entidade)
        {
            if (entidade == null)
                throw new ArgumentNullException(nameof(entidade));

            await unitOfWork.Context.RessarcimentosItemVersoes.AddAsync(entidade);
            await unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<RessarcimentoItemVersao> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<RessarcimentoItemVersao>> ListarAsync()
        {
            throw new NotImplementedException();
        }
    }
}
