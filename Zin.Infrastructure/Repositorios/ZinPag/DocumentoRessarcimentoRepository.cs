using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class DocumentoRessarcimentoRepository(IUnitOfWork unitOfWork) : IDocumentoRessarcimentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(DocumentoRessarcimento entidade)
        {
            Context.DocumentosRessarcimento.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<DocumentoRessarcimento>> BuscarAsync(Expression<Func<DocumentoRessarcimento, bool>> predicado)
        {
            return await Context.DocumentosRessarcimento.Where(predicado).ToListAsync();
        }

        public async Task<DocumentoRessarcimento?> BuscarPorIdAsync(int id)
        {
            return await Context.DocumentosRessarcimento.FindAsync(id).AsTask();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<DocumentoRessarcimento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(DocumentoRessarcimento entidade)
        {
            await Context.DocumentosRessarcimento.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<DocumentoRessarcimento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<DocumentoRessarcimento>> ListarAsync()
        {
            return await Context.DocumentosRessarcimento.ToListAsync();
        }
    }
}
