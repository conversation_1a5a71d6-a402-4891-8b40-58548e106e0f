using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class FilaProcessamentoHistoricoRepository(IUnitOfWork unitOfWork) : IFilaProcessamentoHistoricoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext _context => _unitOfWork.Context;

        public async Task<FilaProcessamentoHistorico?> BuscarPorIdAsync(Guid id)
        {
            return await _context.FilasProcessamentoHistorico
                .Include(f => f.Item)
                .Include(f => f.Agregador)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<List<FilaProcessamentoHistorico>> ListarTodosAsync()
        {
            return await _context.FilasProcessamentoHistorico
                .Include(f => f.Item)
                .Include(f => f.Agregador)
                .ToListAsync();
        }

        public async Task InserirAsync(FilaProcessamentoHistorico fila)
        {
            _context.FilasProcessamentoHistorico.Add(fila);
            await _context.SaveChangesAsync();
        }

        public async Task AtualizarAsync(FilaProcessamentoHistorico fila)
        {
            _context.FilasProcessamentoHistorico.Update(fila);
            await _context.SaveChangesAsync();
        }
    }
}
