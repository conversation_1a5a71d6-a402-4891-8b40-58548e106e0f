using Microsoft.EntityFrameworkCore;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class RegistroProcessamentoRepository(IUnitOfWork unitOfWork) : IRegistroProcessamentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task<RegistroProcessamentoItemVersao?> BuscarPorItemVersaoIdAsync(int idItemVersao)
        {
            return await Context.RegistroProcessamento
                .FirstOrDefaultAsync(r => r.IdItemVersao == idItemVersao);
        }

        public async Task<IEnumerable<RegistroProcessamentoItemVersao>> BuscarPorItensVersaoAsync(IEnumerable<int> idsItensVersao)
        {
            return await Context.RegistroProcessamento
                .Where(r => idsItensVersao.Contains(r.IdItemVersao))
                .ToListAsync();
        }

        public async Task InserirAsync(RegistroProcessamentoItemVersao registro)
        {
            await Context.RegistroProcessamento.AddAsync(registro);
            await Context.SaveChangesAsync();
        }

        public async Task AtualizarAsync(RegistroProcessamentoItemVersao registro)
        {
            Context.RegistroProcessamento.Update(registro);
            await Context.SaveChangesAsync();
        }

        public async Task<RegistroProcessamentoItemVersao?> BuscarPorItemVersaoTipoAsync(int idItemVersao, TipoProcessamento tipoProcessamento)
        {
            return await Context.RegistroProcessamento
                .FirstOrDefaultAsync(r => r.IdItemVersao == idItemVersao && r.Tipo == tipoProcessamento);
        }
    }
}