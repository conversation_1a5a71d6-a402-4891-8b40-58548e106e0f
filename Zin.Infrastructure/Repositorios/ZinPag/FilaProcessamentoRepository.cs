using Microsoft.EntityFrameworkCore;
using System.Linq;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class FilaProcessamentoRepository(IUnitOfWork unitOfWork): IFilaProcessamentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext _context => _unitOfWork.Context;

        public async Task<FilaProcessamento?> BuscarPorIdAsync(Guid id)
        {
            return await _context.FilasProcessamento
                .Include(f => f.Item)
                .Include(f => f.Agregador)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<List<FilaProcessamento>> ListarTodosAsync()
        {
            return await _context.FilasProcessamento
                .Include(f => f.Item)
                .Include(f => f.Agregador)
                .ToListAsync();
        }

        public async Task<List<FilaProcessamento>> ListarPorStatusAsync(params StatusFilaProcessamento?[] status)
        {
            return await _context.FilasProcessamento
                .Where(f => status.Contains(f.StatusFilaProcessamento))
                .Include(f => f.Item)
                .Include(f => f.Agregador)
                .ToListAsync();
        }

        public async Task InserirAsync(FilaProcessamento fila)
        {
            _context.FilasProcessamento.Add(fila);
            await _context.SaveChangesAsync();
        }

        public async Task AtualizarAsync(FilaProcessamento fila)
        {
            _context.FilasProcessamento.Update(fila);
            await _context.SaveChangesAsync();
        }
    }
}
