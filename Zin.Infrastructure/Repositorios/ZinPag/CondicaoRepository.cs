using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class CondicaoRepository
    {
        public class CondicaoRepositoryRepository(IUnitOfWork unitOfWork) : ICondicaoRepository
        {
            private readonly IUnitOfWork _unitOfWork = unitOfWork;
            private ZinDbContext _context => _unitOfWork.Context;

            public async Task<Condicao?> BuscarPorIdAsync(Guid id)
            {
                return await _context.Condicoes.FindAsync(id);
            }

            public async Task AtualizarAsync(Condicao condicao)
            {
                _context.Condicoes.Update(condicao);
                await _context.SaveChangesAsync();
            }
        }
    }
}
