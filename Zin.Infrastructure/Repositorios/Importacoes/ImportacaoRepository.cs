using Microsoft.EntityFrameworkCore;
using Npgsql;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Importacoes;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Importacoes;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Importacoes
{
    public class ImportacaoRepository(IImportacaoUnitOfWork unitOfWork) : IImportacaoRepository
    {
        private readonly IImportacaoUnitOfWork _unitOfWork = unitOfWork;

        private ZinImportacoesDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(ImportacaoAgregador entidade)
        {
            Context.ImportacaoAgregadores.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<ImportacaoAgregador>> BuscarAsync(Expression<Func<ImportacaoAgregador, bool>> predicado)
        {
            return Context.ImportacaoAgregadores.Where(predicado).AsEnumerable();
        }

        public async Task<IEnumerable<ImportacaoAgregador>> BuscarImportacoesPendentesPorClienteAsync(string idCliente)
        {

            return await Context.ImportacaoAgregadores
                .Where(i => i.Status == StatusImportacao.AguardandoProcessamento)
                .Where(i => i.IdCliente == idCliente)
                .ToArrayAsync();
        }

        public Task<ImportacaoAgregador?> BuscarPorIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<ImportacaoAgregador> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(ImportacaoAgregador entidade)
        {
            await Context.ImportacaoAgregadores.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<ImportacaoAgregador> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<ImportacaoAgregador>> ListarAsync()
        {
            throw new NotImplementedException();
        }
    }
}
