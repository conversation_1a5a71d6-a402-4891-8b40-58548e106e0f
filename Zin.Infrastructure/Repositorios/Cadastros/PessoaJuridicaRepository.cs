using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.ValueObject;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class PessoaJuridicaRepository(IUnitOfWork unitOfWork) : IPessoaJuridicaRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(PessoaJuridica entidade)
        {
            Context.PessoasJuridicas.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeletarAsync(int id)
        {
            var entidade = await Context.PessoasJuridicas.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Pessoa com ID {id} não encontrada.");

            Context.PessoasJuridicas.Remove(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeletarVariosAsync(IEnumerable<PessoaJuridica> entidades)
        {
            Context.PessoasJuridicas.RemoveRange(entidades);
            await _unitOfWork.CommitAsync();
        }

        public async Task<int> InserirAsync(PessoaJuridica entidade)
        {
            await Context.PessoasJuridicas.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<PessoaJuridica> entidades)
        {
            await Context.PessoasJuridicas.AddRangeAsync(entidades);
            var result = await _unitOfWork.CommitAsync();
            return [.. entidades.Select(e => e.Id)];

        }

        public async Task<IEnumerable<PessoaJuridica>> BuscarAsync(Expression<Func<PessoaJuridica, bool>> predicado)
        {
            return await Context.PessoasJuridicas.Where(predicado).ToListAsync();
        }

        public Task<ClienteEmpresa> BuscarClienteEmpresaPorCnpjAsync(Cnpj cnpj)
        {
            return Context.PessoasJuridicas
                .OfType<ClienteEmpresa>()
                .FirstOrDefaultAsync(ce => EF.Functions.ILike(ce.Cnpj, cnpj));
        }

        public async Task<Pessoa?> BuscarFornecedorPorCnpjAsync(Cnpj cnpj)
        {
            // Busca em PessoaJuridica com o CNPJ informado
            var pessoaJuridica = await Context.PessoasJuridicas
                .FirstOrDefaultAsync(pj => pj.Cnpj == cnpj);

            if (pessoaJuridica != null)
                return pessoaJuridica;

            return null;
        }

        public async Task<PessoaJuridica> BuscarPorCnpjAsync(Cnpj cnpj)
        {
            return await Context.PessoasJuridicas
                .FirstAsync(pj => pj.Cnpj == cnpj);
        }

        public async Task<IEnumerable<PessoaJuridica>> BuscarPorCnpjsAsync(IEnumerable<Cnpj> cnpjs)
        {
            var valores = cnpjs.Select(c => c.Digitos);

            return await Context.PessoasJuridicas
              .Where(pj => valores.Contains(pj.Cnpj)) // traduz para: WHERE cnpj IN (...)
              .ToListAsync();
        }

        public async Task<PessoaJuridica?> BuscarPorIdAsync(int id)
        {
            var pessoa = await Context.PessoasJuridicas
             .Include(p => p.PessoasEnderecos)
                 .ThenInclude(pe => pe.Endereco)
             .Include(p => p.PessoasContatos)
                 .ThenInclude(pc => pc.Contato)
             // TODO: (Filipe) Tipos e Telefones cairam, revisar se precisa de algo mais
             //.ThenInclude(c => c.Tipos)
             .Include(p => p.PessoasContatos)
                 .ThenInclude(pc => pc.Contato)
             // TODO: (Filipe) Tipos e Telefones cairam, revisar se precisa de algo mais
             //.ThenInclude(c => c.Tipos)
             .Include(p => p.DadosBancarios)
                 .ThenInclude(b => b.DadoBancario)
                 .ThenInclude(db => db.Banco)
             .FirstOrDefaultAsync(p => p.Id == id);

            if (pessoa is null)
                throw new EntidadeNaoEncontradaExcecao($"Pessoa com ID {id} não encontrada.");

            return pessoa;
        }

        public async Task<IEnumerable<PessoaJuridica>> ListarAsync()
        {
            return await Context.PessoasJuridicas.ToListAsync();
        }
    }
}
