using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.ValueObject;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class ClienteEmpresaRepository(IUnitOfWork unitOfWork) : IClienteEmpresaRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(ClienteEmpresa entidade)
        {
            Context.ClienteEmpresas.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<ClienteEmpresa>> BuscarAsync(Expression<Func<ClienteEmpresa, bool>> predicado)
        {
            return await Context.ClienteEmpresas.Where(predicado).ToListAsync();
        }

        public async Task<ClienteEmpresa?> BuscarPorCnpjAsync(Cnpj cnpj)
        {
            return 
                await Context.ClienteEmpresas
                .FirstOrDefaultAsync(pj => pj.Cnpj == cnpj);
        }

        public async Task<IEnumerable<ClienteEmpresa>> BuscarPorCnpjAsync(IEnumerable<Cnpj> cnpjs)
        {
            if (!cnpjs.Any())
                return Enumerable.Empty<ClienteEmpresa>();

            var cnpjsNormalizados = cnpjs
                  .Select(c => c.Digitos)
                  .Where(d => !string.IsNullOrEmpty(d))
                  .Distinct()
                  .ToArray();

            if (cnpjsNormalizados.Length == 0)
                return Array.Empty<ClienteEmpresa>();

            return await Context.ClienteEmpresas
                .AsNoTracking()
                .Where(p => cnpjsNormalizados.Contains(p.Cnpj))
                .ToListAsync();
        }

        public async Task<IEnumerable<ClienteEmpresa>> BuscarPorCnpjsAsync(IEnumerable<Cnpj> cnpjs)
        {
            var valores = cnpjs.Select(c => c.Digitos);

            return await Context.ClienteEmpresas
              .Where(pj => valores.Contains(pj.Cnpj)) // traduz para: WHERE cnpj IN (...)
              .ToListAsync();
        }

        public async Task<ClienteEmpresa?> BuscarPorIdAsync(int id)
        {
            var pessoa = await Context.ClienteEmpresas
               .Include(p => p.PessoasEnderecos)
                   .ThenInclude(pe => pe.Endereco)
               .Include(p => p.PessoasContatos)
                   .ThenInclude(pc => pc.Contato)
               // TODO: (Filipe) Tipos e Telefones cairam, revisar se precisa de algo mais
               //.ThenInclude(c => c.Tipos)
               .Include(p => p.PessoasContatos)
                   .ThenInclude(pc => pc.Contato)
               // TODO: (Filipe) Tipos e Telefones cairam, revisar se precisa de algo mais
               //.ThenInclude(c => c.Tipos)
               .Include(p => p.DadosBancarios)
                   .ThenInclude(b => b.DadoBancario)
                   .ThenInclude(db => db.Banco)
               .FirstOrDefaultAsync(p => p.Id == id);

            if (pessoa is null)
                throw new EntidadeNaoEncontradaExcecao($"Pessoa com ID {id} não encontrada.");

            return pessoa;
        }

        public async Task DeletarAsync(int id)
        {
            var entidade = await Context.ClienteEmpresas.FindAsync(id)
               ?? throw new EntidadeNaoEncontradaExcecao($"Pessoa com ID {id} não encontrada.");

            Context.ClienteEmpresas.Remove(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeletarVariosAsync(IEnumerable<ClienteEmpresa> entidades)
        {
            Context.ClienteEmpresas.RemoveRange(entidades);
            await _unitOfWork.CommitAsync();
        }

        public async Task<int> InserirAsync(ClienteEmpresa entidade)
        {
            await Context.ClienteEmpresas.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<ClienteEmpresa> entidades)
        {
            await Context.ClienteEmpresas.AddRangeAsync(entidades);
            var result = await _unitOfWork.CommitAsync();
            return entidades.Select(e => e.Id).ToArray();
        }

        public async Task<IEnumerable<ClienteEmpresa>> ListarAsync()
        {
            return await Context.ClienteEmpresas.ToListAsync();
        }
    }
}
