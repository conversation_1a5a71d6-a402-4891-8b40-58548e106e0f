using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class DadoBancarioRepository(IUnitOfWork unitOfWork) : IDadoBancarioRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(DadoBancario entidade)
        {
            Context.DadosBancarios.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public Task<IEnumerable<DadoBancario>> BuscarAsync(Expression<Func<DadoBancario, bool>> predicado)
        {
            throw new NotImplementedException();
        }

        public async Task<DadoBancario?> BuscarPorIdAsync(int id)
        {
            var dadoBancario = await Context.DadosBancarios
                .FirstOrDefaultAsync(p => p.Id == id);

            if (dadoBancario is null)
                throw new EntidadeNaoEncontradaExcecao($"Dado Bancário com ID {id} não encontrado.");

            return dadoBancario;
        }

        public async Task DeletarAsync(int id)
        {
            var dadoBancario = await Context.DadosBancarios.FirstOrDefaultAsync(e => e.Id == id);

            if (dadoBancario is null)
                throw new EntidadeNaoEncontradaExcecao($"Dado Bancário com ID {id} não encontrado.");

            Context.DadosBancarios.Remove(dadoBancario);
            await _unitOfWork.CommitAsync();
        }

        public Task DeletarVariosAsync(IEnumerable<DadoBancario> entidades) { throw new NotImplementedException(); }
        public Task<int> InserirAsync(DadoBancario entidade)
        {
            throw new NotImplementedException();
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<DadoBancario> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<DadoBancario>> ListarAsync()
        {
            throw new NotImplementedException();
        }
    }
}
