using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class ConfiguracaoRepository(IUnitOfWork unitOfWork) : IConfiguracaoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext _context => _unitOfWork.Context;

        public async Task<Configuracao?> BuscarPorIdAsync(Guid id)
            => await _context.Configuracoes.Include(x => x.Regras).FirstOrDefaultAsync(x => x.Id == id);

        public async Task<IEnumerable<Configuracao>> BuscarAsync()
            => await _context.Configuracoes.Include(x => x.Regras).ToListAsync();

        public async Task<Guid> InserirAsync(Configuracao configuracao)
        {
            _context.Configuracoes.Add(configuracao);
            await _context.SaveChangesAsync();
            return configuracao.Id;
        }

        public async Task AtualizarAsync(Configuracao configuracao)
        {
            _context.Configuracoes.Update(configuracao);
            await _context.SaveChangesAsync();
        }

        public async Task DeletarAsync(Guid id)
        {
            var entity = await _context.Configuracoes.FindAsync(id);
            if (entity != null)
            {
                _context.Configuracoes.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<Configuracao?>> BuscarPorTipoProcessamentoAsync(TipoProcessamento tipoProcessamento)
        {
            return await _context.Configuracoes
        .Include(c => c.Regras)
        .Where(c => c.TipoProcessamento == tipoProcessamento)
        .ToListAsync();
        }

        public async Task<Regra?> BuscarRegraPorIdAsync(Guid idRegra)
            => await _context.Regras.FirstOrDefaultAsync(x => x.Id == idRegra);

        public async Task InserirRegraAsync(Regra regra)
        {
            _context.Regras.Add(regra);
            await _context.SaveChangesAsync();
        }

        public async Task AtualizarRegraAsync(Regra regra)
        {
            _context.Regras.Update(regra);
            await _context.SaveChangesAsync();
        }

        public async Task DeletarRegraAsync(Guid idRegra)
        {
            var entity = await _context.Regras.FindAsync(idRegra);
            if (entity != null)
            {
                _context.Regras.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }
    }
}
