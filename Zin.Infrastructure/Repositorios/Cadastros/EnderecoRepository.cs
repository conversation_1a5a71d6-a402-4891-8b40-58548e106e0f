using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Cadastros.Enderecos;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class EnderecoRepository(IUnitOfWork unitOfWork) : IEnderecoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(Endereco entidade)
        {
            Context.Enderecos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public Task<IEnumerable<Endereco>> BuscarAsync(Expression<Func<Endereco, bool>> predicado)
        {
            throw new NotImplementedException();
        }

        public async Task<Endereco?> BuscarPorIdAsync(int id)
        {
            var endereco = await Context.Enderecos.FirstOrDefaultAsync(p => p.Id == id);

            if (endereco is null)
                throw new EntidadeNaoEncontradaExcecao($"Endereço com ID {id} não encontrado.");

            return endereco;
        }

        public async Task DeletarAsync(int id)
        {
            var endereco = await Context.Enderecos.FirstOrDefaultAsync(e => e.Id == id);

            if (endereco is null)
                throw new EntidadeNaoEncontradaExcecao($"Endereço com ID {id} não encontrado.");

            Context.Enderecos.Remove(endereco);
            await _unitOfWork.CommitAsync();
        }

        public Task DeletarVariosAsync(IEnumerable<Endereco> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<int> InserirAsync(Endereco entidade)
        {
            throw new NotImplementedException();
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<Endereco> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Endereco>> ListarAsync()
        {
            throw new NotImplementedException();
        }
    }
}
