using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Cadastros.Enderecos;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class CidadeRepository(IUnitOfWork unitOfWork) : ICidadeRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;


        public async Task<IEnumerable<Cidade>> BuscarAsync(Expression<Func<Cidade, bool>> predicado)
        {
            return await Context.Cidades.Where(predicado).ToListAsync();
        }

        public async Task<IEnumerable<Cidade>> ListarAsync()
        {
            return await Context.Cidades.ToListAsync();
        }

        public async Task<Cidade?> BuscarPorCodigoIBGE(string codigoIBGE)
        {
            return await Context.Cidades
                .FirstOrDefaultAsync(c => EF.Functions.ILike(c.Cod<PERSON>b<PERSON>, codigoIBGE));
        }

        public Task AtualizarAsync(Cidade entidade)
        {
            throw new NotImplementedException();
        }

        public Task<Cidade?> BuscarPorIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<int> InserirAsync(Cidade entidade)
        {
            throw new NotImplementedException();
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<Cidade> entidades)
        {
            throw new NotImplementedException();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public async Task DeletarVariosAsync(IEnumerable<Cidade> entidades)
        {
            //return await Context.Cidades.FirstOrDefaultAsync(c => EF.Functions.ILike(c.CodIbge, codigoIBGE));
            throw new NotImplementedException();
        }
    }
}
