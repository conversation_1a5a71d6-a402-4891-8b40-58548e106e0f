using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Cadastros.Contatos;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class ContatoRepository(IUnitOfWork unitOfWork) : IContatoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(Contato entidade)
        {
            Context.Contatos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public Task<IEnumerable<Contato>> BuscarAsync(Expression<Func<Contato, bool>> predicado)
        {
            throw new NotImplementedException();
        }

        public async Task<Contato?> BuscarPorIdAsync(int id)
        {
            var contato = await Context.Contatos
                // TODO: (Filipe) Tipos e Telefones cairam, revisar se precisa de algo mais
                //.Include(p => p.Tipos)
                //.Include(p => p.Telefones)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (contato is null)
                throw new EntidadeNaoEncontradaExcecao($"Contato com ID {id} não encontrado.");

            return contato;
        }

        public async Task DeletarAsync(int id)
        {
            var contato = await Context.Contatos.FirstOrDefaultAsync(e => e.Id == id);

            if (contato is null)
                throw new EntidadeNaoEncontradaExcecao($"Contato com ID {id} não encontrado.");

            Context.Contatos.Remove(contato);
            await _unitOfWork.CommitAsync();
        }

        public Task DeletarVariosAsync(IEnumerable<Contato> entidades) { throw new NotImplementedException(); }
        public Task<int> InserirAsync(Contato entidade)
        {
            throw new NotImplementedException();
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<Contato> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Contato>> ListarAsync()
        {
            throw new NotImplementedException();
        }
    }
}
