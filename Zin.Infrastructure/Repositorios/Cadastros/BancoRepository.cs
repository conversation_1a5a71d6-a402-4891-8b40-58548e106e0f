using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class BancoRepository(IUnitOfWork unitOfWork) : IBancoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public Task AtualizarAsync(Banco entidade)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Banco>> BuscarAsync(Expression<Func<Banco, bool>> predicado)
        {
            throw new NotImplementedException();
        }

        public Task<Banco?> BuscarPorIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<Banco> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<int> InserirAsync(Banco entidade)
        {
            throw new NotImplementedException();
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<Banco> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<Banco>> ListarAsync()
        {
            return await Context.Bancos.ToListAsync();
        }
    }
}
