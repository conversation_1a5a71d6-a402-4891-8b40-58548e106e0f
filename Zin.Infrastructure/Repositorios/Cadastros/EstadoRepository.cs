using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Zin.Domain.Entidades.Cadastros.Enderecos;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class EstadoRepository(IUnitOfWork unitOfWork) : IEstadoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;
 
        public Task AtualizarAsync(Estado entidade)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Estado>> BuscarAsync(Expression<Func<Estado, bool>> predicado)
        {
            throw new NotImplementedException();
        }

        public Task<Estado?> BuscarPorIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<Estado> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<int> InserirAsync(Estado entidade)
        {
            throw new NotImplementedException();
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<Estado> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<Estado>> ListarAsync()
        {
            return await Context.Estados.ToListAsync();
        }

        public async Task<Estado?> BuscarPorSiglaAsync(string sigla)
        {
           return await Context.Estados
                .FirstOrDefaultAsync(e => EF.Functions.ILike(e.Sigla, sigla));
        }
    }
}
