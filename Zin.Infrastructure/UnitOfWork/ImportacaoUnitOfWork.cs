using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.EntityFrameworkCore;
using Zin.Infrastructure.Dados;

namespace Zin.Infrastructure.UnitOfWork
{
    public class ImportacaoUnitOfWork : IImportacaoUnitOfWork
    {
        private readonly ZinImportacoesDbContext _context;
        private IDbContextTransaction? _transaction;

        public ImportacaoUnitOfWork(IDbContextFactory<ZinImportacoesDbContext> contextFactory)
        {
            _context = contextFactory.CreateDbContext();
            _transaction = _context.Database.BeginTransaction();
        }

        public ZinImportacoesDbContext Context => _context;

        public async Task<int> CommitAsync()
        {
            var result = await _context.SaveChangesAsync();
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
            return result;
        }

        public void Rollback()
        {
            _transaction?.Rollback();
            _transaction?.Dispose();
            _transaction = null;
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}