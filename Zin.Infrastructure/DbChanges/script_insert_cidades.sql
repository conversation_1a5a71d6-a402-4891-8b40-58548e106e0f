INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Aimor<PERSON>', '3101102', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Alfenas', '3101607', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Almirante Tamandaré', '4100400', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Anápolis', '5201108', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Apiaí', '3502705', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Araucária', '4101804', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Barra Mansa', '3300407', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bertioga', '3506359', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Betim', '3106705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Blumenau', '4202404', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bocaiúva', '3107307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bragança Paulista', '3507605', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Brasília', '5300108', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Campo Bom', '4303905', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Canela', '4304408', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Canoas', '4304606', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Caxias do Sul', '4305108', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Colombo', '4105805', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Contagem', '3118601', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cuiabá', '5103403', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Curitiba', '4106902', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divinópolis', '3122306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dois Irmãos', '4306403', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Duque de Caxias', '3301702', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Embu das Artes', '3515004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Encantado', '4306809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Erechim', '4307005', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Eugenópolis', '3124906', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Farroupilha', '4307906', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Feliz', '4308102', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Flores da Cunha', '4308201', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Florianópolis', '4205407', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Franca', '3516200', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Garibaldi', '4308607', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Glorinha', '4309050', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goiás', '5208905', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Governador Valadares', '3127701', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarujá', '3518701', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarulhos', '3518800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Igrejinha', '4310108', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajaí', '4208203', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itatinga', '3523503', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itu', '3523909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaraguá do Sul', '4208906', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Pessoa', '2507507', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joinville', '4209102', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juína', '5105150', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajeado', '4311403', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Venda Nova do Imigrante', '3205069', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Londrina', '4113700', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mairinque', '3528403', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maringá', '4115200', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mauá', '3529401', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nilópolis', '3303203', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Iguaçu', '3303500', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Trento', '4211504', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Hamburgo', '4313409', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olaria', '3145406', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oliveira Fortes', '3145703', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Osasco', '3534401', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeiras de Goiás', '5215702', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parobé', '4314050', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passo Fundo', '4314100', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passos', '3147907', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pelotas', '4314407', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peritiba', '4212601', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracicaba', '3538709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poços de Caldas', '3151800', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Alegre', '4314902', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Feliz', '3540606', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Praia Grande', '4213807', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquara', '4321204', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Recife', '2611606', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Branco do Sul', '4122206', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Verde', '5218805', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rolante', '4316006', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rondon', '4122602', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sabará', '3156700', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto do Jacuí', '4316451', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz das Palmeiras', '3546306', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana de Parnaíba', '3547304', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo André', '3547809', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santos', '3548500', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bernardo do Campo', '3548708', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Caetano do Sul', '3548807', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Paula', '4318200', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo', '3304904', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João Batista', '4216305', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Joaquim da Barra', '3549409', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José', '4216602', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Rio Pardo', '3549706', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Leopoldo', '4318705', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Marcos', '4319000', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Mateus', '3204906', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Paulo', '3550308', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro da Aldeia', '3305208', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Roque', '3550605', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Praia Grande', '3541000', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Vicente', '3551009', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sapucaia do Sul', '4320008', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra', '3205002', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sorocaba', '3552205', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tapejara', '4320909', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião', '3550704', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taubaté', '3554102', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Telêmaco Borba', '4127106', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tramandaí', '4321600', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Corações', '3169307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uberlândia', '3170206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Varginha', '3170701', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viamão', '4323002', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limeira', '3526902', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sumaré', '3552403', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Campo Grande', '2701506', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uruaçu', '5221601', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viçosa', '3171303', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Araçatuba', '3502804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Afonso Cláudio', '3200102', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Alagoinhas', '2900702', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Alfredo Chaves', '3200300', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Alvorada', '4300604', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Amparo', '3501905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Aracruz', '3200607', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Arapongas', '4101507', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Artur Nogueira', '3503802', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Assis', '3504008', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Atibaia', '3504107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Balneário Camboriú', '4202008', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cachoeiro de Itapemirim', '3201209', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Campo Belo', '3111200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Campos do Jordão', '3509700', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Campos dos Goytacazes', '3301009', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Canguçu', '4304507', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Caraguatatuba', '3510500', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Carapicuíba', '3510609', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cariacica', '3201308', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Caruaru', '2604106', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Castelo', '3201407', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Chapada', '4305306', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Charqueadas', '4305355', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Colatina', '3201506', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Coronel Fabriciano', '3119401', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Coronel Freitas', '4204400', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Coronel Vivida', '4106506', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cotia', '3513009', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Criciúma', '4204608', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cubatão', '3513504', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Diadema', '3513801', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dourados', '5003702', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ecoporanga', '3202108', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Elói Mendes', '3123601', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estância Velha', '4307609', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esteio', '4307708', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Faxinal', '4107603', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fernandópolis', '3515509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('União do Oeste', '4218855', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Morato', '3516309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Garopaba', '4205704', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gaspar', '4205902', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guabiruba', '4206306', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaíba', '4309308', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Hortolândia', '3519071', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibaiti', '4109708', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirubá', '4310009', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiúna', '3519709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iconha', '3202603', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Indaiatuba', '3520509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Indiara', '5209952', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipatinga', '3131307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irani', '4207809', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irupi', '3202652', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaberaba', '2914703', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguara', '3132206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhaém', '3522109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaquaquecetuba', '3523107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itarana', '3202900', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iúna', '3203007', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivinhema', '5004700', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguaré', '3203056', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Monlevade', '3136207', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Santa', '3137601', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lauro Muller', '4209607', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Linhares', '3203205', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luzerna', '4210035', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marechal Floriano', '3203346', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alto', '3531308', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montenegro', '4312401', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muniz Freire', '3203700', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Navegantes', '4211306', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Venécia', '3203908', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oratórios', '3145851', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paiva', '3146602', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palma', '3146701', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranavaí', '4118402', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pato Branco', '4118501', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Patrocínio do Muriaé', '3148202', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedreira', '3537107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Canário', '3204054', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piratuba', '4213104', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pomerode', '4213203', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponta Grossa', '4119905', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponto Belo', '3204252', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pouso Alegre', '3152501', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Prudente', '3541406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Claro', '4121802', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Pires', '3543303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Grande', '4315602', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salvador', '2927408', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Livramento', '4317103', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Ângelo', '4317509', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio da Patrulha', '4317608', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Carlos', '3548906', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos', '4216107', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gabriel da Palha', '3204708', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João', '4124806', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Urtiga', '4318424', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Rio Preto', '3549805', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sapiranga', '4319901', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Seara', '4217501', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sertãozinho', '3551702', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sete Lagoas', '3167202', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tatuí', '3554003', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teófilo Otoni', '3168606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tijucas', '4218004', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Timbó', '4218202', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trindade do Sul', '4321956', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tubarão', '4218707', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uberaba', '3170107', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vacaria', '4322509', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Valinhos', '3556206', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Videira', '4219309', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Campo Grande', '5002704', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ilhabela', '3520400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itá', '4208005', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fortaleza', '2304400', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Conceição do Castelo', '3201704', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguaçu', '3202702', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lençóis Paulista', '3526803', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Anchieta', '3200409', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Angra dos Reis', '3300100', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Aparecida de Goiânia', '5201405', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Araruama', '3300209', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Areal', '3300225', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Balsa Nova', '4102307', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Belém', '1501402', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Boa Esperança', '3107109', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Boa Esperança', '3201001', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bom Despacho', '3107406', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bom Jesus da Lapa', '2903904', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Canoinhas', '4203808', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Carmópolis de Minas', '3114501', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Caxias', '2103000', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cruz Alta', '4306106', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cruzeiro do Oeste', '4106605', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cunha Porã', '4204707', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dois Lajeados', '4306452', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Espumoso', '4307500', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estrela', '4307807', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Firminópolis', '5207808', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Franco da Rocha', '3516408', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goiatuba', '5209101', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goioerê', '4108601', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guanambi', '2911709', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaporé', '4309407', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Holambra', '3519055', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirité', '3129806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Imbé', '4310330', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irecê', '2914604', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaiópolis', '4208104', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itapema', '4208302', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itatiaia', '3302254', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itirapina', '3523602', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jandira', '3525003', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaraguari', '5004908', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jardinópolis', '3525102', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jequié', '2918001', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juazeiro', '2918407', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lages', '4209300', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lapa', '4113205', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lavras', '3138203', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leopoldina', '3138401', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Louveira', '3527306', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracás', '2920502', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marataízes', '3203320', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marau', '4311809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcelino Ramos', '4311908', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Medina', '3141405', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miranda', '5005608', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Castelo', '3531605', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Santo de Minas', '3143203', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mostardas', '4312500', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mucurici', '3203601', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nanuque', '3144300', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Niquelândia', '5214606', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Bassano', '4312906', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Erechim', '4211405', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Granada', '3533007', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olímpia', '3533908', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Fino', '3146008', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Preto', '3146107', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pará de Minas', '3147105', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranaguá', '4118204', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulo Afonso', '2924009', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pederneiras', '3536703', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Penápolis', '3537305', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peruíbe', '3537602', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhalzinho', '4212908', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracanjuba', '5217104', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pires do Rio', '5217401', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitangui', '3151404', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Seguro', '2925303', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto União', '4213609', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Bernardes', '3153103', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quirinópolis', '5218508', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rafard', '3542107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Bonito', '3542909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Preto', '3543402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rondonópolis', '5107602', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sacramento', '3156908', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara', '3157203', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Vitória', '3159803', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santarém', '1506807', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio de Posse', '3548005', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento do Sul', '4215802', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luís', '2111300', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luiz Gonzaga', '4318903', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sarandi', '4320107', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saudades', '4217303', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrinha', '2930501', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Socorro', '3552106', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Soledade', '4320800', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Suzano', '3552502', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tanguá', '3305752', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tapes', '4321105', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeira de Freitas', '2931350', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tietê', '3554508', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Timóteo', '3168705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Pontas', '3169406', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Triunfo', '4322004', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tupandi', '4322251', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubá', '3169901', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Várzea Paulista', '3556503', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Veranópolis', '4322806', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Valério', '3205176', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória', '3205309', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos do Norte', '3204658', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Eldorado', '3514809', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senhor do Bonfim', '2930105', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaperuçu', '4111258', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Doutor Maurício Cardoso', '4306734', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Engenheiro Beltrão', '4107504', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esmeralda', '4307401', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esperança Nova', '4107520', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estância', '2802106', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estação', '4307559', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Faria Lemos', '3125309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fazenda Rio Grande', '4107652', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Feira de Santana', '2910800', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fraiburgo', '4205506', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Beltrão', '4108403', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibatiba', '3202454', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiporã', '4109807', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguaba Grande', '3301876', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ijuí', '4310207', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Imperatriz', '2105302', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabirito', '3131901', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguaí', '3302007', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itapecerica da Serra', '3522208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaperuna', '3302205', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itapevi', '3522505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itatiba', '3523404', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivoti', '4310801', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jandaia do Sul', '4112108', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joaçaba', '4209003', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Pinheiro', '3136306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Vermelha', '4311304', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laguna', '4209409', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lindolfo Collor', '4311627', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaé', '3302403', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maceió', '2704302', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maravilha', '4210506', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marialva', '4114807', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mariana', '3140001', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maricá', '3302700', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marília', '3529005', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matinhos', '4115705', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mococa', '3530508', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montes Claros', '3143302', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Naviraí', '5005707', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Santa Rita', '4313375', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Xavantina', '5106257', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmas', '1721000', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paula Cândido', '3148301', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Bonita', '3148756', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Dourada', '3149002', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Teixeira', '3149408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Penha', '4212502', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Petrolândia', '4212700', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraquara', '4119509', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prudentópolis', '4120606', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quatro Barras', '4120804', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Bonito', '3304300', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio das Ostras', '3304524', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio do Sul', '4214805', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Fortuna', '4214904', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa', '4317202', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio de Pádua', '3304706', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Sul', '4216206', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Jerônimo', '4318408', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Hortêncio', '4318481', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Oeste', '4217204', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sapucaia', '3305406', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sarandi', '4126256', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sinop', '5107909', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Siqueira Campos', '4126603', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tamarana', '4126678', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tapira', '4126900', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaritinga', '3553708', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teresópolis', '3305802', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Barras', '4218301', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Rios', '3306008', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Umuarama', '4128104', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uruguaiana', '4322400', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Valparaíso de Goiás', '5221858', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Várzea Grande', '5108402', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vassouras', '3306206', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vespasiano', '3171204', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xanxerê', '4219507', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Herval D''Oeste', '4206702', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Portão', '4314803', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taguatinga', '1720903', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobradinho', '2930774', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guará', '3517703', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Biguaçu', '4202305', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Camaçari', '2905701', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Iguaçu', '4125704', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Foz do Iguaçu', '4108304', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Lourenço do Oeste', '4216909', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiraçu', '3202504', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palhoça', '4211900', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Acreúna', '5200134', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Águia Branca', '3200136', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Aloândia', '5200506', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Andirá', '4101101', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Andradina', '3502101', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Araçoiaba da Serra', '3502903', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Araguari', '3103504', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Arapoti', '4101606', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Araxá', '3104007', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Astorga', '4102109', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Barra do Garças', '5101803', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bebedouro', '3506102', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Borborema', '3507407', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Botucatu', '3507506', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Capão da Canoa', '4304630', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Capivari de Baixo', '4203956', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Carmo de Minas', '3114105', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Catalão', '5205109', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Caxambu', '3115508', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cornélio Procópio', '4106407', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cosmópolis', '3512803', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Crateús', '2304103', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Curitibanos', '4204806', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Curvelo', '3120904', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dom Aquino', '5103601', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Formosa', '5208004', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fundão', '3202207', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gramado', '4309100', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibaté', '3519303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Içara', '4207007', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguatu', '2305506', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Inhumas', '5210000', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipumirim', '4207700', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irati', '4110706', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaboraí', '3301900', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabuna', '2914802', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itapiranga', '4208401', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaúna', '3133808', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itumbiara', '5211503', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itupeva', '3524006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguariúna', '3524709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jales', '3524808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Neiva', '3203130', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lorena', '3527207', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luziânia', '5212501', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Magé', '3302502', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manaus', '1302603', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marechal Cândido Rondon', '4114609', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marilândia', '3203353', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mariópolis', '4115309', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matão', '3529302', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mesquita', '3302858', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mimoso do Sul', '3203403', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Mor', '3531803', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muriaé', '3143906', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Natal', '2408102', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Araçá', '4312807', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Friburgo', '3303401', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Hartz', '4313060', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olinda', '2609600', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orlândia', '3534302', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Osório', '4313508', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro', '4211801', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmares do Sul', '4313656', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmas', '4117602', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranaíba', '5006309', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pareci Novo', '4314035', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Patos de Minas', '3148004', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulínia', '3536505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pequeri', '3149507', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piedade de Ponte Nova', '3150208', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhão', '4119301', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinheiral', '3303955', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitanga', '4119608', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitangueiras', '3539509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Serrada', '4213401', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porciúncula', '3304102', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rancharia', '3542206', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão das Neves', '3154606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Novo', '3155405', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cecília', '4215505', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz do Rio Pardo', '3546405', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Sapucaí', '3159605', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa de Viterbo', '3547601', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Amaro da Imperatriz', '4215703', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Cristo', '4317905', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Borja', '4318002', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Assis', '4318101', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Boa Vista', '3549102', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Cedro', '4216701', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Mateus do Sul', '4125605', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Caí', '4319505', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Talhada', '2613909', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobral', '2312908', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Terra de Areia', '4321436', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Terra Rica', '4127304', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Torres', '4321501', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três de Maio', '4321808', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubatuba', '3555406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Unaí', '3170404', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urussanga', '4219002', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vargem Grande Paulista', '3556453', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Várzea Alegre', '2314003', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vazante', '3171006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Votuporanga', '3557105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cruzeiro', '3513405', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Conselheiro Lafaiete', '3118304', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Leopoldo', '3149309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Negro', '4122305', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguaraçu', '4110003', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraty', '3303807', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaçuí', '3202306', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palma Sola', '4212007', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirpirituba', '2511806', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Alegre', '3200201', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Alegrete', '4300406', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ampére', '4101002', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Andradas', '3102605', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Aparecida do Taboado', '5001003', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Aracaju', '2800308', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Aragoiânia', '5201801', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Arroio do Meio', '4301008', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Baependi', '3104908', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Barbacena', '3105608', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Barreiras', '2903201', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Batayporã', '5002001', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bernardino de Campos', '3506300', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bonito', '5002209', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Brochier', '4302659', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Caaporã', '2503001', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Caicó', '2402006', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Campinorte', '5204706', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Candói', '4104428', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Carambeí', '4104659', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Celso Ramos', '4204152', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cláudia', '5103056', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cocalzinho de Goiás', '5205513', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Coronel Martins', '4204459', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Coronel Sapucaia', '5003157', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cromínia', '5206503', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cruzeiro do Sul', '4306205', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Curiúva', '4107009', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dionísio Cerqueira', '4205001', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dona Emma', '4205100', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Doutor Pedrinho', '4205159', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Duartina', '3514502', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ermo', '4205191', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fagundes Varela', '4307864', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Forquilhinha', '4205456', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Governador Celso Ramos', '4206009', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gravatal', '4206207', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Hidrolândia', '5209705', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiaçá', '4309803', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ilha Solteira', '3520442', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Independência', '4310405', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipojuca', '2607208', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iporã', '4110607', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaberá', '3521705', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaí', '3521804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itapuranga', '5211206', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaborá', '4208609', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jardim Alegre', '4112504', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joviânia', '5212105', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jupiá', '4209177', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jussara', '4113007', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Seca', '2508307', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mambaí', '5212709', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manhuaçu', '3139409', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maratá', '4311791', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marema', '4210555', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maripá', '4115358', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Messias', '2705200', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Minaçu', '5213087', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mozarlândia', '5214002', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mundo Novo', '2922102', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Aurora', '4116703', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Roma do Sul', '4313359', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Veneza', '4211603', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palestina', '3535002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmital', '4117800', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Panorama', '3535408', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulo de Faria', '3536604', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Petrolândia', '2611002', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhalão', '4119202', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Médici', '1100254', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quedas do Iguaçu', '4120903', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Restinga Seca', '4315503', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão do Pinhal', '4121901', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio do Oeste', '4214607', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Largo', '2707701', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riolândia', '3544202', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riqueza', '4215075', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto do Lontra', '4123006', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Terezinha de Itaipu', '4124053', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Ivaí', '4125001', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Lourenço', '3163706', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel dos Campos', '2708600', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sepé', '4319604', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Redonda', '2515807', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sulina', '4126652', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tunápolis', '4218756', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Valença', '3306107', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Várzea da Roça', '2933059', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória do Xingu', '1508357', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Bonita do Sul', '4311239', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Eldorado', '5003751', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planaltina', '5217609', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz do Capibaribe', '2612505', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento', '2513901', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião', '2708808', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Conceição do Coité', '2908408', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Agudos do Sul', '4100301', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Alfredo Marcondes', '3500808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Alto Piquiri', '4100707', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Américo Brasiliense', '3501707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Angelim', '2601003', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Aquidauana', '5001102', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Balneário Barra do Sul', '4202057', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Barro Alto', '5203203', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bom Jesus do Norte', '3201100', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Bonito', '2602308', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Brasilândia', '5002308', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Brejo da Madre de Deus', '2602605', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Buritis', '1100452', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cubatão', '9', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cunha', '3128709', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cupira', '2605004', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Currais Novos', '2403103', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cururupu', '2103703', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Custódia', '2605103', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Cutias', '1600212', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Damianópolis', '5206701', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Damolândia', '5206800', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Darcinópolis', '1706506', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('David Canabarro', '4306304', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Delfim Moreira', '3121100', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Delfinópolis', '3121209', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Demerval Lobão', '2203305', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Denise', '5103452', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Deodápolis', '5003454', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Derrubadas', '4306320', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Descalvado', '3513702', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Desterro', '2505402', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Desterro de Entre Rios', '3121407', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Desterro do Melo', '3121506', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Diamante', '2505600', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Diamante do Norte', '4107108', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Diamante D''Oeste', '4107157', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Diamantina', '3121605', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dianópolis', '1707009', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dias D''Ávila', '2910057', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dilermando de Aguiar', '4306379', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Diogo de Vasconcelos', '3121704', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dirceu Arcoverde', '2203354', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dirce Reis', '3513850', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divina Pastora', '2802007', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divinolândia', '3513900', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divinolândia de Minas', '3122207', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divino', '3122009', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divino das Laranjeiras', '3122108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divino de São Lourenço', '3201803', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divisa Alegre', '3122355', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divisa Nova', '3122405', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Divisópolis', '3122454', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dois Córregos', '3514106', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dois Irmãos das Missões', '4306429', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dois Irmãos do Buriti', '5003488', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dois Lajeados', '4306452', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dois Vizinhos', '4107207', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dolcinópolis', '3514205', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dom Aquino', '5103601', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dom Basílio', '2910107', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dom Cavati', '3122504', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dom Eliseu', '1502939', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dom Expedito Lopes', '2203404', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dom Feliciano', '4306502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Domingos Martins', '3201902', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Domingos Mourão', '2203420', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dona Emma', '4205100', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dona Eusébia', '3122900', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Douradina', '5003504', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Douradina', '4107256', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dourado', '3514304', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Douradoquara', '3123502', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dourados', '5003702', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Doutor Camargo', '4107306', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Doutor Maurício Cardoso', '4306734', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Doutor Pedrinho', '4205159', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Doutor Severiano', '2403202', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Doutor Ulysses', '4128633', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Dumont', '3514601', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Duque Bacelar', '2103901', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Duque de Caxias', '3301702', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Echaporã', '3514700', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Edealina', '5207352', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Edéia', '5207402', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Eirunepé', '1301407', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Elias Fausto', '3514908', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Elisiário', '3514924', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Elói Mendes', '3123601', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Embaúba', '3514957', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Embu', '3515004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Embu das Artes', '3515004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Embu-Guaçu', '3515103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Emilianópolis', '3515129', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Encantado', '4306809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Engenheiro Beltrão', '4107504', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Engenheiro Coelho', '3515152', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Engenheiro Navarro', '3123809', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Engenheiro Paulo de Frontin', '3301801', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Engenho Velho', '4306924', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Entre Folhas', '3123858', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Entre Rios', '2910503', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Entre Rios de Minas', '3123908', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Entre Rios do Oeste', '4107538', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Entre Rios do Sul', '4306957', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Entre-Ijuís', '4306932', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ererê', '2304277', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Erico Cardoso', '2900504', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ermo', '4205191', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Erval Grande', '4307203', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Erval Seco', '4307302', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Erval Velho', '4205209', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Escada', '2605202', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esperança', '2506004', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esperança do Sul', '4307450', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esperança Nova', '4107520', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esperança', '2506004', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esperança do Sul', '4307450', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Espigão Alto do Iguaçu', '4107546', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Espigão D''Oeste', '1100098', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Espinosa', '3124302', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esplanada', '2910602', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estância Velha', '4307609', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Esteio', '4307708', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estiva', '3124500', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estrela', '4307807', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estrela Dalva', '3124609', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estrela de Alagoas', '2702553', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estrela do Indaiá', '3124708', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estrela do Norte', '5207501', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estrela do Sul', '3124807', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Estrela Velha', '4307815', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Euclides da Cunha', '2910701', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Euclides da Cunha Paulista', '3515350', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Eugenópolis', '3124906', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Eunápolis', '2910727', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Exu', '2605301', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Extrema', '3125101', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Extremoz', '2403608', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fagundes Varela', '4307864', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Faina', '5207535', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fama', '3125200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Farol', '4107553', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Faria Lemos', '3125309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Farias Brito', '2304301', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Faro', '1503002', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Faxinal', '4107603', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Faxinal dos Guedes', '4205308', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Faxinalzinho', '4308052', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Feira de Santana', '2910800', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Feira Grande', '2702603', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Feira Nova', '2605400', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Feira Nova', '2802205', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Felício dos Santos', '3125408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Felipe Guerra', '2403707', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Felisburgo', '3125606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Felixlândia', '3125705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fernandópolis', '3515509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fernando de Noronha', '2605459', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fernando Falcão', '2104081', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fernando Pedroza', '2403756', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fernandes Pinheiro', '4107736', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fernandes Tourinho', '3125804', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ferraz de Vasconcelos', '3515707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ferreira Gomes', '1600238', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ferreiros', '2605509', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ferrugem', '4107751', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fervedouro', '3125952', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Flor da Serra do Sul', '4107850', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Flor do Sertão', '4205357', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Florânia', '2403806', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Flora Rica', '3515806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Floreal', '3515905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Flores', '2605608', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Flores da Cunha', '4308201', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Floresta', '4107900', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Floresta', '2605707', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Floresta Azul', '2911006', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Florestal', '3126000', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Florestopolis', '4108007', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Floriano', '2203909', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Floriano Peixoto', '4308250', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Florianópolis', '4205407', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Flórida', '4108106', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Flórida Paulista', '3516002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Florínia', '3516101', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fonte Boa', '1301605', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fontoura Xavier', '4308300', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Formiga', '3126109', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Formigueiro', '4308409', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Formosa', '5208004', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Formosa do Oeste', '4108205', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Formosa do Rio Preto', '2911105', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Formoso', '3126208', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Formoso', '5208103', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Foz do Iguaçu', '4108304', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Foz do Jordão', '4108452', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fraiburgo', '4205506', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Alves', '4108320', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Ayres', '2204105', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Badaró', '3126505', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Beltrão', '4108403', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Dumont', '3126604', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Macedo', '2204154', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Morato', '3516309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Santos', '2204204', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Franciscópolis', '3126752', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Franca', '3516200', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francinópolis', '2204006', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Ayres', '2204105', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Badaró', '3126505', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Beltrão', '4108403', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Dumont', '3126604', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Macedo', '2204154', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Morato', '3516309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Francisco Santos', '2204204', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Franciscópolis', '3126752', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Frei Gaspar', '3126802', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Frei Inocêncio', '3126901', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Frei Lagonegro', '3126950', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Frei Miguelinho', '2605806', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fronteira', '3127008', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fronteira dos Vales', '3127057', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fruta de Leite', '3127073', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Frutal', '3127107', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Fundão', '3202207', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Funilândia', '3127206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gabriel Monteiro', '3516507', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gado Bravo', '2506251', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Galinhos', '2404101', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gameleira', '2605905', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gameleiras', '3127339', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Garanhuns', '2606002', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Garibaldi', '4308607', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Garuva', '4205803', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gavião', '2911253', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gavião Peixoto', '3516853', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gentil', '4308854', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('General Câmara', '4308805', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('General Carneiro', '4108502', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('General Maynard', '2802502', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('General Salgado', '3516903', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gentio do Ouro', '2911303', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Getúlio Vargas', '4308904', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Getulina', '3517000', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goiana', '2606200', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goianá', '3127388', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goianápolis', '5208400', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goianésia', '5208608', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goianésia do Pará', '1503093', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goiânia', '5208707', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goiás', '5208905', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goiatins', '1709005', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goiatuba', '5209101', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goioerê', '4108601', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Goioxim', '4108650', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gonçalves', '3127404', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gonçalves Dias', '2104404', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gouvelândia', '5209150', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gourupa', '1503101', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Graça', '2304657', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Graça Aranha', '2104701', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Grão Mogol', '3127800', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Grão Pará', '4206108', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gravatá', '2606408', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gravataí', '4309209', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gravatal', '4206207', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guabiju', '4309258', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guabiruba', '4206306', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaiçara', '3517208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guairaçá', '4108908', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaíra', '3517406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaíra', '4108809', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guamiranga', '4108957', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guamirim', '4206504', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guamirim', '4109005', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guamurama', '4128104', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guapimirim', '3301850', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guapirama', '4109005', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guapó', '5209200', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarabira', '2506301', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaraci', '3517901', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaraci', '4109203', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaraciaba', '3128204', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaraciaba', '4206405', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaraciaba do Norte', '2305001', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaracy', '3128303', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaralhos', '3518800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarani', '3128402', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarani das Missões', '4309506', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarani de Goiás', '5209408', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarani do Oeste', '3518008', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaraniaçu', '4109302', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarapari', '3202405', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarapuava', '4109401', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaraqueçaba', '4109500', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarapuava', '4109401', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaraqueçaba', '4109500', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaratuba', '4109609', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarda-Mor', '3128608', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guareí', '3518107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guariba', '3518206', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaribas', '2204303', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guariciaba do Norte', '2305001', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarinos', '5209457', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarituba', '4109609', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarujá', '3518701', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guarulhos', '3518800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guatambú', '4206553', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaxupé', '3128709', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guaycurus', '5003702', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guia Lopes da Laguna', '5003801', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guia Lopes', '5003801', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guia Lopes da Laguna', '5003801', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guiburi', '2911600', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guimarânia', '3128808', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guiratinga', '5104203', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guiricema', '3128907', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gurinhatã', '3129004', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gurjão', '2506400', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gurupi', '1709500', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gurupá', '1503200', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Gustavo', '4309209', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Guzolândia', '3518503', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Harmonia', '4309555', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Herval', '4309571', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Herval D''Oeste', '4206702', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Herveiras', '4309605', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Hidrolândia', '5209705', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Hidrolina', '5209804', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Holambra', '3519055', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Honório Serpa', '4109658', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Horizonte', '2305233', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Horizontina', '4309704', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Hortolândia', '3519071', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Hugo Napoleão', '2204501', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Hulha Negra', '4309753', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Humaitá', '1301704', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Humaitá', '4309803', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iacanga', '3519105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iaciara', '5209903', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iacri', '3519204', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iaçu', '2911709', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iapu', '3129103', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iaras', '3519253', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iati', '2606101', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibaiti', '4109708', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibarama', '4309902', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibaretama', '2305266', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibatinga', '3519303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibema', '4109757', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibertioga', '3129202', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibia', '3129301', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiaçá', '4309803', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiá', '3129400', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiam', '4206900', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiracatu', '3129509', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiraci', '3129608', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiraçu', '3202504', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiraiaras', '4309951', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirajuba', '2606200', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirama', '4207007', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirapuitã', '4309977', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirapuã', '2911808', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirarema', '3519402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirataia', '2911907', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirité', '3129806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibirubá', '4310009', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibitiara', '2912004', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibitinga', '3519501', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibitirama', '3202603', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibitiúra de Minas', '3129905', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibituruna', '3130002', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibiúna', '3519709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ibotirama', '2912103', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Icapuí', '2305307', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Icaraí', '2305406', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Icaraí de Minas', '3130051', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Icaraíma', '4109807', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Icatu', '2105003', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Içara', '4207007', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Igreja Nova', '2703303', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Igrejinha', '4310108', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Igrejinha', '4310108', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguaba Grande', '3301876', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguape', '3520301', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguaracy', '2606309', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguaraçu', '4110003', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguatama', '3130101', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguatemi', '5003900', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iguatu', '2305506', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ijaci', '3130200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ijuí', '4310207', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ilha Comprida', '3520426', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ilha das Flores', '2802601', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ilha Solteira', '3520442', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ilhéus', '2912901', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ilhota', '4207106', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ilhabela', '3520400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Imaculada', '2506707', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Imbaú', '4109906', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Imbé', '4310330', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Imbituba', '4207205', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Imbuia', '4207304', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Indaiabira', '3130309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Indaiatuba', '3520509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Independência', '4310405', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Indiana', '3520608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Indianópolis', '3130408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Indiara', '5209952', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Indiaporã', '3520707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ingaí', '3130507', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ingá', '2506806', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Inhapim', '3130606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Inhaúma', '3130655', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Inhuma', '2204600', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Inhumas', '5210000', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Inimutaba', '3130705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipaba', '3130804', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipameri', '5210109', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipanema', '3130903', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipanguaçu', '2404200', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipaussu', '3520806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipatinga', '3131307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipatinga', '3131307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipauçu', '3520806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipeúna', '3520905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiaçu', '3131158', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiaú', '2913008', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiguá', '3521002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga', '4110201', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga de Goiás', '5209952', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga do Norte', '5104500', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga do Sul', '4310413', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga', '4110201', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga', '5210000', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga', '5104500', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipira', '4207403', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga do Norte', '5104500', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga do Sul', '4310413', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga', '4110201', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga', '5210000', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipiranga', '5104500', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipira', '4207403', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipoá', '3521101', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iporá', '5210208', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iporã', '4110607', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iporã do Oeste', '4207502', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipuã', '3521200', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipu', '2305605', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipuaçu', '4207650', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipueiras', '2305654', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipueira', '2404309', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipumirim', '4207700', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ipupiara', '2913107', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracema', '1400282', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracemápolis', '3521309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iraceminha', '4207759', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracema', '2305704', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracema', '1400282', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracemápolis', '3521309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iraceminha', '4207759', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracema', '2305704', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracema', '1400282', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracemápolis', '3521309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iraceminha', '4207759', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracemápolis', '3521309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iraceminha', '4207759', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iracemápolis', '3521309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iraceminha', '4207759', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iranduba', '1301803', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irati', '4107709', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irati', '4110706', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irauçuba', '2305803', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irecê', '2914604', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iretama', '4110805', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irineópolis', '4207809', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irituia', '1503408', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Irupi', '3202652', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Isaías Coelho', '2204808', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaberaba', '2914703', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaberaí', '5211206', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabi', '2802700', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabira', '3131703', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabirinha', '3131802', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabirito', '3131901', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaboraí', '3301900', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabuna', '2914802', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacambira', '3132008', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacarambi', '3132107', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacoatiara', '1301902', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacuruba', '2606408', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacurubi', '4310504', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itadam', '3132206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itagimirim', '2914901', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguaçu', '3202702', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguaí', '3302007', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguaru', '5211305', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguara', '3132206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguari', '5211404', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguaru', '5211305', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguatins', '1709807', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itai', '3521804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaí', '3521804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaiópolis', '4208104', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaim', '3521903', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaipava do Grajaú', '2105102', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaipé', '3132305', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaipulândia', '4110904', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaitinga', '2306107', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaituba', '1503606', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajá', '2404807', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajá', '5211503', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajaí', '4208203', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaju', '3132404', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaju do Colônia', '2915007', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajubá', '3132503', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajubá', '3132503', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajuípe', '2915106', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Italva', '3302056', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamaraju', '2915205', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamarandiba', '3132602', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamari', '2915304', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambacuri', '3132701', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambaracá', '4111001', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambé', '2915403', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambé', '4101101', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambé do Mato Dentro', '3132800', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamogi', '3132909', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamonte', '3133006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanagra', '2915502', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhaém', '3522109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhandu', '3133105', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhangá', '5104542', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhomi', '3133204', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhém', '2915601', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itano', '3133303', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaobim', '3133402', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaoca', '3522208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaocara', '3302106', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaquara', '2915700', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaquaquecetuba', '3523107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itarana', '3202900', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaraju', '2915809', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itararé', '3522307', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itarema', '2306256', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itariri', '3522406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itarumã', '5211701', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itatinga', '3522505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itatira', '2306306', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaú', '2404906', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaúba', '5104559', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaúna', '3133808', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaúna do Sul', '4111208', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itá', '4208201', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaara', '4310603', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaberaí', '5211206', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaberá', '3521705', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaberaba', '2914703', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabira', '3131703', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabirito', '3131901', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itabuna', '2914802', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacarambi', '3132107', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacoatiara', '1301902', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacuruba', '2606408', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itacurubi', '4310504', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguara', '3132206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguari', '5211404', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguaru', '5211305', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaguatins', '1709807', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaí', '3521804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaiópolis', '4208104', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaipava do Grajaú', '2105102', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaipé', '3132305', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaipulândia', '4110904', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaitinga', '2306107', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaituba', '1503606', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajá', '2404807', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajá', '5211503', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajaí', '4208203', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaju', '3132404', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaju do Colônia', '2915007', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajubá', '3132503', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itajuípe', '2915106', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Italva', '3302056', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamaraju', '2915205', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamarandiba', '3132602', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamari', '2915304', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambacuri', '3132701', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambaracá', '4111001', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambé', '2915403', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambé', '4101101', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itambé do Mato Dentro', '3132800', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamogi', '3132909', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itamonte', '3133006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanagra', '2915502', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhaém', '3522109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhandu', '3133105', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhangá', '5104542', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhomi', '3133204', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itanhém', '2915601', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaobim', '3133402', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaoca', '3522208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaocara', '3302106', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaquara', '2915700', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaquaquecetuba', '3523107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itarana', '3202900', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itariri', '3522406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itarumã', '5211701', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itatinga', '3522505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itatira', '2306306', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaú', '2404906', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaúba', '5104559', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaúna', '3133808', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaúna do Sul', '4111208', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itaverava', '3133907', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itayá', '3134004', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itinga', '3134103', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itiquira', '5104609', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itirapina', '3523602', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itirapuã', '3523701', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itiruçu', '2916005', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itiúba', '2916104', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itobi', '3523800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itororó', '2916203', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itu', '3523909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ituaçu', '2916302', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ituberá', '2916401', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ituiutaba', '3134202', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itumbiara', '5211503', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itumirim', '3134301', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itupeva', '3524006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itupiranga', '1503705', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Iturama', '3134400', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Itutinga', '3134509', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ituverava', '3524105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivaí', '4111307', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivaiporã', '4111406', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivaté', '4111505', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivatuba', '4111604', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivinhema', '5004709', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivolândia', '5211800', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivorá', '4311304', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ivoti', '4310801', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ixhuatlán', '3134608', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaboatão dos Guararapes', '2607901', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaboticabal', '3524303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaboticatubas', '3134707', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacaraú', '2506905', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacaré', '3134806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacarezinho', '4111703', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacarpe', '3134905', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaci', '3524402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaciara', '5104807', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacinópolis', '1100320', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacinto', '3135001', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacinto Machado', '4207909', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaco', '3524501', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacobina', '2916500', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacobina do Piauí', '2205102', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacuí', '3135100', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacuizinho', '4311254', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacundá', '1503754', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jacy', '3524600', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguapitã', '4111802', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguapita', '4111802', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguaquara', '2916609', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguara', '3135209', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguaraçu', '3135308', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguari', '4311502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguariúna', '3524709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguaribara', '2306405', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguaribe', '2306504', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguaruana', '2306603', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaguaruna', '4208005', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaíba', '3135456', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaicós', '2205201', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jales', '3524808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jambeiro', '3524907', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jampruca', '3135506', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Janaúba', '3135605', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jandaia', '5004903', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jandaia do Sul', '4112108', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jandira', '3525003', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Janduís', '2405002', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jangada', '5104906', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Januária', '3135704', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Januário Cicco', '2405101', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Japaraíba', '3135803', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Japaratinga', '2703700', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Japaratuba', '2802908', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Japi', '2405200', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Japoatã', '2803005', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Japonvar', '3135902', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Japorã', '5005008', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Japurá', '4112200', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaqueira', '2607009', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaquirana', '4311601', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaraguá', '5212006', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaraguá do Sul', '4209102', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaramataia', '2703809', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jardim', '5005206', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jardim Alegre', '4112504', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jardim de Angicos', '2405309', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jardim do Mulato', '2205250', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jardim Olinda', '4112603', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jatobá', '2607108', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jati', '2306702', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jatobá', '2205276', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jataúba', '2607208', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jataí', '5212105', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaú', '3525300', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jaú do Tocantins', '1711509', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jeceaba', '3136009', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jenipapo de Minas', '3136108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jequeri', '3136207', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jequitaí', '3136306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jequitibá', '3136405', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jequitinhonha', '3136504', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jeremoabo', '2916708', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jericó', '2507002', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jeriquara', '3525409', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jerônimo Monteiro', '3203104', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jesuânia', '3136603', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jesuítas', '4112702', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jesúpolis', '5212204', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jijoca de Jericoacoara', '2306900', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joanópolis', '3525508', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Alfredo', '2607208', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Câmara', '2405408', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Costa', '2205300', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Dias', '2405606', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Monlevade', '3136702', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Neiva', '3203130', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Pessoa', '2507507', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Pinheiro', '3136306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Pessoa', '2507507', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('João Pinheiro', '3136306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joaquim Nabuco', '2607407', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joaquim Pires', '2205409', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joaquim Távora', '4112901', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joca Marques', '2205458', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jocelândia', '3136801', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jordânia', '3136900', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('José Bonifácio', '3525608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('José Gonçalves', '3137007', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('José Raydan', '3137106', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('José da Penha', '2405804', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('José de Freitas', '2205508', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Josefina', '3137205', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Josenópolis', '3137304', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Joviânia', '5212105', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juara', '5105106', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juarez Távora', '2507606', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juarina', '1711806', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juatuba', '3137403', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juazeirinho', '2507705', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juazeiro', '2916807', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juazeiro do Norte', '2307506', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juazeiro', '2916807', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juazeiro do Norte', '2307506', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juazeirinho', '2507705', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jucati', '2607506', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jucás', '2307605', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jucurutu', '2406001', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juína', '5105155', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juiz de Fora', '3137502', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Júlio Borges', '2205524', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Júlio de Castilhos', '4311700', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jumirim', '3525707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Junco do Seridó', '2507804', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jupi', '2607605', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juru', '2507903', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jurua', '1302108', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juruá', '1302108', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jurubim', '3137601', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Jurutí', '1503904', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juscimeira', '5105171', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Juvenília', '3137700', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Kaloré', '4111703', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laba', '3137809', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lábrea', '1302207', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lacerdópolis', '4207859', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ladainha', '3137908', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ladário', '5005206', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagamar', '3138005', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagarto', '2803104', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lages', '4209300', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa', '2508009', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Bonita do Sul', '4311239', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa da Canoa', '2704106', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa da Confusão', '1711905', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa da Prata', '3138104', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa de Dentro', '2508207', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa de Itaenga', '2607704', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa de Pedras', '2406100', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa de São Francisco', '2205557', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa do Barro do Piauí', '2205573', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa do Carro', '2607803', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa do Mato', '2105300', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa do Ouro', '2607902', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa do Piauí', '2205599', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa do Sítio', '2205607', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Dourada', '3138203', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Formosa', '3138302', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Grande', '3138401', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Grande', '2608009', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Nova', '2406209', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Salgada', '2406308', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Santa', '3138500', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoa Seca', '2508307', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoinha', '3526001', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lagoinha do Piauí', '2205706', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laguna', '4209409', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laguna Carapã', '5005255', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laje', '2917003', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laje do Muriaé', '3302205', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajeado', '4311403', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajeado', '5211909', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajeado do Bugre', '4311429', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedão', '2917102', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedinho', '2917201', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedo', '2608108', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedo do Tabocal', '2917300', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedão', '2917102', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedinho', '2917201', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedo', '2608108', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedo do Tabocal', '2917300', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajes', '2406407', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajes Pintadas', '2406506', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lambari', '3138609', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lambari D''Oeste', '5105234', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lamim', '3138708', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Landri Sales', '2205805', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lapão', '2917409', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lapa', '4109906', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lapinha da Serra', '3138807', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjal', '4113304', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjal', '3138906', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjal do Jari', '1600279', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjeiras', '2803203', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjeiras do Sul', '4113403', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lassance', '3139003', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lastro', '2508009', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laurentino', '4209458', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lauro de Freitas', '2917608', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lauro Muller', '4209508', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lavras', '3139102', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lavras da Mangabeira', '2307009', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lavras do Sul', '4311502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leandro Ferreira', '3139201', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lebon Régis', '4209607', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leme', '3526701', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leme do Prado', '3139250', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lençóis', '2917707', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lençóis Paulista', '3526800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leoberto Leal', '4209706', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leopoldina', '3139300', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leopoldo de Bulhões', '5212253', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leópolis', '4113429', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Liberato Salzano', '4311601', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Liberdade', '3139409', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Licínio de Almeida', '2917806', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lidianópolis', '4113452', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lima Campos', '2105359', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lima Duarte', '3139508', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limeira', '3526909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limeira do Oeste', '3139607', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limoeiro', '2608207', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limoeiro de Anadia', '2704205', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limoeiro do Ajuru', '1504001', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limoeiro do Norte', '2307108', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lindoeste', '4113502', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lindóia', '3527006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lindóia do Sul', '4209805', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Linha Nova', '4311627', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Linhares', '3203203', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lins', '3527105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Livramento', '2508207', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Livramento de Nossa Senhora', '2917905', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lizarda', '1712002', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Loanda', '4113601', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lobato', '4113700', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Logradouro', '2508307', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Londrina', '4113700', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lontra', '3139706', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lorena', '3527204', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lourdes', '3527253', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Louveira', '3527303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucas do Rio Verde', '5105259', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucélia', '3527402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucena', '2508406', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucianópolis', '3527501', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucrécia', '2406407', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Alves', '4209904', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Correia', '2205904', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Eduardo Magalhães', '2918002', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Gomes', '2406506', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luisburgo', '3139805', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luislândia', '3139904', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luminárias', '3140001', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lunardelli', '4113809', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lupércio', '3527600', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luz', '3140100', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luzerna', '4209953', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luziânia', '5212501', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luzilândia', '2206001', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Antônio', '3527709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maçambará', '4311643', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macarani', '2918101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macatuba', '3528004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macau', '2406605', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaubal', '3528103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macambira', '2803302', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macapá', '1600303', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaparana', '2608306', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macarani', '2918101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macatuba', '3528004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macau', '2406605', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaubal', '3528103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macambira', '2803302', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macapá', '1600303', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaparana', '2608306', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedão', '2917102', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedinho', '2917201', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedo', '2608108', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajedo do Tabocal', '2917300', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajes', '2406407', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lajes Pintadas', '2406506', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lambari', '3138609', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lambari D''Oeste', '5105234', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lamim', '3138708', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Landri Sales', '2205805', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lapão', '2917409', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lapa', '4109906', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lapinha da Serra', '3138807', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjal', '4113304', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjal', '3138906', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjal do Jari', '1600279', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjeiras', '2803203', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laranjeiras do Sul', '4113403', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lassance', '3139003', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lastro', '2508009', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Laurentino', '4209458', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lauro de Freitas', '2917608', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lauro Muller', '4209508', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lavras', '3139102', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lavras da Mangabeira', '2307009', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lavras do Sul', '4311502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leandro Ferreira', '3139201', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lebon Régis', '4209607', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leme', '3526701', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leme do Prado', '3139250', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lençóis', '2917707', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lençóis Paulista', '3526800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leoberto Leal', '4209706', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leopoldina', '3139300', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leopoldo de Bulhões', '5212253', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Leópolis', '4113429', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Liberato Salzano', '4311601', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Liberdade', '3139409', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Licínio de Almeida', '2917806', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lidianópolis', '4113452', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lima Campos', '2105359', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lima Duarte', '3139508', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limeira', '3526909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limeira do Oeste', '3139607', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limoeiro', '2608207', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limoeiro de Anadia', '2704205', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limoeiro do Ajuru', '1504001', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Limoeiro do Norte', '2307108', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lindoeste', '4113502', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lindóia', '3527006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lindóia do Sul', '4209805', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Linha Nova', '4311627', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Linhares', '3203203', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lins', '3527105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Livramento', '2508207', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Livramento de Nossa Senhora', '2917905', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lizarda', '1712002', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Loanda', '4113601', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lobato', '4113700', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Logradouro', '2508307', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Londrina', '4113700', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lontra', '3139706', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lorena', '3527204', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lourdes', '3527253', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Louveira', '3527303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucas do Rio Verde', '5105259', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucélia', '3527402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucena', '2508406', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucianópolis', '3527501', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lucrécia', '2406407', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Alves', '4209904', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Correia', '2205904', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Eduardo Magalhães', '2918002', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Gomes', '2406506', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luisburgo', '3139805', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luislândia', '3139904', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luminárias', '3140001', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lunardelli', '4113809', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Lupércio', '3527600', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luz', '3140100', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luzerna', '4209953', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luziânia', '5212501', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luzilândia', '2206001', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Luís Antônio', '3527709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maçambará', '4311643', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macarani', '2918101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macatuba', '3528004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macau', '2406605', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaubal', '3528103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macambira', '2803302', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macapá', '1600303', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaparana', '2608306', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macarani', '2918101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macatuba', '3528004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macau', '2406605', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaubal', '3528103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macambira', '2803302', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macapá', '1600303', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaparana', '2608306', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macarani', '2918101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macatuba', '3528004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macau', '2406605', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaubal', '3528103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macambira', '2803302', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macapá', '1600303', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaparana', '2608306', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macarani', '2918101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macatuba', '3528004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macau', '2406605', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaubal', '3528103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macambira', '2803302', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macapá', '1600303', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macaparana', '2608306', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macedônia', '3528202', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Machacalis', '3140209', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Machadinho', '4311700', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Machadinho D''Oeste', '1100136', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macieira', '4209805', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macuco', '3302304', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Macururé', '2918200', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Madalena', '2307208', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Madeiro', '2206100', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Madre de Deus', '2918309', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Madre de Deus de Minas', '3140308', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mãe d''Água', '2508505', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mãe do Rio', '1504050', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maetinga', '2918358', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mafra', '4209904', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Magalhães Barata', '1504100', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Magda', '3528301', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Magé', '3302403', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maiquinique', '2918408', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mairi', '2918457', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mairinque', '3528400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mairiporã', '3528509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maitinga', '3528608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Malaquias', '3140407', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Malhada', '2918507', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Malhada de Pedras', '2918606', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Malhada dos Bois', '2803401', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Malhador', '2803500', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mallet', '4113908', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Malta', '2508604', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mamanguape', '2508703', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mambaí', '5212600', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mamborê', '4114005', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mamonas', '3140506', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mampituba', '4311718', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manacapuru', '1302506', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manaíra', '2508802', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manaquiri', '1302555', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manari', '2608405', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manaus', '1302605', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manciporã', '3140605', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mandaguaçu', '4114104', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mandaguari', '4114203', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mandirituba', '4114302', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manduri', '3528707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manfrinópolis', '4114351', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mangaratiba', '3302502', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mangueirinha', '4114401', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manhuaçu', '3140704', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manhumirim', '3140803', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manicoré', '1302704', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manoel Emídio', '2206209', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manoel Ribas', '4114500', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manoel Viana', '4311759', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Manoel Urbano', '1200336', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mansidão', '2918705', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mantena', '3140902', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mantenópolis', '3203302', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maquiné', '4311775', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marabá', '1504209', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marabá Paulista', '3528806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracaí', '3528905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracanaú', '2307654', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracás', '2918804', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracanã', '1504308', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracaju', '5005400', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracanaú', '2307654', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracás', '2918804', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracanã', '1504308', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracaju', '5005400', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracanaú', '2307654', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracás', '2918804', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracanã', '1504308', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracaju', '5005400', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracanaú', '2307654', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracás', '2918804', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracanã', '1504308', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maracaju', '5005400', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maragogi', '2704304', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maragogipe', '2918903', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maraial', '2608504', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marajá do Sena', '2105409', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maranguape', '2307704', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marapanim', '1504407', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marapoama', '3529002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marataízes', '3203328', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maratá', '4311791', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marau', '4311809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maravilha', '2704403', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maravilha', '4210100', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maravilhas', '3141009', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcação', '2508901', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcelândia', '5105309', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcelino Ramos', '4311908', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcelino Vieira', '2406704', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Março', '3141108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marapanim', '1504407', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marapoama', '3529002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marataízes', '3203328', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maratá', '4311791', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marau', '4311809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maravilha', '2704403', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maravilha', '4210100', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maravilhas', '3141009', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcação', '2508901', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcelândia', '5105309', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcelino Ramos', '4311908', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcelino Vieira', '2406704', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marchador', '3141108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marco', '2307802', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcolândia', '2206308', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marcos Parente', '2206357', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marechal Cândido Rondon', '4114500', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marechal Deodoro', '2704502', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marechal Floriano', '3203344', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marechal Thaumaturgo', '1200344', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mariana', '3141207', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mariana Pimentel', '4311981', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mariano Moro', '4312007', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marianópolis do Tocantins', '1712507', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marilac', '3141306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marilândia', '3203401', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marilândia do Sul', '4114609', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marilena', '4114708', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mariluz', '4114807', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maringá', '4115200', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marinópolis', '3529101', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mário Campos', '3141405', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mariópolis', '4115309', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maripá', '4115358', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maripá de Minas', '3141504', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marituba', '1504423', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marizópolis', '2509008', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marliéria', '3141603', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marmelete', '3141702', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marmeleiro', '4115408', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marques de Souza', '4312056', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marruás', '3141801', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Martinópole', '2307901', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Martinópolis', '3529200', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Martinho Campos', '3141900', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Martins', '2406803', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Martins Soares', '3142007', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maruim', '2803609', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marumbi', '4115457', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Marzagão', '5212709', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mascote', '2919006', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Massapê', '2308008', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Massapê do Piauí', '2206407', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mata', '4312106', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mata de São João', '2919105', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mata Grande', '2704700', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mata Roma', '2105508', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matão', '3529309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mataraca', '2509057', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mateiros', '1712705', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matelândia', '4116001', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Materlândia', '3142106', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mateus Leme', '3142205', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mathias Lobato', '3142254', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matias Barbosa', '3142304', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matias Cardoso', '3142403', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matias Olímpio', '2206506', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matina', '2919204', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matinha', '2105607', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matinhas', '2509107', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matinhos', '4116100', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matipó', '3142502', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mato Castelhano', '4312130', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mato Grosso', '4116209', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mato Leitão', '4312155', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mato Queimado', '4312205', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mato Rico', '4116308', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mato Verde', '3142601', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matozinhos', '3142700', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matrinchã', '5212808', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matrinxa', '5212857', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matriz de Camaragibe', '2704809', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matupá', '5105608', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matureia', '2509156', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matutina', '3142809', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Matutina', '3142809', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mauá', '3529408', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mauá da Serra', '4116407', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Maués', '1302902', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Medeiros', '3142908', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Medeiros Neto', '2919303', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Medianeira', '4116506', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Medicilândia', '1504456', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Medina', '3143005', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Meleiro', '4210209', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Melgaço', '1504506', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mendes', '3302601', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mendonça', '3529507', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mercedes', '4116605', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mercês', '3143104', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Meridiano', '3529606', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Meruoca', '2308107', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mesópolis', '3529705', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mesquita', '3302858', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mesquita', '3143203', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Minador do Negrão', '2705006', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Minas do Leão', '4312254', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Minas Novas', '3143302', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Minduri', '3143401', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mineiros', '5213103', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mineiros do Tietê', '3529804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ministro Andreazza', '1101203', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mira Estrela', '3530000', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirabela', '3143500', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miracema', '3302700', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miracema do Tocantins', '1713205', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miracema do Tocantins', '1713205', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miracema do Tocantins', '1713205', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miradouro', '3143609', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miraguaí', '4312304', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miraí', '3143708', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miranda', '5005608', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirandópolis', '3530109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirangaba', '2919402', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirante', '2919501', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirante da Serra', '1101401', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirante do Paranapanema', '3530208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miraselva', '4116704', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirassol', '3530307', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirassol d''Oeste', '5105624', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirassolândia', '3530406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Miravânia', '3143807', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirim Doce', '4210308', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mirinzal', '2105805', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Missal', '4116803', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Missão Velha', '2308206', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mocajuba', '1504605', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mococa', '3530505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Modelo', '4210407', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Moeda', '3143906', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Moema', '3144003', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mogeiro', '2509206', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mogi das Cruzes', '3530604', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mogi Guaçu', '3530703', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mogi Mirim', '3530802', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Moiporá', '5213400', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Moita Bonita', '2803708', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Moju', '1504704', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mojuí dos Campos', '1504753', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mombuca', '3530901', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mombaça', '2308305', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mombuca', '3530901', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mombuca', '3530901', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monção', '2105904', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monções', '3531008', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monjolos', '3144102', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monsenhor Gil', '2206605', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monsenhor Hipólito', '2206654', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monsenhor Tabosa', '2308404', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montadas', '2509305', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montalvânia', '3144201', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montanha', '3203500', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montanhas', '2406902', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montauri', '4312304', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alegre', '1504803', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alegre', '2407009', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alegre', '3144300', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alegre de Goiás', '5213509', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alegre de Minas', '3144409', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alegre de Sergipe', '2803807', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alegre do Piauí', '2206704', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Alto', '3531107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Aprazível', '3531206', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Azul', '3144508', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Azul Paulista', '3531305', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Belo', '3144607', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Belo do Sul', '4312353', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Carlo', '4210506', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Carmelo', '3144706', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Castelo', '4210605', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Castelo', '3531404', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte das Gameleiras', '2407108', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte do Carmo', '1713601', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Formoso', '3144805', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Horebe', '2509404', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Mor', '3531800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Negro', '1101435', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Santo', '2919808', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Santo de Minas', '3144904', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Santo do Tocantins', '1705552', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monte Sião', '3145000', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monteiro', '2509503', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Monteiro Lobato', '3531701', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montenegro', '4312379', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montes Altos', '2106001', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montes Claros', '3145109', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montes Claros de Goiás', '5213707', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Montezuma', '3145208', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morada Nova', '2308503', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morada Nova de Minas', '3145307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morais', '3145406', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Moraújo', '2308602', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Moreira Sales', '4116902', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Moreno', '2608703', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mormaço', '4312387', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morpará', '2920005', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morretes', '4117009', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morrinhos', '5213806', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morrinhos', '2920104', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morrinhos do Sul', '4312403', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro Agudo', '3531909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro Agudo de Goiás', '5213905', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro Cabeça no Tempo', '2206753', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro da Fumaça', '4210704', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro da Garça', '3145505', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro do Chapéu', '2920203', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro do Chapéu do Piauí', '2206803', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro do Pilar', '3145604', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro Grande', '4210803', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro Redondo', '4312429', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morro Reuter', '4312445', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morros', '2106100', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mortugaba', '2920302', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Morungaba', '3532006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mossoró', '2408003', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mostardas', '4312452', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Motuca', '3532055', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mozarlândia', '5214002', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muaná', '1504803', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mucugê', '2920401', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mucuri', '2920500', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mucurici', '3203609', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muitos Capões', '4312478', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muitos Capões', '4312478', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muliterno', '4312502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mundo Novo', '2920808', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mundo Novo', '5005681', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Munhoz', '3145801', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Munhoz de Melo', '4117108', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muniz Ferreira', '2920907', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muniz Freire', '3203708', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muquém de São Francisco', '2921004', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muqui', '3203807', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muquiba', '3145900', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Murici', '2705105', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Murici dos Portelas', '2206902', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muricilândia', '1713957', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muritiba', '2921103', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Murutinga do Sul', '3532103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mutum', '3146007', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mutum Biyu', '4312601', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Mutunópolis', '5214101', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Muzambinho', '3146106', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nacip Raydan', '3146205', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nantes', '3532202', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nanuque', '3146304', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Naque', '3146403', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Narandiba', '3532301', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Natal', '2408102', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Natércia', '3146502', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Natividade', '1714203', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Natividade', '3303003', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Natividade da Serra', '3532400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Natividade do Carangola', '3146551', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Natuba', '2510000', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Navegantes', '4211306', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nazareno', '3146601', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nazaré', '2921202', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nazaré Paulista', '3532509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nazarezinho', '2510109', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nazária', '2206951', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Neópolis', '2803906', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nepomuceno', '3146700', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nerópolis', '5214200', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Neves Paulista', '3532608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nhamundá', '1303009', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nhandeara', '3532707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nicolau Vergueiro', '4312676', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nilo Peçanha', '2921301', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nilópolis', '3303201', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nina Rodrigues', '2106209', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ninheira', '3146759', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nioaque', '5005707', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nísia Floresta', '2408201', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Niterói', '3303300', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nobres', '5105905', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nonoai', '4312700', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nordestina', '2921400', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Normandia', '1400407', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nortelândia', '5106002', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nossa Senhora Aparecida', '2804003', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nossa Senhora da Glória', '2804201', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nossa Senhora das Dores', '2804300', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nossa Senhora de Lourdes', '2804409', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nossa Senhora do Livramento', '5106101', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nossa Senhora do Socorro', '2805000', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nossa Senhora dos Remédios', '2207009', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Aliança', '3532806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Aliança do Ivaí', '4117207', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Alvorada', '4312759', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Alvorada do Sul', '5006006', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova América', '5214309', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova América da Colina', '4117256', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Andradina', '5006204', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Araçá', '4312809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Aurora', '4117306', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Aurora', '5214408', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Bandeirantes', '5106150', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Bassano', '4312908', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Belém', '3146909', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Boa Vista', '4312957', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Brasilândia', '1100144', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Brasilândia D''Oeste', '1101203', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Campina', '3532822', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Canaã', '2921509', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Canaã do Norte', '5106200', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Cantu', '4117405', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Castilho', '3532848', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Colinas', '2106308', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Cruz', '2408300', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Era', '3147006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Esperança', '4117504', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Esperança do Piriá', '1504852', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Esperança do Sudoeste', '4117603', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Europa', '3532905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Fátima', '2921608', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Fátima', '4117702', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Floresta', '2510208', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Friburgo', '3303409', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Glória', '5214507', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Granada', '3533002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Guataporanga', '3533101', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Hartz', '4313017', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Ibiá', '2921707', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Iguaçu', '3303508', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Iguaçu de Goiás', '5214606', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Independência', '3533200', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Iorque', '2106407', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Itaberaba', '4211405', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Itarana', '2921806', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Lacerda', '5106226', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Lima', '3147105', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Londrina', '4117801', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Luzitânia', '3533309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Mamoré', '1100338', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Marilândia', '5106234', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Maringá', '5106242', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Módica', '3147204', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Monte Verde', '5106259', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Mutum', '5106267', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Nazaré', '5106275', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Odessa', '3533408', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Olímpia', '5106283', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Olímpia', '4117900', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Pádua', '4313082', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Palma', '4313108', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Palmeira', '2510307', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Petrópolis', '4313207', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Ponte', '3147303', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Porteirinha', '3147402', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Prata', '4313306', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Prata do Iguaçu', '4118007', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Ramada', '4313330', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Redenção', '2921905', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Resende', '3147501', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Roma', '5214705', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Roma do Sul', '4313355', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Rosalândia', '1714302', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Russas', '2308701', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Santa Bárbara', '4118106', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Santa Helena', '5106291', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Santa Rita', '4313371', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Santa Rosa', '4118205', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Serrana', '3147600', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Soure', '2922002', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Tebas', '4118304', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Timboteua', '1504902', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Trento', '4211504', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Ubiratã', '5106309', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova União', '1101483', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova União', '3147709', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova União', '4313496', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Venécia', '3203906', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Veneza', '4211603', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Veneza', '5214804', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Viçosa', '2922101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nova Xavantina', '5106317', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novais', '3533507', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Acre', '3147808', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Airão', '1303108', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Alegre', '1715003', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Aripuanã', '1303207', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Acordo', '1714203', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Barreiro', '4313397', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Brasil', '5214903', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Cabrais', '4313405', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Cruzeiro', '3147907', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Gama', '5215009', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Hamburgo', '4313421', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Horizonte', '2922200', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Horizonte', '3533606', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Horizonte do Norte', '5106408', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Horizonte do Oeste', '1101491', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Itacolomi', '4118403', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Jardim', '1715102', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Lino', '2705204', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Machado', '4313447', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Mundo', '5106507', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Oriente', '2309303', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Oriente de Minas', '3148004', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Planalto', '5215207', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Progresso', '1505037', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Repartimento', '1505060', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Santo Antônio', '5106606', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Santo Antônio', '2207108', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo São Joaquim', '5106705', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Tiradentes', '4313462', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Triunfo', '2922259', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Novo Xingu', '4313496', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nuporanga', '3533705', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Nuporanga', '3533705', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olaria', '3148103', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Óleo', '3533804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olho d''Água', '2510406', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olho d''Água das Cunhãs', '2106506', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olho d''Água do Casado', '2705303', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olho d''Água do Piauí', '2207207', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olho d''Água Grande', '2705402', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olho D''Água das Flores', '2705501', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olinda', '2609600', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olinda Nova do Maranhão', '2106605', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olindina', '2922309', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olivedos', '2510505', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oliveira', '3148202', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oliveira de Fátima', '1715508', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oliveira Fortes', '3148301', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oliveira dos Brejinhos', '2922408', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olivença', '2705600', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Olivinópolis', '3148400', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Onda Verde', '3533903', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oratórios', '3148509', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oriente', '3534000', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orindiúva', '3534109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oriximiná', '1505102', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orizânia', '3148608', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orizona', '5215306', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orlândia', '3534208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orleans', '4211702', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orobó', '2609709', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orocó', '2609808', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Orós', '2309402', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ortigueira', '4118502', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Osasco', '3534406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Oscar Bressane', '3534505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Osvaldo Cruz', '3534604', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Otacílio Costa', '4211751', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ourém', '1505201', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouricuri', '2609907', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ourilândia do Norte', '1505300', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ourinhos', '3534703', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Branco', '2408507', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Branco', '3148707', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Branco', '4313504', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Fino', '3148806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Preto', '3148905', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Preto do Oeste', '1101509', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Verde', '4211801', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Verde', '3534802', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Verde de Goiás', '5215405', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Verde de Minas', '3149002', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouro Verde do Oeste', '4118601', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouroeste', '3534752', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ourolândia', '2922507', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ouvidor', '5215504', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pacaembu', '3534901', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pacajá', '1505409', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pacajus', '2310107', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pacaraima', '1400456', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pacatuba', '2310206', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pacatuba', '2804909', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paço do Lumiar', '2107009', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pacoti', '2310305', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pacujá', '2310404', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Padre Bernardo', '5215603', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Padre Carvalho', '3149101', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Padre Marcos', '2207306', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Padre Paraíso', '3149150', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paes Landim', '2207355', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pai Pedro', '3149200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paial', '4211850', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paiçandu', '4118700', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paim Filho', '4313603', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paineiras', '3149309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Painel', '4211900', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pains', '3149408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paiva', '3149507', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pajeú do Piauí', '2207405', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palestina', '3149606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palestina', '3535007', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palestina de Goiás', '5215702', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palestina do Pará', '1505508', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palhano', '2310503', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palhoça', '4212007', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palma', '3149705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palma Sola', '4212056', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmácia', '2310602', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmas', '4212106', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmas', '1721000', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmas de Monte Alto', '2922606', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeira', '4118809', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeira', '4212205', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeira d''Oeste', '3535106', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeira dos Índios', '2705600', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeira do Piauí', '2207504', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeira do Tocantins', '1715706', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeirais', '2207553', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeiras', '2922705', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeiras de Goiás', '5215801', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeiras do Tocantins', '1715755', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeirândia', '2107207', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeirante', '1713802', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmeirópolis', '1715904', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmital', '3535205', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmital', '4118908', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmitau', '2922804', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmitinho', '4313702', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palmópolis', '3149804', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Palotina', '4119005', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Panamá', '5215900', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Panambi', '4313801', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pancas', '3204003', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pancarana', '2922903', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pancas', '3204003', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Panelas', '2610003', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Panorama', '3535303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pão de Açúcar', '2706307', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Papagaios', '3149903', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Papanduva', '4212205', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pape', '3557156', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paquetá', '2207603', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pará de Minas', '3150000', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paracambi', '3303607', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paracatu', '3150109', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paracuru', '2310801', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraguaçu', '3535402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraguaçu Paulista', '3535501', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraí', '4313900', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraíba do Sul', '3303706', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraibano', '2107503', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraibuna', '3535600', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraíso', '4314007', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraíso', '3535709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraíso das Águas', '5006279', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraíso do Norte', '4119104', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraíso do Sul', '4314023', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraíso do Tocantins', '1716102', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraisópolis', '3150208', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parambu', '2310900', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paramirim', '2923000', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paramoti', '2311007', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranã', '1716201', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranacity', '4119203', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranaguá', '4119302', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranaíba', '5006303', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranaiguara', '5216007', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranaíta', '5106655', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranapanema', '3535808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranapoema', '4119401', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranapuã', '3535907', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranavaí', '4119500', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paranhos', '5006352', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraopeba', '3150307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parapuã', '3536004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parari', '2510604', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paratinga', '2923109', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraty', '3303805', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraúna', '5216304', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parauapebas', '1505532', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paraú', '2408606', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parau', '2408606', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parazinho', '2408705', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pardinho', '3536103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parelhas', '2408804', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pariconha', '2706406', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parintins', '1303405', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paripiranga', '2923208', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paripueira', '2706505', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pariquera-Açu', '3536202', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parisi', '3536251', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parnaguá', '2207702', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parnaíba', '2207801', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parnamirim', '2408903', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parnamirim', '2610102', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Parnarama', '2107602', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passa Quatro', '3150406', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passa Tempo', '3150505', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passa Vinte', '3150604', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passabém', '3150703', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passagem', '2510703', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passagem Franca', '2107701', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passagem Franca do Piauí', '2207850', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passaúna', '3150802', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passira', '2610201', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passo de Camaragibe', '2706604', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passo do Sobrado', '4314106', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passo Fundo', '4314205', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passos', '3150901', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Passos Maia', '4212254', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pastos Bons', '2107800', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Patos', '2510802', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Patos de Minas', '3151008', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Patos do Piauí', '2207900', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Patrocínio', '3151107', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Patrocínio do Muriaé', '3151206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Patu', '2410105', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paty do Alferes', '3303854', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paty do Alferes', '3303854', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pau Brasil', '2923307', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pau D''Arco', '1505557', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pau D''Arco', '1716300', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pau D''Arco do Piauí', '2208007', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pauliceia', '3536301', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulínia', '3536400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulino Neves', '2107909', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulistana', '2208106', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulista', '2510901', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulista', '2610300', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulistânia', '3536509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulo Afonso', '2923406', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulo Bento', '4314304', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulo de Faria', '3536608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulo Frontin', '4119609', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulo Jacinto', '2706703', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paulo Lopes', '4212304', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pausa', '2923505', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pavão', '3151305', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Paverama', '4314403', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pavussu', '2208205', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pé de Serra', '2923604', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peabiru', '4119708', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peçanha', '3151404', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra', '2610409', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Azul', '3151503', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Bela', '3536707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Bonita', '3151602', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Branca', '2310702', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Branca do Amapari', '1600154', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra do Anta', '3151701', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra do Indaiá', '3151800', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Dourada', '3151909', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Grande', '2409109', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Lavrada', '2511008', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Mole', '2805109', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedra Preta', '5107004', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedralva', '3152006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedranópolis', '3536806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedrão', '2923703', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedregulho', '3536905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedreira', '3537002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedreiras', '2108006', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedrinhas', '2805208', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedrinhas Paulista', '3537101', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Avelino', '2409208', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Canário', '3204052', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro de Toledo', '3537150', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Gomes', '5006402', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro II', '2208304', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Laurentino', '2208403', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Leopoldo', '3152105', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Osório', '4314452', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pedro Teixeira', '3152204', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peixe', '1716508', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peixe-Boi', '1505607', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peixoto de Azevedo', '5107046', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pejuçara', '4314460', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pelotas', '4314502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Penaforte', '2310801', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Penalva', '2108105', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Penápolis', '3537309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pendências', '2409307', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Penha', '4212503', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pentecoste', '2310900', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pequeri', '3152303', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pequi', '3152402', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pequizeiro', '1716607', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Perdigão', '3152501', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Perdizes', '3152600', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Perdões', '3152709', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pereira Barreto', '3537408', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pereiras', '3537507', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pereiro', '2311007', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peri Mirim', '2108204', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Periquito', '3152808', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peritiba', '4212602', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Perobal', '4119906', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Perolândia', '5216453', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Peruíbe', '3537606', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pescador', '3152907', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pesqueira', '2610707', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Petrolândia', '2610806', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Petrolândia', '4212651', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Petrolina', '2611101', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Petrópolis', '3303904', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piaçabuçu', '2706802', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piacatu', '3537705', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piaçava', '2923802', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piatã', '2923901', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piau', '3153004', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Picada Café', '4314478', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piçarra', '1505631', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Picos', '2208502', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Picuí', '2511107', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piedade', '3537804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piedade de Caratinga', '3153103', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piedade de Ponte Nova', '3153202', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piedade do Rio Grande', '3153301', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piedade dos Gerais', '3153400', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piên', '4120003', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pilão Arcado', '2924008', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pilar', '2706901', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pilar', '2511206', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pilar do Sul', '3537903', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pilar de Goiás', '5216800', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pilões', '2511305', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pilõezinhos', '2511404', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pimenta', '3153509', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pimenta Bueno', '1100185', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pimentel', '2924107', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pindaí', '2924206', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pindamonhangaba', '3538000', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pindaré-Mirim', '2108253', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pindoba', '2707008', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pindobaçu', '2924305', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pindorama', '3538109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pindorama do Tocantins', '1717008', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhais', '4120102', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhal', '4314494', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhal da Serra', '4314502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhal de São Bento', '4120151', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhal Grande', '4314551', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhalão', '4120201', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhalzinho', '4212701', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinhalzinho', '3538208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinheiral', '3303953', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinheirinho do Vale', '4314601', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinheiro', '2108303', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinheiro Machado', '4314700', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pinheiro Preto', '4212800', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pintópolis', '3153608', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pio IX', '2208601', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pio XII', '2108402', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piquerobi', '3538307', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piquete', '3538505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piquiatuba', '1505656', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracaia', '3538604', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracanjuba', '5216909', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracema', '3153707', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracicaba', '3538709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracuruca', '2208700', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraí', '3304001', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraí do Norte', '2924404', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraí do Sul', '4120300', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirajuba', '3153806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirambu', '2805307', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraju', '3538808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirajuí', '3538907', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piranguçu', '3153905', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piranguinho', '3154002', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piranhas', '2707107', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piranhas', '5217105', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirapetinga', '3154101', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirapora', '3154200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirapozinho', '3539004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraquara', '4120409', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirassununga', '3539103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piratininga', '3539202', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piratuba', '4212909', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraúba', '3154309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pires do Rio', '5217204', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piritiba', '2924503', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirituba', '3539301', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitanga', '4120508', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitangueiras', '3539400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitangueiras', '4120607', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitangui', '3154408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piúma', '3204102', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piumhi', '3154507', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pium', '1717505', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Placas', '1505706', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto', '4314759', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto', '2924602', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto', '3539509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto', '4120656', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto Alegre', '4213006', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto da Serra', '5107061', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Platina', '3539608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poá', '3539707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poço Branco', '2409406', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poço Fundo', '3154606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poço Redondo', '2805406', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poço Verde', '2805505', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pocinhos', '2511503', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poconé', '5107103', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poções', '2924701', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poçoes', '2924701', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poema', '4120706', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pojuca', '2924800', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poloni', '3539806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pombal', '2511602', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pombos', '2611200', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pomerode', '4213105', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pompéia', '3539905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pompéu', '3154705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pongaí', '3540002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponta Grossa', '4120805', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponta Porã', '5006600', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontal', '3540101', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontal do Araguaia', '5107152', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontal do Paraná', '4120854', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontalina', '5217303', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontalinda', '3540200', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontes e Lacerda', '5107202', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Alta', '3154804', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Alta do Bom Jesus', '1717802', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Alta do Norte', '4213204', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Alta do Tocantins', '1717901', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Branca', '5107251', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Nova', '3154903', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Preta', '4314775', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Serrada', '4213303', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontes Gestal', '3540309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontinha', '2924909', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontinha do Cocho', '3540408', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Populina', '3540507', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracaia', '3538604', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracanjuba', '5216909', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracema', '3153707', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracicaba', '3538709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piracuruca', '2208700', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraí', '3304001', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraí do Norte', '2924404', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraí do Sul', '4120300', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirajuba', '3153806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirambu', '2805307', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraju', '3538808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirajuí', '3538907', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piranguçu', '3153905', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piranguinho', '3154002', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piranhas', '2707107', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piranhas', '5217105', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirapetinga', '3154101', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirapora', '3154200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirapozinho', '3539004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraquara', '4120409', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pirassununga', '3539103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piratininga', '3539202', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piratuba', '4212909', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piraúba', '3154309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pires do Rio', '5217204', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piritiba', '2924503', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitanga', '4120508', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitangueiras', '3539400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitangueiras', '4120607', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pitangui', '3154408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piúma', '3204102', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Piumhi', '3154507', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pium', '1717505', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Placas', '1505706', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto', '4314759', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto', '2924602', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto', '3539509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto', '4120656', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto Alegre', '4213006', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Planalto da Serra', '5107061', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Platina', '3539608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poá', '3539707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poço Branco', '2409406', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poço Fundo', '3154606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poço Redondo', '2805406', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poço Verde', '2805505', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pocinhos', '2511503', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poconé', '5107103', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poções', '2924701', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poema', '4120706', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pojuca', '2924800', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poloni', '3539806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pombal', '2511602', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pombos', '2611200', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pomerode', '4213105', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pompéia', '3539905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pompéu', '3154705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pongaí', '3540002', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponta Grossa', '4120805', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponta Porã', '5006600', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontal', '3540101', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontal do Araguaia', '5107152', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontal do Paraná', '4120854', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontalina', '5217303', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontalinda', '3540200', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontes e Lacerda', '5107202', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Alta', '3154804', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Alta do Bom Jesus', '1717802', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Alta do Norte', '4213204', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Alta do Tocantins', '1717901', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Branca', '5107251', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Nova', '3154903', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Preta', '4314775', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ponte Serrada', '4213303', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pontes Gestal', '3540309', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Populina', '3540507', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poranga', '2311106', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto', '2208809', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Acre', '1200401', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Alegre', '4314902', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Alegre do Norte', '5107301', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Alegre do Piauí', '2208858', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Amazonas', '4120904', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Barreiro', '4121001', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Belo', '4213401', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Calvo', '2707206', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto da Folha', '2805604', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto de Moz', '1505805', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto de Pedras', '2707305', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Feliz', '3540606', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Ferreira', '3540705', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Firme', '3155009', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Franco', '2108501', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Grande', '1600535', 26);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Lucena', '4315008', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Mauá', '4315057', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Murtinho', '5006907', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Nacional', '1718008', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Real', '3304118', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Real do Colégio', '2707404', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Rico', '4121100', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Rico do Maranhão', '2108808', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Seguro', '2925005', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto União', '4213500', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Velho', '1100205', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Vera Cruz', '4315073', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Vitória', '4121209', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Walter', '1200500', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poté', '3155108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Potengi', '2311205', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Potim', '3540754', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Potirendaba', '3540804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pouso Alegre', '3155207', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pouso Alto', '3155306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poxoréu', '5107400', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pracinha', '3540853', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prado', '2925104', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prado Ferreira', '4121258', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prados', '3155405', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Praia Grande', '3541000', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Praia Grande', '4213609', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prainha', '1505904', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pranchita', '4121308', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prata', '3155504', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prata', '2511701', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prata do Piauí', '2208908', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pratânia', '3541059', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pratápolis', '3155603', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pratinha', '3155702', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Alves', '3541109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Bernardes', '3541208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Bernardes', '3160006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Castello Branco', '4121407', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Castelo Branco', '3541307', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Dutra', '2109005', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Dutra', '2925203', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Epitácio', '3541406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Figueiredo', '1303536', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Getúlio', '4213708', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Jânio Quadros', '2925252', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Juscelino', '3155801', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Juscelino', '2109104', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Kennedy', '3204201', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Kennedy', '4315107', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Kubitschek', '3155900', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Lucena', '4315131', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Médici', '1100144', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Médici', '2109203', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Olegário', '3156007', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Prudente', '3541505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Sarney', '2109237', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Tancredo Neves', '2925302', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Venceslau', '3541604', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Primavera', '4315156', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Primavera', '1506001', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Primavera de Rondônia', '1100185', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Porto Xavier', '4315107', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Posse', '5217402', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poté', '3155108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Potengi', '2311205', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Potim', '3540754', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Potirendaba', '3540804', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pouso Alegre', '3155207', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pouso Alto', '3155306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Poxoréu', '5107400', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pracinha', '3540853', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prado', '2925104', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prado Ferreira', '4121258', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prados', '3155405', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Praia Grande', '3541000', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Praia Grande', '4213609', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prainha', '1505904', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pranchita', '4121308', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prata', '3155504', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prata', '2511701', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prata do Piauí', '2208908', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pratânia', '3541059', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pratápolis', '3155603', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pratinha', '3155702', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Alves', '3541109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Bernardes', '3541208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Bernardes', '3160006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Castello Branco', '4121407', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Castelo Branco', '3541307', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Dutra', '2109005', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Dutra', '2925203', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Epitácio', '3541406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Figueiredo', '1303536', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Getúlio', '4213708', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Jânio Quadros', '2925252', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Juscelino', '3155801', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Juscelino', '2109104', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Kennedy', '3204201', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Kennedy', '4315107', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Kubitschek', '3155900', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Lucena', '4315131', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Médici', '1100144', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Médici', '2109203', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Olegário', '3156007', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Prudente', '3541505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Sarney', '2109237', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Tancredo Neves', '2925302', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Presidente Venceslau', '3541604', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Primavera', '4315156', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Primavera', '1506001', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Primavera de Rondônia', '1100185', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Primeira Cruz', '2109252', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Primeiro de Maio', '4121506', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Princesa', '2511800', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Princesa Isabel', '2511909', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Professor Jamil', '5217600', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Progresso', '4315206', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Promissão', '3541703', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Propriá', '2805703', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Protásio Alves', '4315305', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prudente de Morais', '3156106', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Prudentópolis', '4121605', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pugmil', '1718504', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Pureza', '2409505', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Putinga', '4315404', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Puxinanã', '2512006', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quadra', '3541901', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quaraí', '4315453', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quartel Geral', '3156205', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quarto Centenário', '4121704', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quatá', '3542008', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quatiguá', '4121753', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quatis', '3304126', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quatro Barras', '4121803', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quatro Irmãos', '4315503', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quatro Pontes', '4121902', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quatá', '3542008', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quebrangulo', '2707602', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quedas do Iguaçu', '4122009', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Queimada Nova', '2209005', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Queimadas', '2512105', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Queimadas', '2925401', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Queimados', '3304142', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Queiroz', '3542107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Queluz', '3542206', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Queluzito', '3156304', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quijingue', '2925500', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quilombo', '4213807', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quinta do Sol', '4122108', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quintana', '3542305', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quinze de Novembro', '4315602', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quitandinha', '4122157', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quiterianópolis', '2311304', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quixabeira', '2925609', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quixadá', '2311403', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quixelô', '2311502', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quixeramobim', '2311601', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Quixeré', '2311700', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rafael Fernandes', '2410204', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rafael Godeiro', '2410303', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rafard', '3542404', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ramilândia', '4122207', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rancharia', '3542503', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Raposa', '2109302', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Raul Soares', '3156403', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Realeza', '4122306', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rebouças', '4122405', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Recife', '2611606', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Redenção', '1506100', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Redenção', '2311809', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Redenção da Serra', '3542602', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Redentora', '4315701', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Reduto', '3156502', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Regeneração', '2209104', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Regente Feijó', '3542701', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Reginópolis', '3542800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Registro', '3542909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Relvado', '4315750', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Remanso', '2925708', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Remígio', '2512204', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Renascença', '4122504', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Reriutaba', '2311908', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Resende', '3304200', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Resende Costa', '3156601', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Reserva', '4122603', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Reserva do Cabaçal', '5107509', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Reserva do Iguaçu', '4122652', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Resplendor', '3156700', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ressaquinha', '3156809', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachão', '2109401', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachão', '2925807', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachão das Neves', '2925906', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachão do Bacamarte', '2512303', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachão do Dantas', '2805802', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachão do Jacuípe', '2926003', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachinho', '1718504', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachinho', '3156908', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riacho da Cruz', '2410402', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riacho das Almas', '2611705', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riacho de Santana', '2926201', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riacho de Santana', '2410501', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riacho dos Cavalos', '2512402', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riacho Frio', '2209203', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachuelo', '2410600', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riachuelo', '2805901', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rialma', '5217709', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rianápolis', '5217808', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribamar Fiquene', '2109450', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribas do Rio Pardo', '5007103', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeira', '3543006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeira do Amparo', '2926300', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeira do Piauí', '2209302', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeira do Pombal', '2926409', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão', '3543105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Bonito', '3543204', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Branco', '3543238', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Cascalheira', '5107541', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Claro', '4122702', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Corrente', '3543253', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão das Neves', '3157005', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão do Largo', '2926508', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão do Pinhal', '4122801', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão do Sul', '3543303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão dos Índios', '3543402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Grande', '3543501', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Pires', '3543600', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirão Preto', '3543709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ribeirópolis', '2806008', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rifaina', '3543808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rincão', '3543907', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rinópolis', '3544004', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Acima', '3157104', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Azul', '4122900', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Bananal', '3204250', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Bom', '4123007', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Bonito', '3304309', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Bonito do Iguaçu', '4123106', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Branco', '1200401', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Branco', '3157203', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Branco do Ivaí', '4123205', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Branco do Sul', '4123304', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Brilhante', '5007202', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Claro', '3304408', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Claro', '3543907', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Crespo', '1100260', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio do Antônio', '2926607', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio do Campo', '4213401', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio do Fogo', '2410709', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio do Oeste', '4213500', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio do Pires', '2926706', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio do Sul', '4213609', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio dos Bois', '1719007', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio dos Cedros', '4213708', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio dos Índios', '4315750', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Espera', '3157302', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Formoso', '2611804', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Fortuna', '4213807', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Grande', '4315800', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Grande da Serra', '3544103', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Largo', '2707701', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Manso', '3157401', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Negrinho', '4213906', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Negro', '4123403', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Negro', '5007301', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Novo', '3157609', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Novo do Sul', '3204300', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Paranaíba', '3157708', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Pardo', '4315909', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Pardo de Minas', '3157807', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Piracicaba', '3157906', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Real', '2926805', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Rufino', '4214003', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Sono', '1719502', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Tinto', '2512501', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rio Vermelho', '3158003', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riolândia', '3544202', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riqueza', '4214102', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ritápolis', '3158102', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Riversul', '3544301', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Roca Sales', '4316105', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rochedo', '5007400', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rochedo de Minas', '3158201', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rodeio', '4214201', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rodeio Bonito', '4316204', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rodolfo Fernandes', '2410808', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rogerópolis', '3158300', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rolândia', '4123502', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rolante', '4316303', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rolim de Moura', '1100286', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ronda Alta', '4316402', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rondinha', '4316501', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rondon', '4123601', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rondon do Pará', '1506183', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rondonópolis', '5107602', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rondônia', '4316600', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Roque Gonzales', '4316709', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rorainópolis', '1400472', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rosana', '3544400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rosário', '2109500', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rosário da Limeira', '3158409', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rosário do Catete', '2806107', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rosário do Ivaí', '4123700', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rosário do Sul', '4316808', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rosário Oeste', '5107701', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Roteiro', '2707809', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rubelita', '3158508', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rubiácea', '3544509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rubiataba', '5218004', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rubim', '3158607', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rubinéia', '3544608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Rurópolis', '1506191', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Russas', '2312005', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ruy Barbosa', '2410907', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ruy Barbosa', '2927002', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sabará', '3158706', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sabáudia', '4123809', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sabinópolis', '3158805', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sabino', '3544707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sacramento', '3158904', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sagrada Família', '4317106', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sagres', '3544806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sairé', '2611903', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salete', '4214300', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salinas', '3159001', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salinas da Margarida', '2927101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salinópolis', '1506209', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salitre', '2312104', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salmourão', '3544905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saloá', '2612001', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saltinho', '3545001', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto', '3545100', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto da Água Verde', '4123908', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto de Pirapora', '3545209', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto do Céu', '5107750', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto do Itararé', '4124005', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto do Jacuí', '4317205', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto do Lontra', '4124104', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salto Grande', '3545308', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salvador', '2927408', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salvador das Missões', '4317254', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salvador do Sul', '4317304', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Salvaterra', '1506308', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sambaíba', '2109559', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sampaio', '1719007', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sananduva', '4317403', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sanclerlândia', '5218509', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sandolândia', '1720005', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sandovalina', '3545407', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Adélia', '3545506', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara', '2927507', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara', '3546007', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara d''Oeste', '3546106', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara de Goiás', '5218301', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara do Leste', '3159209', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara do Monte Verde', '3159308', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara do Pará', '1506506', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara do Sul', '4317403', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Bárbara do Tugúrio', '3159407', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Branca', '3546205', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Clara d''Oeste', '3546254', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Clara do Sul', '4317502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz', '2927606', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz', '3546304', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz Cabrália', '2927705', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz da Baixa Verde', '2612100', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz da Conceição', '3546403', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz da Esperança', '3546502', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz das Palmeiras', '3546601', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz de Goiás', '5218400', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz de Minas', '3159506', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz de Monte Castelo', '4124203', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz do Arari', '1506605', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz do Capibaribe', '2612209', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz do Escalvado', '3159605', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz do Piauí', '2209500', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz do Rio Pardo', '3546700', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz do Sul', '4317601', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Cruz dos Milagres', '2209559', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Efigênia de Minas', '3159704', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Fé', '4124302', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Fé de Goiás', '5218509', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Fé de Minas', '3159803', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Fé do Araguaia', '1719007', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Fé do Sul', '3546809', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Filomena', '2209609', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Filomena', '2612308', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Filomena do Maranhão', '2109609', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Gertrudes', '3546908', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Helena', '4214409', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Helena', '3547005', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Helena de Goiás', '5219002', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Helena de Minas', '3159902', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Helena do Maranhão', '2109708', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Inês', '2411004', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Inês', '2512709', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Inês', '2927804', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Inês', '2109807', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Isabel', '3547104', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Isabel', '5219101', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Isabel do Ivaí', '4124401', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Isabel do Rio Negro', '1303601', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Izabel do Oeste', '4124500', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Izabel do Pará', '1506704', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Lúcia', '3547203', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Lúcia', '3159993', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Luz', '2927903', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Luzia', '3547302', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Luzia', '2512808', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Luzia', '3160000', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Luzia D''Oeste', '1100294', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Luzia do Norte', '2707908', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Luzia do Pará', '1506803', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Margarida', '3160109', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Margarida do Sul', '4317700', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria', '4317809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria da Boa Vista', '2612407', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria da Serra', '3547401', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria de Jetibá', '3204409', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria do Cambucá', '2612506', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria do Pará', '1506902', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria do Salto', '3160208', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria do Suaçuí', '3160307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria das Barreiras', '1507009', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria Madalena', '3304606', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Maria do Oeste', '4124609', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Mariana', '4124708', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Mercedes', '3547500', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Mônica', '4124807', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Quitéria', '2312202', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Quitéria do Maranhão', '2109906', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita', '3547609', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita', '2512907', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita', '2928000', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita de Caldas', '3160406', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita de Cássia', '2928109', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita de Ibitipoca', '3160455', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita de Jacutinga', '3160505', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita de Minas', '3160604', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Araguaia', '5219200', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Este', '3160703', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Novo Destino', '5220000', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Pardo', '5007558', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Passa Quatro', '3547708', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Sapucaí', '3160802', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Tocantins', '1720203', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rita do Trivelato', '5107768', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa', '4317908', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa da Serra', '3160901', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa de Goiás', '5220059', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa de Lima', '4214607', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa de Lima', '2806206', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa do Pardo', '5007608', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa do Purus', '1200435', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Rosa do Sul', '4214706', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Salete', '3547807', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Teresa', '3204508', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Terezinha', '4214805', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Terezinha', '5107776', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Terezinha de Goiás', '5220109', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Terezinha de Itaipu', '4125003', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Terezinha do Progresso', '4214904', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Vitória', '3161008', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Vitória do Palmar', '4318005', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santa Vitória do Palmar', '4318005', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana', '1303908', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana da Boa Vista', '4318054', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana da Ponte Pensa', '3547906', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana da Vargem', '3161107', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana de Cataguases', '3161206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana de Mangueira', '2513004', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana de Parnaíba', '3548003', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana de Pirapama', '3161305', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Acaraú', '2312301', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Araguaia', '1507207', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Cariri', '2312400', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Deserto', '3161404', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Garambéu', '3161503', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Ipanema', '2708005', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Itararé', '4125102', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Jacaré', '3161602', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Manhuaçu', '3161651', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Maranhão', '2109955', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Matos', '2411103', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Mundaú', '2708104', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Paraíso', '3161701', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Piauí', '2209708', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Riacho', '3161800', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do São Francisco', '2806305', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana do Seridó', '2411202', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santana dos Garrotes', '2513103', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santanópolis', '2928208', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santarém', '1506308', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santarém Novo', '1507009', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santiago', '4318302', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santiago do Sul', '4215001', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Amaro', '2928307', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Amaro da Imperatriz', '4215100', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Amaro das Brotas', '2806503', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Amaro do Maranhão', '2110003', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Anastácio', '3548102', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo André', '3548201', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo André', '2513202', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Ângelo', '4318401', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio', '2411400', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio da Alegria', '3548300', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio da Barra', '5219358', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio da Patrulha', '4318500', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio da Platina', '4125201', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio das Missões', '4318609', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio de Goiás', '5219408', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio de Jesus', '2928406', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio de Lisboa', '2210005', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio de Pádua', '3304705', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio de Posse', '3548409', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Aventureiro', '3161909', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Caiuá', '4125300', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Descoberto', '5219507', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Grama', '3162006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Içá', '1303957', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Itambé', '3162105', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Jacinto', '3162204', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Jardim', '3548508', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Leste', '5107792', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Leverger', '5107800', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Monte', '3162303', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Palma', '4318617', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Paraíso', '4125409', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Pinhal', '3548607', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Planalto', '4318625', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio dos Lopes', '2110102', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio dos Milagres', '2210054', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Augusto', '4318708', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Estêvão', '2928505', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Expedito', '3548706', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Expedito do Sul', '4318807', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Inácio', '4125508', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Inácio do Piauí', '2210104', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Hipólito', '3162402', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Antônio do Sudoeste', '4125557', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Augusto', '4318708', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Estêvão', '2928505', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Expedito', '3548706', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santo Expedito do Sul', '4318807', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santópolis do Aguapeí', '3548805', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santos', '3549001', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Santos Dumont', '3162501', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Benedito', '2312509', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Benedito do Rio Preto', '2110201', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bentinho', '2513400', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento', '2513509', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento', '2110300', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento do Norte', '2411509', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento do Sapucaí', '3549100', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento do Sul', '4215209', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento do Tocantins', '1720302', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bento do Trairi', '2411608', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bernardo', '2110409', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bernardo do Campo', '3548706', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Bonifácio', '4215308', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Borja', '4318906', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Brás', '2708203', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Brás do Suaçuí', '3162550', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Caetano de Odivelas', '1507403', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Caetano do Sul', '3549209', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Caitano', '2612506', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Carlos', '3549308', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Carlos', '4215407', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Carlos do Ivaí', '4125607', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Cristóvão', '2806701', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Cristóvão do Sul', '4215456', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Desidério', '2928604', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos', '2513608', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos', '2928703', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos', '3162600', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos', '4215506', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos do Araguaia', '1507452', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos do Azeitão', '2110508', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos do Capim', '1507502', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos do Maranhão', '2110607', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos do Prata', '3162709', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Domingos das Dores', '3162808', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Felipe', '2928802', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Felipe d''Oeste', '1101484', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Félix', '2928901', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Félix de Minas', '3162907', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Félix do Coribe', '2928950', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Félix do Piauí', '2210203', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Félix do Tocantins', '1720401', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Fernando', '2411707', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco', '3163004', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco', '4319003', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco', '3549209', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco', '2513707', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco', '4319003', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Assis', '4319102', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Assis do Piauí', '2210302', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Goiás', '5220208', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Itabapoana', '3304804', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Paula', '3163103', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Paula', '4319120', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco de Sales', '3163202', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Brejão', '2110656', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Conde', '2929008', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Glória', '3163301', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Guaporé', '1101492', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Oeste', '2411806', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Pará', '1507601', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Piauí', '2210351', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Francisco do Sul', '4215605', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gabriel', '4319300', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gabriel', '2929107', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gabriel da Cachoeira', '1304104', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gabriel da Palha', '3204557', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gabriel do Oeste', '5007699', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Geraldo', '3163400', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Geraldo da Piedade', '3163509', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Geraldo do Araguaia', '1507700', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Geraldo do Baixio', '3163608', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo', '3304903', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Abaeté', '3163707', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Amarante', '2312806', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Amarante', '2411905', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Gurguéia', '2210401', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Pará', '3163806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Piauí', '2210500', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Rio Abaixo', '3163905', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Rio Preto', '3164002', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gonçalo do Sapucaí', '3164101', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Gotardo', '3164200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Jerônimo', '4319409', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Jerônimo da Serra', '4126001', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João', '2513806', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João', '2708302', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João', '4319508', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João', '4126100', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João Batista', '4215704', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João Batista do Glória', '3164309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Barra', '3305000', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Boa Vista', '3549100', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Lagoa', '3164408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Mata', '3164507', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Paraúna', '5220307', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Ponte', '3164606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Serra', '2210609', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Urtiga', '4319607', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João da Varjota', '2210625', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João das Missões', '3164705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João de Iracema', '3549209', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João de Meriti', '3305109', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João de Pirabas', '1507759', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João del Rei', '3164804', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Araguaia', '1507809', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Caiuá', '4126209', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Cariri', '2513905', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Ivaí', '4126308', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Jacuípe', '2929206', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Manhuaçu', '3164903', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Manteninha', '3165009', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Oeste', '4215803', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Pacuí', '3165108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Paraíso', '3165207', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Paraíso', '2110706', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Pau d''Alho', '3549258', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Piauí', '2210708', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Polêsine', '4319706', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Soter', '2110805', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Sul', '4215902', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João do Tigre', '2514002', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João dos Patos', '2110904', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João Evangelista', '3165306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São João Nepomuceno', '3165405', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Joaquim', '4216009', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Joaquim da Barra', '3549308', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Joaquim de Bicas', '3165504', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José', '4216108', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José da Barra', '3165603', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José da Bela Vista', '3549407', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José da Boa Vista', '4126407', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José da Lapa', '3165702', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José da Safira', '3165801', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José da Varginha', '3165900', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José de Caiana', '2514101', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José de Espinharas', '2514200', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José de Mipibu', '2412002', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José de Piranhas', '2514309', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José de Princesa', '2514408', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Alegre', '3166007', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Barreiro', '3549506', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Belmonte', '2612803', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Bonfim', '2514507', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Brejo do Cruz', '2514556', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Calçado', '3204607', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Campestre', '2412101', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Cedro', '4216207', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Egito', '2612902', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Goiabal', '3166106', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Herval', '4320002', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Hortêncio', '4320101', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Inhacorá', '4320200', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Jacuípe', '2929305', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Jacuri', '3166205', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Mantimento', '3166304', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Norte', '4320309', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Ouro', '4320408', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Piauí', '2210807', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Povo', '5107859', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Rio Claro', '5107909', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Rio Pardo', '3549605', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Sabugi', '2514606', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Seridó', '2412200', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Sul', '4320457', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José do Vale do Rio Preto', '3305133', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José dos Ausentes', '4320507', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José dos Basílios', '2111001', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José dos Campos', '3549902', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José dos Cordeiros', '2514705', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José dos Pinhais', '4126506', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José dos Quatro Marcos', '5108006', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São José dos Ramos', '2514804', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Julião', '2210906', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Leopoldo', '4320606', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Lourenço', '3166806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Lourenço da Mata', '2613009', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Lourenço do Oeste', '4216306', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Lourenço do Piauí', '2211003', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Lourenço do Sul', '4320705', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luís', '2111300', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luís de Montes Belos', '5220406', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luís do Curu', '2312905', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luís do Paraitinga', '3549902', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luís do Piauí', '2211102', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luís do Quitunde', '2708808', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Luís Gonzaga do Maranhão', '2111409', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Manuel', '3550009', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Marcos', '4320804', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Martinho', '4216405', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Martinho da Serra', '4320853', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Mateus', '3204706', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Mateus do Maranhão', '2111508', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Mateus do Sul', '4126605', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel', '2412509', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel Arcanjo', '3550108', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel da Baixa Grande', '2211201', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel das Matas', '2929404', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Aleixo', '2806800', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Anta', '3166905', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Fidalgo', '2211300', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Guamá', '1507908', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Iguaçu', '4126704', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Oeste', '4216504', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Passa Quatro', '5220505', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Tapuio', '2211409', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel do Tocantins', '1720500', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel dos Campos', '2709004', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Miguel dos Milagres', '2709103', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Nicolau', '4320903', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Paulo', '3550308', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Paulo de Olivença', '1304203', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro', '3550407', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro da Aldeia', '3305208', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro da Cipa', '5107909', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro da Serra', '4321000', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro das Missões', '4321109', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro de Alcântara', '4216603', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro do Iguaçu', '4126803', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro do Ivaí', '4126902', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro do Paraná', '4127009', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro do Piauí', '2211508', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro do Sul', '4321208', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Pedro do Turvo', '3550506', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Rafael', '2412558', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Raimundo das Mangabeiras', '2111607', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Raimundo do Doca Bezerra', '2111631', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Raimundo Nonato', '2211607', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Roberto', '2111672', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Romão', '3166954', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Roque', '3550605', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Roque de Minas', '3167002', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Salvador do Tocantins', '1720658', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião', '3550704', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião', '2709301', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião da Amoreira', '4127108', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião da Bela Vista', '3167101', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião da Boa Vista', '1508005', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião da Grama', '3550803', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião da Vargem Alegre', '3167200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião de Lagoa de Roça', '2514903', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Alto', '3305307', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Anta', '3167309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Caí', '4321307', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Maranhão', '3167408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Oeste', '3167507', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Paraíso', '3167606', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Passé', '2929503', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Rio Preto', '3167705', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Rio Verde', '3167804', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Tocantins', '1720808', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sebastião do Umbuzeiro', '2515008', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Sepé', '4321406', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Simão', '3550902', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Simão', '5219705', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Thomé das Letras', '3167903', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Valentim', '4321430', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Valentim do Sul', '4321455', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Valério', '1720857', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Valério do Sul', '4321463', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Vicente', '3551000', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Vicente de Minas', '3168000', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('São Vicente do Sul', '4321471', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sapiranga', '4321505', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sapopema', '4127207', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sapucaia', '3305406', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sapucaia do Sul', '4321604', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sapucaí-Mirim', '3168109', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saquarema', '3305505', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sarandi', '4127306', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sarandi', '4321703', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sarapuí', '3551109', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saraiva', '3168208', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sarzedo', '3168307', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Satuba', '2709400', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Satubinha', '2111706', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Satubinha', '2111706', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saubara', '2929602', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saudade do Desterro', '2515107', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saudades', '4216702', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Saúde', '2929701', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Schroeder', '4216801', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Schroeder', '4216801', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Seabra', '2929800', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sebastianópolis do Sul', '3551208', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sebastião Barros', '2211706', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sebastião Laranjeiras', '2929909', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sebastião Leal', '2211805', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sebastião Leal', '2211805', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Seberi', '4321802', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sede Nova', '4321836', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Segredo', '4321851', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Selbach', '4321901', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Selvíria', '5007707', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sem-Peixe', '3168406', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Amaral', '3168505', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Canedo', '5220604', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Cortes', '3168604', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Elói de Souza', '2412708', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Firmino', '3168703', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Georgino Avelino', '2412807', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Guiomard', '1200500', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador José Bento', '3168802', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Modestino Gonçalves', '3168901', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Pompeu', '2313002', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Rui Palmeira', '2709509', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Sá', '2313101', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Senador Salgado Filho', '4321950', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sentinela do Sul', '4322008', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serafina Corrêa', '4322107', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sericita', '3169008', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Seringueiras', '1101500', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sério', '4322206', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Seropédica', '3305554', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra', '3205000', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Alta', '4216900', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Azul', '3551307', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Azul de Minas', '3169057', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Branca', '2515206', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Caiada', '2412906', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra da Raiz', '2515404', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra do Mel', '2413003', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra do Navio', '1600055', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra do Ramalho', '2930006', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra do Salitre', '3169107', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra dos Aimorés', '3169206', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Dourada', '2930105', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Grande', '2515305', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Negra', '3551406', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Negra do Norte', '2413102', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Nova Dourada', '5107883', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serra Preta', '2930204', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrana', '3551505', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrania', '3169305', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrano do Maranhão', '2111805', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serranópolis', '5220687', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serranópolis do Iguaçu', '4127405', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serranos', '3169503', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrinha', '2930303', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrinha', '2413201', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrinha dos Pintos', '2413300', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrita', '2613207', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serro', '3169602', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Serrolândia', '2930402', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sertaneja', '4127504', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sertânia', '2613306', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sertão', '4322502', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sertão Santana', '4322536', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sertãozinho', '3551604', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sertãozinho', '3169701', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sete Barras', '3551703', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sete Lagoas', '3169800', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sete Quedas', '5007806', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Setubinha', '3169909', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Severiano de Almeida', '4322601', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Severiano Melo', '2413409', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Severínia', '3551802', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Silva Jardim', '3305604', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Silvânia', '5220703', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Silvanópolis', '1720907', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Simão Dias', '2807006', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Simão Pereira', '3170006', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Simonésia', '3170105', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Simplício Mendes', '2211904', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sinop', '5107909', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Siqueira Campos', '4127603', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sirinhaém', '2613405', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Siriri', '2807105', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sitio Novo', '2413508', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sitio Novo', '2111904', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sitio Novo do Tocantins', '1721004', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobradinho', '4322700', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobradinho', '2930501', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobral', '2313406', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobradinho', '2515404', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobradinho', '4322700', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobradinho', '2930501', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sobral', '2313406', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Socorro', '3552008', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Socorro do Piauí', '2212001', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Solânea', '2515503', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Soledade', '2515602', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Soledade', '4322809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Soledade de Minas', '3170204', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Solidão', '2613504', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Solonópole', '2313505', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sombrio', '4217007', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sonora', '5007939', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sorriso', '5107925', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sossêgo', '2515701', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Soure', '1508104', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sousa', '2515800', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Souto Soares', '2930600', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sucupira', '1721103', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sucupira do Norte', '2112001', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sucupira do Riachão', '2112100', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sud Mennucci', '3552107', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sul Brasil', '4217106', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sumaúma', '2515909', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sumaré', '3552206', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sumidouro', '3305703', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Surubim', '2613603', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Sussuarana', '2930709', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Suzanápolis', '3552305', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Suzano', '3552404', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tabaí', '4322858', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tabaporã', '5107941', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tabapuã', '3552503', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tabatinga', '3552602', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tabatinga', '1304260', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tabira', '2613702', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taboão da Serra', '3552800', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tabocas do Brejo Velho', '2930808', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tacaimbó', '2613801', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taciba', '3552909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tacuru', '5007954', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tacuru', '5007954', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taguaí', '3553006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taguatinga', '1721202', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taiaçu', '3553105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tailândia', '1508203', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taió', '4217205', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taiobeiras', '3170303', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taipas do Tocantins', '1721251', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taipu', '2413607', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquara', '4322809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaraçu de Minas', '3170402', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaral', '3553204', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaral de Goiás', '5220802', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquarana', '2707600', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquari', '4323005', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaritinga', '3553303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaritinga do Norte', '2613900', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquarituba', '3553402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquarivaí', '3553501', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaruçu do Sul', '4323104', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquarussu', '5007970', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarabai', '3553600', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarauacá', '1200609', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarrafas', '2313554', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tartarugalzinho', '1600154', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarumã', '3553659', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarumirim', '3170501', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tasso Fragoso', '2112209', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tatuí', '3553709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taubaté', '3553808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tavares', '4323203', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tefé', '1304302', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeira', '2516006', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeira de Freitas', '2931350', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeiras', '3170600', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeirópolis', '1101559', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tejupá', '3553857', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tejuçuoca', '2313604', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Telêmaco Borba', '4127702', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Telha', '2807204', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tenente Ananias', '2413706', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tenente Laurentino Cruz', '2413805', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tenente Portela', '4323302', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tacuru', '5007954', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taguaí', '3553006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taguatinga', '1721202', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taiaçu', '3553105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tailândia', '1508203', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taió', '4217205', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taiobeiras', '3170303', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taipas do Tocantins', '1721251', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taipu', '2413607', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquara', '4322809', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaraçu de Minas', '3170402', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaral', '3553204', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaral de Goiás', '5220802', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquarana', '2707600', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquari', '4323005', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaritinga', '3553303', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaritinga do Norte', '2613900', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquarituba', '3553402', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquarivaí', '3553501', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquaruçu do Sul', '4323104', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taquarussu', '5007970', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarabai', '3553600', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarauacá', '1200609', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarrafas', '2313554', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tartarugalzinho', '1600154', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarumã', '3553659', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tarumirim', '3170501', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tasso Fragoso', '2112209', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tatuí', '3553709', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Taubaté', '3553808', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tavares', '4323203', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tefé', '1304302', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeira', '2516006', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeira de Freitas', '2931350', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeiras', '3170600', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Teixeirópolis', '1101559', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tejupá', '3553857', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tejuçuoca', '2313604', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Telêmaco Borba', '4127702', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Telha', '2807204', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tenente Ananias', '2413706', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tenente Laurentino Cruz', '2413805', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tenente Portela', '4323302', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tocantins', '3168208', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tocantins', '3168208', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tocantinópolis', '1721400', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tocos do Moji', '3171103', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Toledo', '3554301', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Toledo', '4127702', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tomar do Geru', '2807402', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tomazina', '4128502', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tombos', '3171202', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tomé-Açu', '1508302', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tonantins', '1304401', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Toritama', '2614205', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Torixoréu', '5108105', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Toropi', '4323708', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Torre de Pedra', '3554400', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Torres', '4323757', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Torrinha', '3554509', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tracuateua', '1508351', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tracunhaém', '2614304', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trairão', '1508401', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trairi', '2313703', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tramandaí', '4323807', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Travesseiro', '4323856', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tremedal', '2931003', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Arroios', '4323906', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Barras', '4217601', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Barras do Paraná', '4128601', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Cachoeiras', '4323955', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Corações', '3171301', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Coroas', '4324003', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três de Maio', '4324102', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Forquilhas', '4324151', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Fronteiras', '3554608', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Lagoas', '5008309', 7);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Marias', '3171400', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Palmeiras', '4324201', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Passos', '4324300', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Pontas', '3171509', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Ranchos', '5221303', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Três Rios', '3305901', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Treviso', '4217700', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Treze de Maio', '4217809', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Treze Tílias', '4217908', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trindade', '5221402', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trindade', '2614502', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trindade do Sul', '4324359', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Triunfo', '4324409', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Triunfo', '2614601', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Triunfo Potiguar', '2414309', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trizidela do Vale', '2112233', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trombas', '5221501', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Trombudo Central', '4218005', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tronqueiras', '2516303', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tubarão', '4218104', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tucano', '2931806', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tucumã', '1508500', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tucuruí', '1508609', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tufilândia', '2112274', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tuiuti', '3554707', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tumiritinga', '3171608', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tunápolis', '4218203', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tuneiras do Oeste', '4128700', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tuntum', '2112308', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tupaciguara', '3171707', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tupã', '3554806', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tupãssi', '4128809', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tuparetama', '2614700', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tupirama', '1721509', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tupiratins', '1721608', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turiaçu', '2112407', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turilândia', '2112456', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turiúba', '3554905', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turmalina', '3171806', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turmalina', '3554954', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turuçu', '4324508', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turvânia', '5221550', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turvelândia', '5221576', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turvo', '4128858', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turvo', '4218302', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Turvolândia', '3171905', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Tutóia', '2112506', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uarini', '1304500', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uauá', '2931905', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubaitaba', '2932002', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubaí', '3172002', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubajara', '2313752', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubaporanga', '3172101', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubarana', '3555001', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubatã', '2932101', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubatuba', '3555100', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uberaba', '3172309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uberlândia', '3172507', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubirajara', '3555209', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubiratã', '4128908', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ubiratã', '4128908', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uchoa', '3555308', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uiraúna', '2516502', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Ulianópolis', '1508708', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Umari', '2313802', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Umarizal', '2414408', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Umbaúba', '2807501', 19);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Umburanas', '2932200', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Umburatiba', '3172200', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Umbuzeiro', '2516601', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Una', '2932309', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Unaí', '3172309', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uniflor', '4129005', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Union', '2212308', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('União', '4324508', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('União da Serra', '4324557', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('União da Vitória', '4129104', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('União de Minas', '3172333', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('União do Oeste', '4218351', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('União do Sul', '5108303', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('União dos Palmares', '2707709', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urandi', '2932408', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uraí', '4129203', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urubici', '4218401', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uruburetama', '2313901', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urucânia', '3172408', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urucuaca', '2313950', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urucui', '2212407', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urucurituba', '1304500', 16);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uruguaiana', '4324607', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uruoca', '2314008', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urupá', '1101708', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urupema', '4218500', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urupês', '3555407', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Urussanga', '4218609', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uruçuca', '2932507', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Uruçuí', '2212506', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Utinga', '2932606', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vacaria', '4324706', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vale do Anari', '1101757', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vale do Paraíso', '1101807', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vale do Sol', '4324805', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vale Real', '4324854', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vale Verde', '4324904', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Valença', '2932705', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Valença', '3306107', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Valença do Piauí', '2212605', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Valente', '2932804', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vera', '5108501', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vera Cruz', '2933174', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vera Cruz', '2414805', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vera Cruz', '3556603', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vera Cruz do Oeste', '4129500', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Veranópolis', '4325105', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Verdelândia', '3173407', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Verê', '4129609', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Veríssimo', '3173506', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vermelho Novo', '3173605', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vespasiano', '3173704', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vespasiano Correa', '4325204', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viadutos', '4325303', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viamão', '4325402', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viana', '3205307', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viana', '2112704', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vianópolis', '5222005', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vicência', '2615109', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vicente Dutra', '4325501', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vicentinópolis', '5222054', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Bela da Santíssima Trindade', '5105507', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Boa', '5222203', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Flor', '2414904', 23);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Lângaro', '4325709', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Maria', '4325808', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Nova do Piauí', '2212902', 18);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Nova dos Martírios', '2112803', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Pavão', '3205406', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Propício', '5222302', 10);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Rica', '5108600', 9);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Valério', '3205505', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vila Velha', '3205604', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vilhena', '1101757', 14);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vinhedo', '3556702', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viradouro', '3556801', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Virgem da Lapa', '3173803', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Virgínia', '3173902', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Virginópolis', '3174009', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Virgolândia', '3174108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Virgínia', '3173902', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Virginópolis', '3174009', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Virgolândia', '3174108', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Visconde do Rio Branco', '3174207', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viseu', '1508807', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vista Alegre', '4325907', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vista Alegre do Prata', '4326004', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vista Gaúcha', '4326103', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vista Serrana', '2516759', 22);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitor Meireles', '4218708', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória', '3205307', 8);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória Brasil', '3556909', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória da Conquista', '2933307', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória das Missões', '4326202', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória de Santo Antão', '2615208', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória do Jari', '1600609', 15);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória do Mearim', '2112902', 25);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Vitória do Xingu', '1508856', 17);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viçosa', '3174306', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viçosa', '2708709', 20);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Viçosa do Ceará', '2314305', 24);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Volta Grande', '3174504', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Volta Redonda', '3306305', 5);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Votorantim', '3557006', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Votuporanga', '3557105', 4);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Wagner', '2933406', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Wanderlândia', '1721707', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Wanderley', '2933455', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Wenceslau Braz', '4128502', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Wenceslau Braz', '3174702', 6);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Wenceslau Guimarães', '2933505', 12);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Westfália', '4326301', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Witmarsum', '4218856', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xambioá', '1721806', 13);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xambrê', '4128601', 3);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xangri-lá', '4326400', 1);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xapuri', '1200708', 11);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xavantina', '4218906', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xaxim', '4219003', 2);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xexéu', '2615307', 21);
INSERT INTO cadastros.cidades(nome, cod_ibge, id_estado) VALUES ('Xique-Xique', '2933604', 12);