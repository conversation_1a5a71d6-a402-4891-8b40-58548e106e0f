-- Database: zin_desenv_importacoes

-- DROP DATABASE IF EXISTS zin_desenv_importacoes;

CREATE DATABASE zin_desenv_importacoes
    WITH
    OWNER = zin
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    LOCALE_PROVIDER = 'libc'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;
	


--------------------


-- SCHEMA: importacoes

-- DROP SCHEMA IF EXISTS importacoes ;

CREATE SCHEMA IF NOT EXISTS importacoes
    AUTHORIZATION zin;
	
	
-- Table: importacoes.importacoes

-- DROP TABLE IF EXISTS importacoes.importacoes;

CREATE TABLE IF NOT EXISTS importacoes.importacoes
(
    id_importacao serial NOT NULL DEFAULT,
    dados_agregador jsonb,
    status integer,
    data_importacao timestamp with time zone,
    data_processamento timestamp with time zone,
    id_agregador integer,
    id_cliente character varying COLLATE pg_catalog."default",    
    mensagem_erro text,
    CONSTRAINT importacoes_pkey PRIMARY KEY (id_importacao)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS importacoes.importacoes
    OWNER to zin;