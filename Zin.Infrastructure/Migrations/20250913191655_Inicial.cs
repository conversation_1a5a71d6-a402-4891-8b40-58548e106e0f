using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Zin.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Inicial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "zinpag");

            migrationBuilder.EnsureSchema(
                name: "cadastros");

            migrationBuilder.CreateTable(
                name: "ativos",
                schema: "zinpag",
                columns: table => new
                {
                    id_ativo = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    tipo = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ativos", x => x.id_ativo);
                });

            migrationBuilder.CreateTable(
                name: "bancos",
                schema: "cadastros",
                columns: table => new
                {
                    id_banco = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    codigo = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    nome = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_bancos", x => x.id_banco);
                });

            migrationBuilder.CreateTable(
                name: "configuracoes",
                schema: "zinpag",
                columns: table => new
                {
                    id_configuracao = table.Column<Guid>(type: "uuid", nullable: false),
                    nome = table.Column<string>(type: "text", nullable: false),
                    tipo_processamento = table.Column<int>(type: "integer", nullable: false),
                    tipo_configuracao = table.Column<int>(type: "integer", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    criado_em = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    atualizado_em = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    criado_por = table.Column<string>(type: "text", nullable: false),
                    alterado_por = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_configuracoes", x => x.id_configuracao);
                });

            migrationBuilder.CreateTable(
                name: "contatos",
                schema: "cadastros",
                columns: table => new
                {
                    id_contato = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    meio_de_contato = table.Column<int>(type: "integer", nullable: false),
                    nome_responsavel = table.Column<string>(type: "text", nullable: false),
                    valor = table.Column<string>(type: "text", nullable: true),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    observacao = table.Column<string>(type: "text", nullable: true),
                    recebe_notificacao = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_contatos", x => x.id_contato);
                });

            migrationBuilder.CreateTable(
                name: "estados",
                schema: "cadastros",
                columns: table => new
                {
                    id_estado = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    nome = table.Column<string>(type: "text", nullable: false),
                    sigla = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_estados", x => x.id_estado);
                });

            migrationBuilder.CreateTable(
                name: "pessoas",
                schema: "cadastros",
                columns: table => new
                {
                    id_pessoa = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    tipo_pessoa = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_pessoas", x => x.id_pessoa);
                });

            migrationBuilder.CreateTable(
                name: "processamento_dashboard",
                schema: "zinpag",
                columns: table => new
                {
                    id_processamento_dashboard = table.Column<Guid>(type: "uuid", nullable: false),
                    data_registro = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    tipo = table.Column<int>(type: "integer", nullable: true),
                    dados = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_processamento_dashboard", x => x.id_processamento_dashboard);
                });

            migrationBuilder.CreateTable(
                name: "veiculos",
                schema: "zinpag",
                columns: table => new
                {
                    id_ativo = table.Column<int>(type: "integer", nullable: false),
                    placa = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    chassi = table.Column<string>(type: "character varying(30)", maxLength: 30, nullable: true),
                    renavam = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    modelo = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    cor = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ano_fabricacao = table.Column<int>(type: "integer", nullable: true),
                    ano_modelo = table.Column<int>(type: "integer", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_veiculos", x => x.id_ativo);
                    table.ForeignKey(
                        name: "FK_veiculos_ativos_id_ativo",
                        column: x => x.id_ativo,
                        principalSchema: "zinpag",
                        principalTable: "ativos",
                        principalColumn: "id_ativo",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "dados_bancarios",
                schema: "cadastros",
                columns: table => new
                {
                    id_dado_bancario = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_banco = table.Column<int>(type: "integer", nullable: false),
                    agencia = table.Column<string>(type: "text", nullable: true),
                    agencia_dv = table.Column<string>(type: "text", nullable: true),
                    conta = table.Column<string>(type: "text", nullable: false),
                    conta_dv = table.Column<string>(type: "text", nullable: true),
                    tipo_conta = table.Column<int>(type: "integer", nullable: false),
                    titular = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    cpf_cnpj_titular = table.Column<string>(type: "character varying(14)", maxLength: 14, nullable: false),
                    pix = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    principal = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dados_bancarios", x => x.id_dado_bancario);
                    table.ForeignKey(
                        name: "FK_dados_bancarios_bancos_id_banco",
                        column: x => x.id_banco,
                        principalSchema: "cadastros",
                        principalTable: "bancos",
                        principalColumn: "id_banco",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "regras",
                schema: "zinpag",
                columns: table => new
                {
                    id_regra = table.Column<Guid>(type: "uuid", nullable: false),
                    id_configuracao = table.Column<Guid>(type: "uuid", nullable: false),
                    nome = table.Column<string>(type: "text", nullable: false),
                    valor = table.Column<string>(type: "text", nullable: false),
                    tipagem = table.Column<string>(type: "text", nullable: false),
                    operador = table.Column<int>(type: "integer", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_regras", x => x.id_regra);
                    table.ForeignKey(
                        name: "FK_regras_configuracoes_id_configuracao",
                        column: x => x.id_configuracao,
                        principalSchema: "zinpag",
                        principalTable: "configuracoes",
                        principalColumn: "id_configuracao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "cidades",
                schema: "cadastros",
                columns: table => new
                {
                    id_cidade = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    nome = table.Column<string>(type: "text", nullable: false),
                    cod_ibge = table.Column<string>(type: "text", nullable: false),
                    id_estado = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_cidades", x => x.id_cidade);
                    table.ForeignKey(
                        name: "FK_cidades_estados_id_estado",
                        column: x => x.id_estado,
                        principalSchema: "cadastros",
                        principalTable: "estados",
                        principalColumn: "id_estado",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "pessoas_contatos",
                schema: "cadastros",
                columns: table => new
                {
                    id_pessoa = table.Column<int>(type: "integer", nullable: false),
                    id_contato = table.Column<int>(type: "integer", nullable: false),
                    principal = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_pessoas_contatos", x => new { x.id_pessoa, x.id_contato });
                    table.ForeignKey(
                        name: "FK_pessoas_contatos_contatos_id_contato",
                        column: x => x.id_contato,
                        principalSchema: "cadastros",
                        principalTable: "contatos",
                        principalColumn: "id_contato",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_pessoas_contatos_pessoas_id_pessoa",
                        column: x => x.id_pessoa,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "pessoas_fisicas",
                schema: "cadastros",
                columns: table => new
                {
                    id_pessoa = table.Column<int>(type: "integer", nullable: false),
                    nome = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    sobrenome = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    cpf = table.Column<string>(type: "character varying(11)", maxLength: 11, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_pessoas_fisicas", x => x.id_pessoa);
                    table.ForeignKey(
                        name: "FK_pessoas_fisicas_pessoas_id_pessoa",
                        column: x => x.id_pessoa,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "pessoas_juridicas",
                schema: "cadastros",
                columns: table => new
                {
                    id_pessoa = table.Column<int>(type: "integer", nullable: false),
                    razao_social = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    nome_fantasia = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    cnpj = table.Column<string>(type: "character varying(14)", maxLength: 14, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_pessoas_juridicas", x => x.id_pessoa);
                    table.ForeignKey(
                        name: "FK_pessoas_juridicas_pessoas_id_pessoa",
                        column: x => x.id_pessoa,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "pessoas_dados_bancarios",
                schema: "cadastros",
                columns: table => new
                {
                    id_pessoa = table.Column<int>(type: "integer", nullable: false),
                    id_dado_bancario = table.Column<int>(type: "integer", nullable: false),
                    principal = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_pessoas_dados_bancarios", x => new { x.id_pessoa, x.id_dado_bancario });
                    table.ForeignKey(
                        name: "FK_pessoas_dados_bancarios_dados_bancarios_id_dado_bancario",
                        column: x => x.id_dado_bancario,
                        principalSchema: "cadastros",
                        principalTable: "dados_bancarios",
                        principalColumn: "id_dado_bancario",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_pessoas_dados_bancarios_pessoas_id_pessoa",
                        column: x => x.id_pessoa,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "enderecos",
                schema: "cadastros",
                columns: table => new
                {
                    id_endereco = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    bairro = table.Column<string>(type: "text", nullable: false),
                    id_cidade = table.Column<int>(type: "integer", nullable: false),
                    id_estado = table.Column<int>(type: "integer", nullable: false),
                    logradouro = table.Column<string>(type: "text", nullable: false),
                    numero = table.Column<string>(type: "text", nullable: false),
                    complemento = table.Column<string>(type: "text", nullable: true),
                    cep = table.Column<string>(type: "text", nullable: false),
                    tipo_endereco = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_enderecos", x => x.id_endereco);
                    table.ForeignKey(
                        name: "FK_enderecos_cidades_id_cidade",
                        column: x => x.id_cidade,
                        principalSchema: "cadastros",
                        principalTable: "cidades",
                        principalColumn: "id_cidade",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_enderecos_estados_id_estado",
                        column: x => x.id_estado,
                        principalSchema: "cadastros",
                        principalTable: "estados",
                        principalColumn: "id_estado",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "cliente_empresas",
                schema: "cadastros",
                columns: table => new
                {
                    id_pessoa = table.Column<int>(type: "integer", nullable: false),
                    matriz = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_cliente_empresas", x => x.id_pessoa);
                    table.ForeignKey(
                        name: "FK_cliente_empresas_pessoas_juridicas_id_pessoa",
                        column: x => x.id_pessoa,
                        principalSchema: "cadastros",
                        principalTable: "pessoas_juridicas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "veiculo_oficina",
                schema: "zinpag",
                columns: table => new
                {
                    id_veiculo_oficina = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_veiculo = table.Column<int>(type: "integer", nullable: false),
                    id_pessoa_oficina = table.Column<int>(type: "integer", nullable: false),
                    data_entrada = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    data_saida = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    veiculo_presente = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_veiculo_oficina", x => x.id_veiculo_oficina);
                    table.ForeignKey(
                        name: "FK_veiculo_oficina_pessoas_juridicas_id_pessoa_oficina",
                        column: x => x.id_pessoa_oficina,
                        principalSchema: "cadastros",
                        principalTable: "pessoas_juridicas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_veiculo_oficina_veiculos_id_veiculo",
                        column: x => x.id_veiculo,
                        principalSchema: "zinpag",
                        principalTable: "veiculos",
                        principalColumn: "id_ativo",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "pessoas_enderecos",
                schema: "cadastros",
                columns: table => new
                {
                    id_pessoa = table.Column<int>(type: "integer", nullable: false),
                    id_endereco = table.Column<int>(type: "integer", nullable: false),
                    principal = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_pessoas_enderecos", x => new { x.id_pessoa, x.id_endereco });
                    table.ForeignKey(
                        name: "FK_pessoas_enderecos_enderecos_id_endereco",
                        column: x => x.id_endereco,
                        principalSchema: "cadastros",
                        principalTable: "enderecos",
                        principalColumn: "id_endereco",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_pessoas_enderecos_pessoas_id_pessoa",
                        column: x => x.id_pessoa,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "agregadores",
                schema: "zinpag",
                columns: table => new
                {
                    id_agregador = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_cliente_empresa = table.Column<int>(type: "integer", nullable: false),
                    tipo_agregador = table.Column<int>(type: "integer", nullable: false),
                    numero = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    status_processamento = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_agregadores", x => x.id_agregador);
                    table.ForeignKey(
                        name: "FK_agregadores_cliente_empresas_id_cliente_empresa",
                        column: x => x.id_cliente_empresa,
                        principalSchema: "cadastros",
                        principalTable: "cliente_empresas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "agregadores_ativos",
                schema: "zinpag",
                columns: table => new
                {
                    id_agregador = table.Column<int>(type: "integer", nullable: false),
                    id_ativo = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_agregadores_ativos", x => new { x.id_agregador, x.id_ativo });
                    table.ForeignKey(
                        name: "FK_agregadores_ativos_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_agregadores_ativos_ativos_id_ativo",
                        column: x => x.id_ativo,
                        principalSchema: "zinpag",
                        principalTable: "ativos",
                        principalColumn: "id_ativo",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "itens",
                schema: "zinpag",
                columns: table => new
                {
                    id_item = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_agregador = table.Column<int>(type: "integer", nullable: false),
                    codigo = table.Column<string>(type: "text", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: false),
                    quantidade = table.Column<int>(type: "integer", nullable: false),
                    valor_unitario = table.Column<decimal>(type: "numeric", nullable: false),
                    valor_total = table.Column<decimal>(type: "numeric", nullable: false),
                    status_processamento = table.Column<int>(type: "integer", nullable: false),
                    status_processamento_item_duplicado = table.Column<int>(type: "integer", nullable: false),
                    status_processamento_pagamento_duplicado = table.Column<int>(type: "integer", nullable: false),
                    status_processamento_pagamento = table.Column<int>(type: "integer", nullable: false),
                    status_processamento_ressarcimento = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_itens", x => x.id_item);
                    table.ForeignKey(
                        name: "FK_itens_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "movimentacoes",
                schema: "zinpag",
                columns: table => new
                {
                    id_movimentacao = table.Column<Guid>(type: "uuid", nullable: false),
                    id_agregador = table.Column<int>(type: "integer", nullable: false),
                    data_hora_autorizacao_item = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    valor_total_a_pagar = table.Column<decimal>(type: "numeric", nullable: true),
                    valor_total_pago = table.Column<decimal>(type: "numeric", nullable: true),
                    valor_total_a_ressarcir = table.Column<decimal>(type: "numeric", nullable: true),
                    valor_total_ressarcido = table.Column<decimal>(type: "numeric", nullable: true),
                    tipo_movimentacao = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_movimentacoes", x => x.id_movimentacao);
                    table.ForeignKey(
                        name: "FK_movimentacoes_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "pagamentos",
                schema: "zinpag",
                columns: table => new
                {
                    id_pagamento = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_pessoa = table.Column<int>(type: "integer", nullable: false),
                    id_agregador = table.Column<int>(type: "integer", nullable: true),
                    status_pagamento = table.Column<int>(type: "integer", maxLength: 20, nullable: false),
                    data_criacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    data_atualizacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    cancelado = table.Column<bool>(type: "boolean", nullable: false),
                    data_previsao = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    valor = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    forma_pagamento = table.Column<int>(type: "integer", maxLength: 50, nullable: false),
                    descricao = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_pagamentos", x => x.id_pagamento);
                    table.ForeignKey(
                        name: "FK_pagamentos_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador");
                    table.ForeignKey(
                        name: "FK_pagamentos_pessoas_id_pessoa",
                        column: x => x.id_pessoa,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "processamento_agregador_status",
                schema: "zinpag",
                columns: table => new
                {
                    id_processamento_agregador_status = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_agregador = table.Column<int>(type: "integer", nullable: false),
                    tipo = table.Column<int>(type: "integer", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    data_atualizacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    detalhe = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_processamento_agregador_status", x => x.id_processamento_agregador_status);
                    table.ForeignKey(
                        name: "FK_processamento_agregador_status_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "fila_processamento",
                schema: "zinpag",
                columns: table => new
                {
                    id_fila_processamento = table.Column<Guid>(type: "uuid", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: true),
                    data_registro = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    id_agregador = table.Column<int>(type: "integer", nullable: false),
                    id_item = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_fila_processamento", x => x.id_fila_processamento);
                    table.ForeignKey(
                        name: "FK_fila_processamento_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_fila_processamento_itens_id_item",
                        column: x => x.id_item,
                        principalSchema: "zinpag",
                        principalTable: "itens",
                        principalColumn: "id_item",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "fila_processamento_historico",
                schema: "zinpag",
                columns: table => new
                {
                    id_fila_processamento_historico = table.Column<Guid>(type: "uuid", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: true),
                    data_registro = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    id_agregador = table.Column<int>(type: "integer", nullable: true),
                    id_item = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_fila_processamento_historico", x => x.id_fila_processamento_historico);
                    table.ForeignKey(
                        name: "FK_fila_processamento_historico_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador");
                    table.ForeignKey(
                        name: "FK_fila_processamento_historico_itens_id_item",
                        column: x => x.id_item,
                        principalSchema: "zinpag",
                        principalTable: "itens",
                        principalColumn: "id_item");
                });

            migrationBuilder.CreateTable(
                name: "documentos",
                schema: "zinpag",
                columns: table => new
                {
                    id_documento = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_agregador = table.Column<int>(type: "integer", nullable: false),
                    id_pessoa_emitente = table.Column<int>(type: "integer", nullable: false),
                    id_pessoa_destinatario = table.Column<int>(type: "integer", nullable: false),
                    tipo_documento = table.Column<int>(type: "integer", nullable: false),
                    numero = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    data_hora_emissao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    serie = table.Column<string>(type: "text", nullable: false),
                    total = table.Column<decimal>(type: "numeric", nullable: false),
                    caminho_arquivo = table.Column<string>(type: "text", nullable: true),
                    status_documento = table.Column<int>(type: "integer", nullable: false),
                    id_movimentacao = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_documentos", x => x.id_documento);
                    table.ForeignKey(
                        name: "FK_documentos_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_documentos_movimentacoes_id_movimentacao",
                        column: x => x.id_movimentacao,
                        principalSchema: "zinpag",
                        principalTable: "movimentacoes",
                        principalColumn: "id_movimentacao");
                    table.ForeignKey(
                        name: "FK_documentos_pessoas_id_pessoa_destinatario",
                        column: x => x.id_pessoa_destinatario,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_documentos_pessoas_id_pessoa_emitente",
                        column: x => x.id_pessoa_emitente,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "liquidacoes_pagamentos",
                schema: "zinpag",
                columns: table => new
                {
                    id_liquidacao_pagamento = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_pagamento = table.Column<int>(type: "integer", nullable: false),
                    status_pagamento = table.Column<int>(type: "integer", nullable: false),
                    data = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    valor_pago = table.Column<decimal>(type: "numeric(10,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_liquidacoes_pagamentos", x => x.id_liquidacao_pagamento);
                    table.ForeignKey(
                        name: "FK_liquidacoes_pagamentos_pagamentos_id_pagamento",
                        column: x => x.id_pagamento,
                        principalSchema: "zinpag",
                        principalTable: "pagamentos",
                        principalColumn: "id_pagamento",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "documentos_pagamento",
                schema: "zinpag",
                columns: table => new
                {
                    id_documento_pagamento = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_documento = table.Column<int>(type: "integer", nullable: false),
                    id_pagamento = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_documentos_pagamento", x => x.id_documento_pagamento);
                    table.ForeignKey(
                        name: "FK_documentos_pagamento_documentos_id_documento",
                        column: x => x.id_documento,
                        principalSchema: "zinpag",
                        principalTable: "documentos",
                        principalColumn: "id_documento",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_documentos_pagamento_pagamentos_id_pagamento",
                        column: x => x.id_pagamento,
                        principalSchema: "zinpag",
                        principalTable: "pagamentos",
                        principalColumn: "id_pagamento",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "itens_versoes",
                schema: "zinpag",
                columns: table => new
                {
                    id_item_versao = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_item = table.Column<int>(type: "integer", nullable: false),
                    id_item_versao_anterior = table.Column<int>(type: "integer", nullable: true),
                    id_documento = table.Column<int>(type: "integer", nullable: true),
                    id_pessoa_fornecedora = table.Column<int>(type: "integer", nullable: false),
                    id_ativo = table.Column<int>(type: "integer", nullable: false),
                    id_oficina = table.Column<int>(type: "integer", nullable: false),
                    numero_versao = table.Column<int>(type: "integer", nullable: false),
                    valor_unitario = table.Column<decimal>(type: "numeric", nullable: false),
                    quantidade = table.Column<int>(type: "integer", nullable: false),
                    valor_total = table.Column<decimal>(type: "numeric", nullable: false),
                    data_hora_criacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    data_hora_entrega = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    data_hora_autorizacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    data_hora_movimento = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    tipo_movimento_item = table.Column<int>(type: "integer", nullable: false),
                    status_processamento_item_duplicado = table.Column<int>(type: "integer", nullable: false),
                    status_processamento_pagamento_duplicado = table.Column<int>(type: "integer", nullable: false),
                    status_processamento_pagamento = table.Column<int>(type: "integer", nullable: false),
                    status_processamento_ressarcimento = table.Column<int>(type: "integer", nullable: false),
                    tipo_item_versao = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_itens_versoes", x => x.id_item_versao);
                    table.ForeignKey(
                        name: "FK_itens_versoes_ativos_id_ativo",
                        column: x => x.id_ativo,
                        principalSchema: "zinpag",
                        principalTable: "ativos",
                        principalColumn: "id_ativo",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_itens_versoes_documentos_id_documento",
                        column: x => x.id_documento,
                        principalSchema: "zinpag",
                        principalTable: "documentos",
                        principalColumn: "id_documento");
                    table.ForeignKey(
                        name: "FK_itens_versoes_itens_id_item",
                        column: x => x.id_item,
                        principalSchema: "zinpag",
                        principalTable: "itens",
                        principalColumn: "id_item",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_itens_versoes_itens_versoes_id_item_versao_anterior",
                        column: x => x.id_item_versao_anterior,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao");
                    table.ForeignKey(
                        name: "FK_itens_versoes_pessoas_id_oficina",
                        column: x => x.id_oficina,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_itens_versoes_pessoas_id_pessoa_fornecedora",
                        column: x => x.id_pessoa_fornecedora,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "notas_fiscais",
                schema: "zinpag",
                columns: table => new
                {
                    id_documento = table.Column<int>(type: "integer", nullable: false),
                    icms = table.Column<decimal>(type: "numeric", nullable: true),
                    ipi = table.Column<decimal>(type: "numeric", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_notas_fiscais", x => x.id_documento);
                    table.ForeignKey(
                        name: "FK_notas_fiscais_documentos_id_documento",
                        column: x => x.id_documento,
                        principalSchema: "zinpag",
                        principalTable: "documentos",
                        principalColumn: "id_documento",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ressarcimentos",
                schema: "zinpag",
                columns: table => new
                {
                    id_ressarcimento = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_cliente_empresa = table.Column<int>(type: "integer", nullable: false),
                    id_fornecedor = table.Column<int>(type: "integer", nullable: false),
                    id_agregador = table.Column<int>(type: "integer", nullable: false),
                    id_ativo = table.Column<int>(type: "integer", nullable: false),
                    id_agregador_ativo = table.Column<int>(type: "integer", nullable: true),
                    id_movimentacao = table.Column<Guid>(type: "uuid", nullable: true),
                    id_documento = table.Column<int>(type: "integer", nullable: true),
                    numero_ressarcimento = table.Column<string>(type: "text", nullable: false),
                    valor = table.Column<decimal>(type: "numeric(12,2)", nullable: false),
                    data_solicitacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    data_pagamento = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    forma_pagamento_ressarcimento = table.Column<int>(type: "integer", nullable: false),
                    status_ressarcimento = table.Column<int>(type: "integer", nullable: false),
                    data_cancelamento = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    cancelado = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ressarcimentos", x => x.id_ressarcimento);
                    table.ForeignKey(
                        name: "FK_ressarcimentos_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ressarcimentos_agregadores_id_agregador_ativo",
                        column: x => x.id_agregador_ativo,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador");
                    table.ForeignKey(
                        name: "FK_ressarcimentos_ativos_id_ativo",
                        column: x => x.id_ativo,
                        principalSchema: "zinpag",
                        principalTable: "ativos",
                        principalColumn: "id_ativo",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ressarcimentos_documentos_id_documento",
                        column: x => x.id_documento,
                        principalSchema: "zinpag",
                        principalTable: "documentos",
                        principalColumn: "id_documento");
                    table.ForeignKey(
                        name: "FK_ressarcimentos_movimentacoes_id_movimentacao",
                        column: x => x.id_movimentacao,
                        principalSchema: "zinpag",
                        principalTable: "movimentacoes",
                        principalColumn: "id_movimentacao");
                    table.ForeignKey(
                        name: "FK_ressarcimentos_pessoas_id_cliente_empresa",
                        column: x => x.id_cliente_empresa,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ressarcimentos_pessoas_id_fornecedor",
                        column: x => x.id_fornecedor,
                        principalSchema: "cadastros",
                        principalTable: "pessoas",
                        principalColumn: "id_pessoa",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "documento_item_versao",
                schema: "zinpag",
                columns: table => new
                {
                    id_documento_item_versao = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_documento = table.Column<int>(type: "integer", nullable: false),
                    id_item_versao = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_documento_item_versao", x => x.id_documento_item_versao);
                    table.ForeignKey(
                        name: "FK_documento_item_versao_documentos_id_documento",
                        column: x => x.id_documento,
                        principalSchema: "zinpag",
                        principalTable: "documentos",
                        principalColumn: "id_documento",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_documento_item_versao_itens_versoes_id_item_versao",
                        column: x => x.id_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "movimentacoes_itens_versoes",
                schema: "zinpag",
                columns: table => new
                {
                    id_movimentacao_item_versao = table.Column<Guid>(type: "uuid", nullable: false),
                    id_movimentacao = table.Column<Guid>(type: "uuid", nullable: false),
                    id_item_versao = table.Column<int>(type: "integer", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    data_vinculo = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    data_desvinculo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ItemVersaoId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_movimentacoes_itens_versoes", x => x.id_movimentacao_item_versao);
                    table.ForeignKey(
                        name: "FK_movimentacoes_itens_versoes_itens_versoes_ItemVersaoId",
                        column: x => x.ItemVersaoId,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao");
                    table.ForeignKey(
                        name: "FK_movimentacoes_itens_versoes_movimentacoes_id_movimentacao",
                        column: x => x.id_movimentacao,
                        principalSchema: "zinpag",
                        principalTable: "movimentacoes",
                        principalColumn: "id_movimentacao",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_movitemversao_itemversao",
                        column: x => x.id_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "pagamento_itens_versoes",
                schema: "zinpag",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_pagamento = table.Column<int>(type: "integer", nullable: false),
                    id_item_versao = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_pagamento_itens_versoes", x => x.id);
                    table.ForeignKey(
                        name: "FK_pagamento_itens_versoes_itens_versoes_id_item_versao",
                        column: x => x.id_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_pagamento_itens_versoes_pagamentos_id_pagamento",
                        column: x => x.id_pagamento,
                        principalSchema: "zinpag",
                        principalTable: "pagamentos",
                        principalColumn: "id_pagamento",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "registro_processamento_item_versao",
                schema: "zinpag",
                columns: table => new
                {
                    id_registro_processamento_item_versao = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_item_versao = table.Column<int>(type: "integer", nullable: false),
                    detalhe = table.Column<string>(type: "text", nullable: true),
                    tipo = table.Column<int>(type: "integer", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    data_atualizacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_registro_processamento_item_versao", x => x.id_registro_processamento_item_versao);
                    table.ForeignKey(
                        name: "FK_registro_processamento_item_versao_itens_versoes_id_item_ve~",
                        column: x => x.id_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "documentos_ressarcimento",
                schema: "zinpag",
                columns: table => new
                {
                    id_documento_ressarcimento = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_documento = table.Column<int>(type: "integer", nullable: false),
                    id_ressarcimento = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_documentos_ressarcimento", x => x.id_documento_ressarcimento);
                    table.ForeignKey(
                        name: "FK_documentos_ressarcimento_documentos_id_documento",
                        column: x => x.id_documento,
                        principalSchema: "zinpag",
                        principalTable: "documentos",
                        principalColumn: "id_documento",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_documentos_ressarcimento_ressarcimentos_id_ressarcimento",
                        column: x => x.id_ressarcimento,
                        principalSchema: "zinpag",
                        principalTable: "ressarcimentos",
                        principalColumn: "id_ressarcimento",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "liquidacoes_ressarcimentos",
                schema: "zinpag",
                columns: table => new
                {
                    id_liquidacao_ressarcimento = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_ressarcimento = table.Column<int>(type: "integer", nullable: false),
                    data_ressarcimento = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    valor_ressarcido = table.Column<decimal>(type: "numeric(10,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_liquidacoes_ressarcimentos", x => x.id_liquidacao_ressarcimento);
                    table.ForeignKey(
                        name: "FK_liquidacoes_ressarcimentos_ressarcimentos_id_ressarcimento",
                        column: x => x.id_ressarcimento,
                        principalSchema: "zinpag",
                        principalTable: "ressarcimentos",
                        principalColumn: "id_ressarcimento",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ressarcimentos_itens_versao",
                schema: "zinpag",
                columns: table => new
                {
                    id_ressarcimento_item_versao = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_ressarcimento = table.Column<int>(type: "integer", nullable: false),
                    id_item_versao = table.Column<int>(type: "integer", nullable: false),
                    data_autorizacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ressarcimentos_itens_versao", x => x.id_ressarcimento_item_versao);
                    table.ForeignKey(
                        name: "FK_ressarcimentos_itens_versao_itens_versoes_id_item_versao",
                        column: x => x.id_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ressarcimentos_itens_versao_ressarcimentos_id_ressarcimento",
                        column: x => x.id_ressarcimento,
                        principalSchema: "zinpag",
                        principalTable: "ressarcimentos",
                        principalColumn: "id_ressarcimento",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "condicoes",
                schema: "zinpag",
                columns: table => new
                {
                    id_condicao = table.Column<Guid>(type: "uuid", nullable: false),
                    detalhe = table.Column<string>(type: "text", nullable: true),
                    apto = table.Column<bool>(type: "boolean", nullable: true),
                    tipoconfiguracao = table.Column<int>(type: "integer", nullable: true),
                    regra = table.Column<string>(type: "text", nullable: true),
                    data_registro = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    decisao = table.Column<int>(type: "integer", nullable: true),
                    data_decisao = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    usuario_decisao = table.Column<string>(type: "text", nullable: true),
                    id_registro_processamento_item_versao = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_condicoes", x => x.id_condicao);
                    table.ForeignKey(
                        name: "FK_condicoes_registro_processamento_item_versao_id_registro_pr~",
                        column: x => x.id_registro_processamento_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "registro_processamento_item_versao",
                        principalColumn: "id_registro_processamento_item_versao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "divergencias",
                schema: "zinpag",
                columns: table => new
                {
                    id_divergencia = table.Column<Guid>(type: "uuid", nullable: false),
                    tipo_divergencia = table.Column<int>(type: "integer", nullable: false),
                    detalhe = table.Column<string>(type: "text", nullable: true),
                    data_registro = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    decisao = table.Column<int>(type: "integer", nullable: true),
                    data_decisao = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    usuario_decisao = table.Column<string>(type: "text", nullable: true),
                    id_registro_processamento_item_versao = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_divergencias", x => x.id_divergencia);
                    table.ForeignKey(
                        name: "FK_divergencias_registro_processamento_item_versao_id_registro~",
                        column: x => x.id_registro_processamento_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "registro_processamento_item_versao",
                        principalColumn: "id_registro_processamento_item_versao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "item_versao_condicao",
                schema: "zinpag",
                columns: table => new
                {
                    id_item_versao_condicao = table.Column<Guid>(type: "uuid", nullable: false),
                    id_item_versao = table.Column<int>(type: "integer", nullable: false),
                    id_condicao = table.Column<Guid>(type: "uuid", nullable: false),
                    data_registro = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_item_versao_condicao", x => x.id_item_versao_condicao);
                    table.ForeignKey(
                        name: "FK_item_versao_condicao_condicoes_id_condicao",
                        column: x => x.id_condicao,
                        principalSchema: "zinpag",
                        principalTable: "condicoes",
                        principalColumn: "id_condicao",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_item_versao_condicao_itens_versoes_id_item_versao",
                        column: x => x.id_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "item_versao_divergencia",
                schema: "zinpag",
                columns: table => new
                {
                    id_item_versao_divergencia = table.Column<Guid>(type: "uuid", nullable: false),
                    id_item_versao = table.Column<int>(type: "integer", nullable: false),
                    id_divergencia = table.Column<Guid>(type: "uuid", nullable: false),
                    data_registro = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_item_versao_divergencia", x => x.id_item_versao_divergencia);
                    table.ForeignKey(
                        name: "FK_item_versao_divergencia_divergencias_id_divergencia",
                        column: x => x.id_divergencia,
                        principalSchema: "zinpag",
                        principalTable: "divergencias",
                        principalColumn: "id_divergencia",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_item_versao_divergencia_itens_versoes_id_item_versao",
                        column: x => x.id_item_versao,
                        principalSchema: "zinpag",
                        principalTable: "itens_versoes",
                        principalColumn: "id_item_versao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_agregadores_id_cliente_empresa",
                schema: "zinpag",
                table: "agregadores",
                column: "id_cliente_empresa");

            migrationBuilder.CreateIndex(
                name: "IX_agregadores_ativos_id_ativo",
                schema: "zinpag",
                table: "agregadores_ativos",
                column: "id_ativo");

            migrationBuilder.CreateIndex(
                name: "IX_cidades_id_estado",
                schema: "cadastros",
                table: "cidades",
                column: "id_estado");

            migrationBuilder.CreateIndex(
                name: "IX_condicoes_id_registro_processamento_item_versao",
                schema: "zinpag",
                table: "condicoes",
                column: "id_registro_processamento_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_dados_bancarios_id_banco",
                schema: "cadastros",
                table: "dados_bancarios",
                column: "id_banco");

            migrationBuilder.CreateIndex(
                name: "IX_divergencias_id_registro_processamento_item_versao",
                schema: "zinpag",
                table: "divergencias",
                column: "id_registro_processamento_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_documento_item_versao_id_documento",
                schema: "zinpag",
                table: "documento_item_versao",
                column: "id_documento");

            migrationBuilder.CreateIndex(
                name: "IX_documento_item_versao_id_item_versao",
                schema: "zinpag",
                table: "documento_item_versao",
                column: "id_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_documentos_id_agregador",
                schema: "zinpag",
                table: "documentos",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_documentos_id_movimentacao",
                schema: "zinpag",
                table: "documentos",
                column: "id_movimentacao");

            migrationBuilder.CreateIndex(
                name: "IX_documentos_id_pessoa_destinatario",
                schema: "zinpag",
                table: "documentos",
                column: "id_pessoa_destinatario");

            migrationBuilder.CreateIndex(
                name: "IX_documentos_id_pessoa_emitente",
                schema: "zinpag",
                table: "documentos",
                column: "id_pessoa_emitente");

            migrationBuilder.CreateIndex(
                name: "IX_documentos_pagamento_id_documento",
                schema: "zinpag",
                table: "documentos_pagamento",
                column: "id_documento",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_documentos_pagamento_id_pagamento",
                schema: "zinpag",
                table: "documentos_pagamento",
                column: "id_pagamento");

            migrationBuilder.CreateIndex(
                name: "IX_documentos_ressarcimento_id_documento",
                schema: "zinpag",
                table: "documentos_ressarcimento",
                column: "id_documento");

            migrationBuilder.CreateIndex(
                name: "IX_documentos_ressarcimento_id_ressarcimento",
                schema: "zinpag",
                table: "documentos_ressarcimento",
                column: "id_ressarcimento");

            migrationBuilder.CreateIndex(
                name: "IX_enderecos_id_cidade",
                schema: "cadastros",
                table: "enderecos",
                column: "id_cidade");

            migrationBuilder.CreateIndex(
                name: "IX_enderecos_id_estado",
                schema: "cadastros",
                table: "enderecos",
                column: "id_estado");

            migrationBuilder.CreateIndex(
                name: "IX_fila_processamento_id_agregador",
                schema: "zinpag",
                table: "fila_processamento",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_fila_processamento_id_item",
                schema: "zinpag",
                table: "fila_processamento",
                column: "id_item");

            migrationBuilder.CreateIndex(
                name: "IX_fila_processamento_historico_id_agregador",
                schema: "zinpag",
                table: "fila_processamento_historico",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_fila_processamento_historico_id_item",
                schema: "zinpag",
                table: "fila_processamento_historico",
                column: "id_item");

            migrationBuilder.CreateIndex(
                name: "IX_item_versao_condicao_id_condicao",
                schema: "zinpag",
                table: "item_versao_condicao",
                column: "id_condicao");

            migrationBuilder.CreateIndex(
                name: "IX_item_versao_condicao_id_item_versao",
                schema: "zinpag",
                table: "item_versao_condicao",
                column: "id_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_item_versao_divergencia_id_divergencia",
                schema: "zinpag",
                table: "item_versao_divergencia",
                column: "id_divergencia");

            migrationBuilder.CreateIndex(
                name: "IX_item_versao_divergencia_id_item_versao",
                schema: "zinpag",
                table: "item_versao_divergencia",
                column: "id_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_itens_id_agregador",
                schema: "zinpag",
                table: "itens",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_ativo",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_ativo");

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_documento",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_documento");

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_item",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_item");

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_item_versao_anterior",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_item_versao_anterior");

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_oficina",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_oficina");

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_pessoa_fornecedora",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_pessoa_fornecedora");

            migrationBuilder.CreateIndex(
                name: "IX_liquidacoes_pagamentos_id_pagamento",
                schema: "zinpag",
                table: "liquidacoes_pagamentos",
                column: "id_pagamento");

            migrationBuilder.CreateIndex(
                name: "IX_liquidacoes_ressarcimentos_id_ressarcimento",
                schema: "zinpag",
                table: "liquidacoes_ressarcimentos",
                column: "id_ressarcimento");

            migrationBuilder.CreateIndex(
                name: "IX_movimentacoes_id_agregador",
                schema: "zinpag",
                table: "movimentacoes",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_movimentacoes_itens_versoes_id_item_versao",
                schema: "zinpag",
                table: "movimentacoes_itens_versoes",
                column: "id_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_movimentacoes_itens_versoes_id_movimentacao",
                schema: "zinpag",
                table: "movimentacoes_itens_versoes",
                column: "id_movimentacao");

            migrationBuilder.CreateIndex(
                name: "IX_movimentacoes_itens_versoes_ItemVersaoId",
                schema: "zinpag",
                table: "movimentacoes_itens_versoes",
                column: "ItemVersaoId");

            migrationBuilder.CreateIndex(
                name: "IX_pagamento_itens_versoes_id_item_versao",
                schema: "zinpag",
                table: "pagamento_itens_versoes",
                column: "id_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_pagamento_itens_versoes_id_pagamento",
                schema: "zinpag",
                table: "pagamento_itens_versoes",
                column: "id_pagamento");

            migrationBuilder.CreateIndex(
                name: "IX_pagamentos_id_agregador",
                schema: "zinpag",
                table: "pagamentos",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_pagamentos_id_pessoa",
                schema: "zinpag",
                table: "pagamentos",
                column: "id_pessoa");

            migrationBuilder.CreateIndex(
                name: "IX_pessoas_contatos_id_contato",
                schema: "cadastros",
                table: "pessoas_contatos",
                column: "id_contato");

            migrationBuilder.CreateIndex(
                name: "IX_pessoas_dados_bancarios_id_dado_bancario",
                schema: "cadastros",
                table: "pessoas_dados_bancarios",
                column: "id_dado_bancario");

            migrationBuilder.CreateIndex(
                name: "IX_pessoas_enderecos_id_endereco",
                schema: "cadastros",
                table: "pessoas_enderecos",
                column: "id_endereco");

            migrationBuilder.CreateIndex(
                name: "IX_processamento_agregador_status_id_agregador",
                schema: "zinpag",
                table: "processamento_agregador_status",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_registro_processamento_item_versao_id_item_versao",
                schema: "zinpag",
                table: "registro_processamento_item_versao",
                column: "id_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_regras_id_configuracao",
                schema: "zinpag",
                table: "regras",
                column: "id_configuracao");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_id_agregador",
                schema: "zinpag",
                table: "ressarcimentos",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_id_agregador_ativo",
                schema: "zinpag",
                table: "ressarcimentos",
                column: "id_agregador_ativo");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_id_ativo",
                schema: "zinpag",
                table: "ressarcimentos",
                column: "id_ativo");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_id_cliente_empresa",
                schema: "zinpag",
                table: "ressarcimentos",
                column: "id_cliente_empresa");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_id_documento",
                schema: "zinpag",
                table: "ressarcimentos",
                column: "id_documento");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_id_fornecedor",
                schema: "zinpag",
                table: "ressarcimentos",
                column: "id_fornecedor");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_id_movimentacao",
                schema: "zinpag",
                table: "ressarcimentos",
                column: "id_movimentacao");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_itens_versao_id_item_versao",
                schema: "zinpag",
                table: "ressarcimentos_itens_versao",
                column: "id_item_versao");

            migrationBuilder.CreateIndex(
                name: "IX_ressarcimentos_itens_versao_id_ressarcimento",
                schema: "zinpag",
                table: "ressarcimentos_itens_versao",
                column: "id_ressarcimento");

            migrationBuilder.CreateIndex(
                name: "IX_veiculo_oficina_id_pessoa_oficina",
                schema: "zinpag",
                table: "veiculo_oficina",
                column: "id_pessoa_oficina");

            migrationBuilder.CreateIndex(
                name: "IX_veiculo_oficina_id_veiculo",
                schema: "zinpag",
                table: "veiculo_oficina",
                column: "id_veiculo");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "agregadores_ativos",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "documento_item_versao",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "documentos_pagamento",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "documentos_ressarcimento",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "fila_processamento",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "fila_processamento_historico",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "item_versao_condicao",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "item_versao_divergencia",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "liquidacoes_pagamentos",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "liquidacoes_ressarcimentos",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "movimentacoes_itens_versoes",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "notas_fiscais",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "pagamento_itens_versoes",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "pessoas_contatos",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "pessoas_dados_bancarios",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "pessoas_enderecos",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "pessoas_fisicas",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "processamento_agregador_status",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "processamento_dashboard",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "regras",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "ressarcimentos_itens_versao",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "veiculo_oficina",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "condicoes",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "divergencias",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "pagamentos",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "contatos",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "dados_bancarios",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "enderecos",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "configuracoes",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "ressarcimentos",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "veiculos",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "registro_processamento_item_versao",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "bancos",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "cidades",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "itens_versoes",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "estados",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "ativos",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "documentos",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "itens",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "movimentacoes",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "agregadores",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "cliente_empresas",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "pessoas_juridicas",
                schema: "cadastros");

            migrationBuilder.DropTable(
                name: "pessoas",
                schema: "cadastros");
        }
    }
}
