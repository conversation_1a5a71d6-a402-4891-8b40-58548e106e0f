using Microsoft.Extensions.Logging;
using Zin.Application.Helpers;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Processos
{
    public class PossivelItemDuplicadoService : IPossivelItemDuplicadoService
    {
        private readonly IItemRepository _itemRepositorio;
        private readonly IItemVersaoRepository _itemVersaoRepositorio;
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepositorio;
        private readonly ILogger<PossivelItemDuplicadoService> _logger;

        public PossivelItemDuplicadoService(
            IItemRepository itemRepositorio,
            IItemVersaoRepository itemVersaoRepositorio,
            IRegistroProcessamentoRepository registroProcessamentoRepositorio,
            ILogger<PossivelItemDuplicadoService> logger)
        {
            _itemRepositorio = itemRepositorio;
            _itemVersaoRepositorio = itemVersaoRepositorio;
            _registroProcessamentoRepositorio = registroProcessamentoRepositorio;
            _logger = logger;
        }

        public async Task ProcessarAsync(int idAgregador, int idItem)
        {
            var item = await _itemRepositorio.BuscarPorIdAsync(idItem);
            if (item != null)
            {
                var versoes = await _itemVersaoRepositorio.BuscarPorItemIdAsync(item.Id);

                var autorizados = versoes.Where(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao).ToList();
                var temCancelado = versoes.Any(v => v.TipoMovimento == TipoMovimentoItem.Exclusao);

                foreach (var itemversao in versoes)
                {
                    try
                    {
                        if (autorizados.Count > 1 && !temCancelado)
                        {
                            var divergencias = new List<Divergencia>();
                            var divergencia = new Divergencia
                            {
                                TipoDivergencia = TipoDivergencia.PossivelItemDuplicado,
                                Detalhe = "Item com múltiplas autorizações sem cancelamento.",
                                DataRegistro = DateTime.UtcNow,
                                Decisao = null,
                                DataDecisao = null,
                                UsuarioDecisao = null
                            };
                            divergencias.Add(divergencia);

                            await RegistroProcessamentoHelper.RegistrarAsync(
                                itemversao,
                                TipoProcessamento.ItemDuplicado,
                                StatusProcessamento.PossivelmenteDuplicado,
                                "Item processado com indício de duplicidade.",
                                divergencias,
                                new List<Condicao>(),
                                _registroProcessamentoRepositorio,
                                _itemVersaoRepositorio
                            );
                        }
                        else
                        {
                            await RegistroProcessamentoHelper.RegistrarAsync(
                                itemversao,
                                TipoProcessamento.ItemDuplicado,
                                StatusProcessamento.Processado,
                                "Item processado sem indício de duplicidade.",
                                new List<Divergencia>(),
                                new List<Condicao>(),
                                _registroProcessamentoRepositorio,
                                _itemVersaoRepositorio
                            );
                        }
                    }
                    catch (Exception ex)
                    {
                        // Marca o status da versão como erro
                        StatusAtualizador.SetStatusProcessamentoItemVersao(itemversao, TipoProcessamento.ItemDuplicado, StatusProcessamento.Erro);
                        await _itemVersaoRepositorio.AtualizarAsync(itemversao);

                        // Marca o status do item como erro
                        item.StatusProcessamento = StatusProcessamento.Erro;
                        await _itemRepositorio.AtualizarAsync(item);

                        // Log do erro
                        _logger.LogError(ex, $"Erro ao processar versão {itemversao.Id} do item {item.Id} no agregador {idAgregador} no processo Possivel Item Duplicado");
                    }
                    await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(item.Id, _itemRepositorio, _itemVersaoRepositorio);
                }
            }
        }
    }
}