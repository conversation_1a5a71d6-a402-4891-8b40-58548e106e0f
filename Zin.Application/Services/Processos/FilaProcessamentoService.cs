
using Zin.Application.Services.Processos.Interfaces;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Processos
{
    public class FilaProcessamentoService: IFilaProcessamentoService
    {
        private readonly IFilaProcessamentoRepository _repository;

        public FilaProcessamentoService(IFilaProcessamentoRepository repository)
        {
            _repository = repository;
        }

        public async Task<FilaProcessamento?> BuscarPorIdAsync(Guid id)
        {
            return await _repository.BuscarPorIdAsync(id);
        }

        public async Task<List<FilaProcessamento>> ListarTodosAsync()
        {
            return await _repository.ListarTodosAsync();
        }

        public async Task InserirAsync(FilaProcessamento fila)
        {
            await _repository.InserirAsync(fila);
        }

        public async Task AtualizarAsync(FilaProcessamento fila)
        {
            await _repository.AtualizarAsync(fila);
        }
    }
}
