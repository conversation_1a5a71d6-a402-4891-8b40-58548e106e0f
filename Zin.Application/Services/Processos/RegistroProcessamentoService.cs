using System;
using System.Threading.Tasks;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Processos
{
    public class RegistroProcessamentoService : IRegistroProcessamentoService
    {
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepositorio;
        private readonly IItemVersaoRepository _itemVersaoRepositorio;

        public RegistroProcessamentoService(
            IRegistroProcessamentoRepository registroProcessamentoRepositorio,
            IItemVersaoRepository itemVersaoRepositorio)
        {
            _registroProcessamentoRepositorio = registroProcessamentoRepositorio;
            _itemVersaoRepositorio = itemVersaoRepositorio;
        }

       
    }
}