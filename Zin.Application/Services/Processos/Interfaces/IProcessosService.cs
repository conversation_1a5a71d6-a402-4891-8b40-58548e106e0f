using Zin.Domain.Entidades.ZinPag.Agregadores;

namespace Zin.Application.Services.Processos.Interfaces
{
    public interface IProcessosService
    {
        /// <summary>
        /// Executa o processamento de verificação de itens duplicados para um agregador.
        /// </summary>
        Task ProcessaItemDuplicadoAsync();

        /// <summary>
        /// Executa o processamento de verificação de pagamentos duplicados para um agregador.
        /// </summary>
        Task ProcessaPagamentoDuplicadoAsync();

        /// <summary>
        /// Executa o processamento de verificação de possivel pagamentos para um agregador.
        /// </summary>
        Task ProcessaPossivelPagamentoAsync();

        /// <summary>
        /// Executa o processamento de verificação de possivel ressarcimento para um agregador.
        /// </summary>
        Task ProcessaPossivelRessarcimentoAsync();

        /// <summary>
        /// Executa todos os processos sequencialmente para um agregador.
        /// </summary>
        Task ProcessaTodosProcessosAsync();
    }
}
