using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Processamento;

namespace Zin.Application.Services.Processos.Interfaces
{
    public interface IFilaProcessamentoService
    {
        Task<FilaProcessamento?> BuscarPorIdAsync(Guid id);
        Task<List<FilaProcessamento>> ListarTodosAsync();
        Task InserirAsync(FilaProcessamento fila);
        Task AtualizarAsync(FilaProcessamento fila);
    }
}
