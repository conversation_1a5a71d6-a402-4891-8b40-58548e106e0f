using Microsoft.Extensions.Logging;
using Zin.Application.Helpers;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Processos
{
    public class PossivelPagamentoService : IPossivelPagamentoService
    {
        private readonly IItemVersaoRepository _itemVersaoRepositorio;
        private readonly IPagamentoRepository _pagamentoRepositorio;
        private readonly IItemRepository _itemRepositorio;
        private readonly ILogger<PossivelPagamentoService> _logger;
        private readonly IConfiguracaoService _configuracaoService;
        private readonly ICondicaoComparadorService _condicaoComparadorService;

        public PossivelPagamentoService(
            IItemVersaoRepository itemVersaoRepositorio,
            IPagamentoRepository pagamentoRepositorio,
            IItemRepository itemRepositorio,
            IAgregadorRepository agregadorRepositorio,
            ILogger<PossivelPagamentoService> logger,
            IConfiguracaoService configuracaoService,
            ICondicaoComparadorService condicaoComparadorService
        )
        {
            _itemVersaoRepositorio = itemVersaoRepositorio;
            _pagamentoRepositorio = pagamentoRepositorio;
            _itemRepositorio = itemRepositorio;
            _logger = logger;
            _configuracaoService = configuracaoService;
            _condicaoComparadorService = condicaoComparadorService;
        }

        public async Task ProcessarAsync(int idAgregador, int idItem)
        {
            var versoes = await _itemVersaoRepositorio.BuscarPorItemIdAsync(idItem);

            foreach (var itemVersao in versoes)
            {
                try
                {
                    // 1. Apenas autorizações do tipo autorizado
                    if (itemVersao.TipoMovimento != TipoMovimentoItem.Autorizacao)
                        continue;

                    // 2. Sem pagamento realizado
                    var pagamentos = await _pagamentoRepositorio.BuscarPagamentosPorItemVersaoIdAsync(itemVersao.Id);
                    if (pagamentos.Any())
                    {
                        // Já foi pago
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Pagamento,
                            StatusProcessamento.PagamentoEfetuado,
                            "Pagamento já efetuado para este item versão.",
                            new List<Divergencia>(),
                            new List<Condicao>(),
                            null,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // 3. Sem exclusão correspondente (mesma DataHoraAutorizacao)
                    var existeExclusao = versoes.Any(v =>
                        v.TipoMovimento == TipoMovimentoItem.Exclusao &&
                        v.DataHoraAutorizacao == itemVersao.DataHoraAutorizacao
                    );
                    if (existeExclusao)
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Pagamento,
                            StatusProcessamento.Processado,
                            "Existe exclusão para esta autorização.",
                            new List<Divergencia>(),
                            new List<Condicao>(),
                            null,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // 3. Verificação dinâmica das regras da configuração
                    var configuracoes = await _configuracaoService.BuscarPorTipoProcessamentoAsync(TipoProcessamento.Pagamento);
                    var apto = true;
                    var condicoes = new List<Condicao>();
                    foreach (var config in configuracoes)
                    {
                        if (config == null)
                        {
                            // Configuração padrão, não deve ser processada
                            continue;
                        }
                        else
                        {
                            var resultadosRegras = await _condicaoComparadorService.VerificaAsync(config.Id, itemVersao);
                            if (resultadosRegras != null)
                            {
                                foreach (var resultadoRegra in resultadosRegras)
                                {
                                    if(!resultadoRegra.Apto)
                                    {
                                        apto = false;
                                    }
                                    
                                    var condicao = new Condicao
                                    {
                                        Apto = resultadoRegra.Apto,
                                        TipoConfiguracao = resultadoRegra.TipoConfiguracao,
                                        Regra = resultadoRegra.NomeRegra,
                                        DataRegistro = DateTime.UtcNow,
                                        Decisao = null,
                                        DataDecisao = null,
                                        UsuarioDecisao = null
                                    };
                                    condicoes.Add(condicao);
                                }
                            }
                        }
                    }

                    if (!apto)
                    {

                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Pagamento,
                            StatusProcessamento.InaptoParaPagamento,
                            "Item Versão esta inapto para pagamento conforme condições",
                            new List<Divergencia>(),
                            condicoes,
                            null,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }
                    // Se passou por todas as condições, é possível pagamento
                    await RegistroProcessamentoHelper.RegistrarAsync(
                        itemVersao,
                        TipoProcessamento.Pagamento,
                        StatusProcessamento.PossivelPagamento,
                        $"ItemVersao apto para pagamento. Valor: {itemVersao.ValorTotal}, Documento(s): {itemVersao.Documento?.Numero})",
                        new List<Divergencia>(),
                        new List<Condicao>(),
                        null,
                        _itemVersaoRepositorio
                    );

                }
                catch (Exception ex)
                {

                    StatusAtualizador.SetStatusProcessamentoItemVersao(itemVersao, TipoProcessamento.Pagamento, StatusProcessamento.Erro);
                    await _itemVersaoRepositorio.AtualizarAsync(itemVersao);

                    if (itemVersao.Item != null)
                    {
                        itemVersao.Item.StatusProcessamento = StatusProcessamento.Erro;
                        await _itemRepositorio.AtualizarAsync(itemVersao.Item);
                    }
                    _logger.LogError(ex, $"Erro ao processar versão {itemVersao.Id} do item {itemVersao.IdItem} no agregador {idAgregador} no processo Possivel Pagamento");
                }
                await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
            }
        }
    }
}