using AutoMapper;
using Zin.Application.DTOs.Contato;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Entidades.Cadastros.Contatos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class ContatoService(
        IMapper mapper,
        IContatoRepository contatoRepository) : IContatoService
    {
        private readonly IMapper _mapper = mapper;
        private readonly IContatoRepository _contatoRepository = contatoRepository;

        public async Task AtualizarContatoAsync(int id, AtualizaContatoDto contatoDto)
        {
            if (contatoDto == null)
                throw new ArgumentNullException(nameof(contatoDto), "Contato não pode ser nulo.");

            var contatoExistente = await _contatoRepository.BuscarPorIdAsync(id);
            if (contatoExistente == null)
                throw new InvalidOperationException($"Contato com id {id} não encontrado.");

            // TODO: (Filipe) Revisar Contato para refletir com nossa entidade atualizada
            //contatoExistente.Nome = contatoDto.Nome;
            //contatoExistente.Email = contatoDto.Email;
            //contatoExistente.Ativo = contatoDto.ativo;

            ////TIPOS A REMOVER
            //var tiposARemover = contatoExistente.Tipos?
            //    .Where(ce => !contatoDto.Tipos.Contains((int)ce.TipoContato))
            //    .ToList();

            ////TIPOS A ADICIONAR
            //var tiposAAdicionar = contatoDto.Tipos?
            //    .Where(ce => !contatoExistente.Tipos.Select(t => (int)t.TipoContato).Contains(ce))
            //    .ToList();

            ////EXCLUI TIPOS
            //foreach (var tipo in tiposARemover)
            //    contatoExistente.Tipos.Remove(tipo);

            ////ADICIONA TIPOS
            //foreach (var tipo in tiposAAdicionar)
            //    contatoExistente.Tipos.Add(new ContatoTipo { TipoContato = (TipoContato)(int)tipo });

            ////TELEFONES A EXCLUIR
            //var telefoneIdsDto = contatoDto.Telefones.Select(t => t.id_contato_telefone).ToArray();
            //var telefonesARemover = contatoExistente.Telefones?
            //    .Where(ce => !telefoneIdsDto.Contains(ce.Id))
            //    .ToList();

            ////EXCLUI TELEFONES
            //foreach (var telefone in telefonesARemover)
            //    contatoExistente.Telefones.Remove(telefone);

            ////ADICIONA TELEFONES
            //foreach (var telefoneDto in contatoDto.Telefones.Where(t => t.id_contato_telefone == 0))
            //{
            //    contatoExistente.Telefones.Add(new ContatoTelefone
            //    {
            //        Tipo = (TipoTelefone)telefoneDto.Tipo,
            //        Numero = telefoneDto.Numero,
            //        Ramal = telefoneDto.Ramal
            //    });
            //}

            await _contatoRepository.AtualizarAsync(contatoExistente);
        }

        public async Task DeletarContatoAsync(int id)
        {
            var contatoExistente = await _contatoRepository.BuscarPorIdAsync(id);
            if (contatoExistente == null)
                throw new InvalidOperationException($"Contato com id {id} não encontrado.");

            await _contatoRepository.DeletarAsync(contatoExistente.Id);
        }

        // TODO: (Filipe) Revisar. Provavelmente deve remover pois Tipos de Contato e Tipo de Telefones foram removidos
        //public IEnumerable<TipoContatoDto> Tipos()
        //{
        //    return Enum.GetValues(typeof(TipoContato))
        //        .Cast<TipoContato>()
        //        .Select(tc => new TipoContatoDto
        //        {
        //            id = (int)tc,
        //            nome = tc.ToString()
        //        });
        //}

        //public IEnumerable<TipoTelefoneDto> TiposTelefones()
        //{
        //    return Enum.GetValues(typeof(TipoTelefone))
        //        .Cast<TipoTelefone>()
        //        .Select(tc => new TipoTelefoneDto
        //        {
        //            id = (int)tc,
        //            nome = tc.ToString()
        //        });
        //}
    }
}