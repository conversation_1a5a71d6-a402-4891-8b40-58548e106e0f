using Zin.Application.DTOs.Configuracao;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Services.ZinPag.Interfaces
{
    public interface ICondicaoComparadorService
    {
        /// <summary>
        /// Verifica se uma entidade atende a todas as regras de uma configuração.
        /// </summary>
        /// <param name="idConfiguracao"></param>
        /// <param name="objetoBase">Objeto base (ex: ItemVersao) do qual os campos serão extraídos dinamicamente conforme as regras.</param>
        /// <returns></returns>
        Task<List<ResultadoRegraDto>> VerificaAsync(Guid idConfiguracao, ItemVersao objetoBase);
    }
}
