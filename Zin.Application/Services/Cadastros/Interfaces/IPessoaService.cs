using Zin.Application.DTOs.Pessoas;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Enums;

namespace Zin.Application.Services.Cadastros.Interfaces
{
    public interface IPessoaService
    {
        Task AtualizarPessoaAsync(int id, AtualizaPessoaDto pessoa);
        Task<PessoaDto?> BuscarPessoaPorIdAsync(int id);
        Task<int> CriaPessoaAsync(CriaPessoaDto criaPessoaDto);
        Task DeletarPessoaAsync(int id);
        Task<IEnumerable<PessoaDto>> ListarPessoasAsync(TipoPessoa? tipoPessoa, string? razaoSocial, string? nomeFantasia, string? cnpj);
        Task<int> IncluirEnderecoAsync(CriaPessoaEnderecoDto dto);
        Task<bool> AlteraEnderecoPrincipal(int idPessoa, int idEndereco);
        Task<int> IncluirContatoAsync(CriaPessoaContatoDto dto);
        Task<int> IncluirDadoBancarioAsync(CriaPessoaDadoBancarioDto dto);
        Task<bool> AlteraDadoBancarioPrincipal(int idPessoa, int idDadoBancario);
    }
}
