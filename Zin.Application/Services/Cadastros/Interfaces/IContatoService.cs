using Zin.Application.DTOs.Contato;

namespace Zin.Application.Services.Cadastros.Interfaces
{
    public interface IContatoService
    {
        Task AtualizarContatoAsync(int id, AtualizaContatoDto contato);
        Task DeletarContatoAsync(int id);
        // TODO: (Filipe) Revisar pois Tipos e TiposTelefones foram removidos
        //IEnumerable<TipoContatoDto> Tipos();
        //IEnumerable<TipoTelefoneDto> TiposTelefones();
    }
}
