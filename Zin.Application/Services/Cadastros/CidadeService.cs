using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Metadata.Ecma335;
using Zin.Application.DTOs.Cidades;
using Zin.Application.DTOs.Estados;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class CidadeService(
        IMapper mapper,
        ICidadeRepository cidadeRepository) : ICidadeService
    {
        private readonly IMapper _mapper = mapper;
        private readonly ICidadeRepository _cidadeRepository = cidadeRepository;

        public async Task<IEnumerable<CidadeDto>> ListarCidadesAsync(int idEstado)
        {
            var cidades = await _cidadeRepository.BuscarAsync(c => c.IdEstado == idEstado);
            return cidades.Select(e =>
                new CidadeDto
                {
                    Id = e.Id,
                    Nome = e.Nome
                });
        }
    }
}