using AutoMapper;
using Zin.Application.DTOs.Bancos;
using Zin.Application.DTOs.Contato;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class BancoService(
        IMapper mapper,
        IBancoRepository bancoRepository) : IBancoService
    {
        private readonly IMapper _mapper = mapper;
        private readonly IBancoRepository _bancoRepository = bancoRepository;

        public async Task<IEnumerable<BancoDto>> ListarBancosAsync()
        {
            var bancos = await _bancoRepository.ListarAsync();
            return bancos.OrderBy(o => o.Codigo).Select(e =>
                new BancoDto
                {
                    Id = e.Id,
                    Nome = $"{e.Codigo} - {e.Nome}"
                });
        }

        public IEnumerable<TipoContaDto> TiposConta()
        {
            return Enum.GetValues(typeof(TipoConta))
                .Cast<TipoConta>()
                .Select(tc => new TipoContaDto
                {
                    id = (int)tc,
                    nome = tc.ToString()
                });
        }
    }
}