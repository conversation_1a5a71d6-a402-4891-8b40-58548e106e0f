using AutoMapper;
using Zin.Application.DTOs.Enderecos;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class EnderecoService(
        IMapper mapper,
        IEnderecoRepository enderecoRepository) : IEnderecoService
    {
        private readonly IMapper _mapper = mapper;
        private readonly IEnderecoRepository _enderecoRepository = enderecoRepository;

        public async Task AtualizarEnderecoAsync(int id, AtualizaEnderecoDto enderecoDto)
        {
            if (enderecoDto == null)
                throw new ArgumentNullException(nameof(enderecoDto), "Endereço não pode ser nulo.");

            var enderecoExistente = await _enderecoRepository.BuscarPorIdAsync(id);
            if (enderecoExistente == null)
                throw new InvalidOperationException($"Endereço com id {id} não encontrado.");

            enderecoExistente.Logradouro = enderecoDto.Logradouro;
            enderecoExistente.Numero = enderecoDto.Numero;
            enderecoExistente.Complemento = enderecoDto.Complemento;
            enderecoExistente.Bairro = enderecoDto.Bairro;
            enderecoExistente.Cep = enderecoDto.Cep;
            enderecoExistente.IdEstado = enderecoDto.estado;
            enderecoExistente.IdCidade = enderecoDto.cidade;

            await _enderecoRepository.AtualizarAsync(enderecoExistente);
        }

        public async Task DeletarEnderecoAsync(int id)
        {
            var enderecoExistente = await _enderecoRepository.BuscarPorIdAsync(id);
            if (enderecoExistente == null)
                throw new InvalidOperationException($"Endereço com id {id} não encontrado.");

            await _enderecoRepository.DeletarAsync(enderecoExistente.Id);
        }
    }
}