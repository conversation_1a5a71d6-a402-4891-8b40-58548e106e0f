using AutoMapper;
using System.Numerics;
using Zin.Application.DTOs.DadoBancario;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class DadoBancarioService(
        IMapper mapper,
        IDadoBancarioRepository dadoBancarioRepository) : IDadoBancarioService
    {
        private readonly IMapper _mapper = mapper;
        private readonly IDadoBancarioRepository _dadoBancarioRepository = dadoBancarioRepository;

        public async Task AtualizarDadoBancarioAsync(int id, AtualizaDadoBancarioDto dadoBancarioDto)
        {
            if (dadoBancarioDto == null)
                throw new ArgumentNullException(nameof(dadoBancarioDto), "Dado Bancário não pode ser nulo.");

            var dadoBancarioExistente = await _dadoBancarioRepository.BuscarPorIdAsync(id);
            if (dadoBancarioExistente == null)
                throw new InvalidOperationException($"Dado Bancário com id {id} não encontrado.");

            // TODO: (Filipe) revisar Dados Bancários para refletir com nossa entidade atualizada
            //dadoBancarioExistente.IdBanco = dadoBancarioDto.IdBanco;
            //dadoBancarioExistente.Agencia = dadoBancarioDto.Agencia;
            //dadoBancarioExistente.AgenciaDV = dadoBancarioDto.AgenciaDv;
            //dadoBancarioExistente.Conta = BigInteger.Parse(dadoBancarioDto.Conta);
            //dadoBancarioExistente.ContaDv = dadoBancarioDto.ContaDv;
            dadoBancarioExistente.TipoConta = (TipoConta)(int)dadoBancarioDto.TipoConta;
            dadoBancarioExistente.Titular = dadoBancarioDto.Titular;
            dadoBancarioExistente.CpfCnpjTitular = dadoBancarioDto.CpfCnpjTitular;
            dadoBancarioExistente.Pix = dadoBancarioDto.Pix;

            await _dadoBancarioRepository.AtualizarAsync(dadoBancarioExistente);
        }

        public async Task DeletarDadoBancarioAsync(int id)
        {
            var dadoBancarioExistente = await _dadoBancarioRepository.BuscarPorIdAsync(id);
            if (dadoBancarioExistente == null)
                throw new InvalidOperationException($"Dado Bancário com id {id} não encontrado.");

            await _dadoBancarioRepository.DeletarAsync(dadoBancarioExistente.Id);
        }
    }
}
