using AutoMapper;
using Zin.Application.DTOs.Estados;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class EstadoService(
        IMapper mapper,
        IEstadoRepository estadoRepository) : IEstadoService
    {
        private readonly IMapper _mapper = mapper;
        private readonly IEstadoRepository _estadoRepository = estadoRepository;

        public async Task<IEnumerable<EstadoDto>> ListarEstadosAsync()
        {
            var estados = await _estadoRepository.ListarAsync();
            return estados.Select(e =>
                new EstadoDto
                {
                    Id = e.Id,
                    Nome = e.Nome,
                    Sigla = e.Sigla
                });
        }
    }
}