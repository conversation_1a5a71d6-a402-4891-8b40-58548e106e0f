using System.Globalization;
using Zin.Application.DTOs.Configuracao;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros.Regras
{
    public class CondicaoComparadorService : ICondicaoComparadorService
    {
        private readonly ICondicaoComparadorRepository _repository;

        public CondicaoComparadorService(ICondicaoComparadorRepository repository)
        {
            _repository = repository;
        }

        // Novo método: recebe objeto base e busca campo conforme tipagem
        public async Task<ResultadoRegraDto> VerificaAsync(Guid idConfiguracao, ItemVersao objetoBase)
        {
            var config = await _repository.GetConfiguracaoComRegrasAsync(idConfiguracao);
            if (config == null)
                throw new ArgumentException($"Configuração não encontrada.");

            foreach (var regra in config.Regras.Where(r => r.Ativo))
            {
                // Extrai campo da tipagem: "TIPO|CAMPO"
                var partesTipagem = regra.Tipagem?.Split('|');
                var tipo = partesTipagem?.Length > 0 ? partesTipagem[0].ToUpperInvariant() : "STRING";
                var campo = partesTipagem?.Length > 1 ? partesTipagem[1] : null;

                // Busca valor do campo (pode ser encadeado, ex: "Veiculo.Status")
                var valorCampo = campo != null ? ObterValorPorCaminho(objetoBase, campo) : null;

                if (!Comparar(regra, tipo, valorCampo))
                    return new ResultadoRegraDto
                    {
                        TipoConfiguracao = config.TipoConfiguracao.ToString() ?? "",
                        NomeRegra = regra.Nome ?? "",
                        Tipagem = regra.Tipagem ?? "",
                        Resultado = false
                    };

            }
            return new ResultadoRegraDto
            {
                TipoConfiguracao = config.TipoConfiguracao.ToString() ?? "",
                NomeRegra = null,
                Tipagem = null,
                Resultado = true
            };
        }

        // Busca valor do campo, com suporte a propriedades encadeadas
        private object? ObterValorPorCaminho(ItemVersao obj, string campo)
        {
            if (obj == null || string.IsNullOrWhiteSpace(campo))
                return null;

            switch (campo)
            {
                case "QuantidadeDiasAposPrazoEntrega":
                    // Retorna DataEntrega de itemVersao (obj)
                    if (obj.DataEntrega.HasValue)
                    {
                        var propEntrega = Convert.ToInt32((DateTime.Now - obj.DataEntrega.Value).TotalDays);
                        return propEntrega;
                    }
                    else
                    {
                        return null;
                    }

                case "SituacaoVeiculo":
                    var veiculo = obj.Ativo as Veiculo;
                    if (veiculo != null)
                    {
                        return veiculo.StatusVeiculo;
                    }
                    return null;

                case "SituacaoNotaFiscalVenda":
                    if (obj.Documento?.TipoDocumento == TipoDocumento.NotaFiscalVenda)
                    {
                        return obj.Documento;
                    }
                    return null;

                case "SituacaoNotaFiscalDevolucao":
                    if (obj.Documento?.TipoDocumento == TipoDocumento.NotaFiscalDevolucao)
                    {
                        return obj.Documento;
                    }
                    return null;

                case "QuantidadeDiasAposExclusao":
                    return obj.DataHoraMovimentacao;

                default:
                    throw new ArgumentException($"Campo '{campo}' não suportado em ObterValorPorCaminho.");
            }
        }

        private bool Comparar(Regra regra, string tipo, object valorCampo)
        {
            object? valorRegra = regra.Valor;
            object? valorComparado = valorCampo;

            if (tipo == "ENUM")
            {
                var partesTipagem = regra.Tipagem?.Split('|');
                var enumName = partesTipagem != null && partesTipagem.Length > 1 ? partesTipagem[1] : null;
                var enumType = enumName != null ? ObterTipoEnum(enumName) : null;
                if (enumType != null)
                {
                    valorRegra = EnumParseSafe(enumType, regra.Valor);
                    valorComparado = EnumParseSafe(enumType, valorCampo?.ToString());
                }
            }
            else if (tipo == "INT" || tipo == "INTEGER")
            {
                valorRegra = int.TryParse(regra.Valor, out var v) ? v : (int?)null;
                valorComparado = int.TryParse(valorCampo?.ToString(), out var v2) ? v2 : (int?)null;
            }
            else if (tipo == "DOUBLE" || tipo == "DECIMAL" || tipo == "FLOAT")
            {
                valorRegra = double.TryParse(regra.Valor, NumberStyles.Any, CultureInfo.InvariantCulture, out var d1) ? d1 : (double?)null;
                valorComparado = double.TryParse(valorCampo?.ToString(), NumberStyles.Any, CultureInfo.InvariantCulture, out var d2) ? d2 : (double?)null;
            }
            else if (tipo == "DATE")
            {
                // valorRegra: número de dias
                valorRegra = double.TryParse(regra.Valor, NumberStyles.Any, CultureInfo.InvariantCulture, out var diasRegra) ? diasRegra : (double?)null;
                DateTime? dtCampo = valorCampo as DateTime?;
                if (dtCampo == null)
                    dtCampo = DateTime.TryParse(valorCampo?.ToString(), CultureInfo.InvariantCulture, DateTimeStyles.None, out var dt) ? dt : (DateTime?)null;
                valorComparado = dtCampo.HasValue ? (DateTime.Now.Date - dtCampo.Value.Date).TotalDays : (double?)null;
            }
            else if (tipo == "DATETIME")
            {
                // valorRegra: número de dias
                valorRegra = double.TryParse(regra.Valor, NumberStyles.Any, CultureInfo.InvariantCulture, out var diasRegra) ? diasRegra : (double?)null;
                DateTime? dtCampo = valorCampo as DateTime?;
                if (dtCampo == null)
                    dtCampo = DateTime.TryParse(valorCampo?.ToString(), CultureInfo.InvariantCulture, DateTimeStyles.None, out var dt) ? dt : (DateTime?)null;
                valorComparado = dtCampo.HasValue ? (DateTime.Now - dtCampo.Value).TotalDays : (double?)null;
            }
            else if (tipo == "BOOLEAN" || tipo == "BOOL")
            {
                valorRegra = bool.TryParse(regra.Valor?.ToString(), out var b1) ? b1 : (bool?)null;
                if (valorCampo != null)
                {
                    valorComparado = true;
                }
                else
                {
                    valorComparado = false;
                }
            }
            else
            {
                valorRegra = regra.Valor;
                valorComparado = valorCampo?.ToString();
            }

            // Compara usando operador
            switch (regra.Operador)
            {
                case Zin.Domain.Enums.Processos.OperadorComparacao.Igual:
                    return Equals(valorComparado, valorRegra);
                case Zin.Domain.Enums.Processos.OperadorComparacao.Diferente:
                    return !Equals(valorComparado, valorRegra);
                case Zin.Domain.Enums.Processos.OperadorComparacao.Contem:
                    return valorComparado is string s1 && valorRegra is string s2 && s1.Contains(s2, StringComparison.OrdinalIgnoreCase);
                case Zin.Domain.Enums.Processos.OperadorComparacao.ComecaCom:
                    return valorComparado is string s3 && valorRegra is string s4 && s3.StartsWith(s4, StringComparison.OrdinalIgnoreCase);
                case Zin.Domain.Enums.Processos.OperadorComparacao.TerminaCom:
                    return valorComparado is string s5 && valorRegra is string s6 && s5.EndsWith(s6, StringComparison.OrdinalIgnoreCase);
                case Zin.Domain.Enums.Processos.OperadorComparacao.Maior:
                    return CompararNumerico(valorComparado, valorRegra, (a, b) => a > b);
                case Zin.Domain.Enums.Processos.OperadorComparacao.MaiorOuIgual:
                    return CompararNumerico(valorComparado, valorRegra, (a, b) => a >= b);
                case Zin.Domain.Enums.Processos.OperadorComparacao.Menor:
                    return CompararNumerico(valorComparado, valorRegra, (a, b) => a < b);
                case Zin.Domain.Enums.Processos.OperadorComparacao.MenorOuIgual:
                    return CompararNumerico(valorComparado, valorRegra, (a, b) => a <= b);
                default:
                    return false;
            }
        }

        // Função auxiliar para enums
        private static object? EnumParseSafe(Type enumType, string? value)
        {
            if (string.IsNullOrWhiteSpace(value)) return null;
            try
            {
                return Enum.Parse(enumType, value, ignoreCase: true);
            }
            catch
            {
                return null;
            }
        }

        // Busca o tipo do enum no assembly Zin.Domain.Enums
        private static Type? ObterTipoEnum(string nomeEnum)
        {
            var assembly = typeof(Zin.Domain.Enums.Processos.OperadorComparacao).Assembly;
            return assembly.GetTypes().FirstOrDefault(t =>
                t.IsEnum &&
                t.Name.Equals(nomeEnum, StringComparison.OrdinalIgnoreCase) &&
                t.Namespace == "Zin.Domain.Enums");
        }

        // Função auxiliar para comparação numérica segura
        private static bool CompararNumerico(object? a, object? b, Func<double, double, bool> comp)
        {
            if (a == null || b == null) return false;
            try
            {
                var da = Convert.ToDouble(a);
                var db = Convert.ToDouble(b);
                return comp(da, db);
            }
            catch
            {
                return false;
            }
        }
    }
}
