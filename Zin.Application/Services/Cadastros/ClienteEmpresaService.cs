using Zin.Application.DTOs.Empresas;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class ClienteEmpresaService(IPessoaRepository pessoaRepository) : IClienteEmpresaService
    {
        private readonly IPessoaRepository _pessoaRepository = pessoaRepository;

        public Task<int> CriaClienteEmpresaAsync(CriaClienteEmpresaDto criaEmpresaDto)
        {
            if (criaEmpresaDto == null)
                throw new ArgumentNullException(nameof(criaEmpresaDto), "O DTO de criação de empresa não pode ser nulo.");

            var cnpj = Domain.ValueObject.Cnpj.Criar(criaEmpresaDto.Cnpj);
            var cliente = new ClienteEmpresa(cnpj, criaEmpresaDto.Matriz, criaEmpresaDto.NomeFantasia, criaEmpresaDto.RazaoSocial);
            return _pessoaRepository.InserirAsync(cliente);
        }
    }
}
