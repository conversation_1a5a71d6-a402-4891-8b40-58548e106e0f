using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System.Numerics;
using Zin.Application.DTOs.Pessoas;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Entidades.Cadastros.Contatos;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Entidades.Cadastros.Enderecos;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.ValueObject;

namespace Zin.Application.Services.Cadastros
{
    public class PessoaService(
        IMapper mapper,
        IPessoaRepository pessoaRepository) : IPessoaService
    {
        private readonly IMapper _mapper = mapper;
        private readonly IPessoaRepository _pessoaRepository = pessoaRepository;

        public async Task AtualizarPessoaAsync(int id, AtualizaPessoaDto pessoaDto)
        {
            if (pessoaDto == null)
            {
                throw new ArgumentNullException(nameof(pessoaDto), "Pessoa não pode ser nula.");
            }

            // Busca a pessoa existente
            var pessoaExistente = await _pessoaRepository.BuscarPorIdAsync(id);
            if (pessoaExistente == null)
            {
                throw new InvalidOperationException($"Pessoa com id {id} não encontrada.");
            }

            // Atualiza apenas os campos populados no DTO
            switch (pessoaDto.TipoPessoa)
            {
                case TipoPessoa.Fisica when pessoaExistente is PessoaFisica pf:
                    if (!string.IsNullOrWhiteSpace(pessoaDto.Nome))
                        pf.Nome = pessoaDto.Nome;
                    if (!string.IsNullOrWhiteSpace(pessoaDto.Sobrenome))
                        pf.Sobrenome = pessoaDto.Sobrenome;
                    if (!string.IsNullOrWhiteSpace(pessoaDto.Cpf))
                        pf.Cpf = pessoaDto.Cpf;
                    break;

                case TipoPessoa.Juridica when pessoaExistente is PessoaJuridica pj:
                    if (!string.IsNullOrWhiteSpace(pessoaDto.RazaoSocial))
                        pj.DefinirRazaoSocial(pessoaDto.RazaoSocial);
                    if (!string.IsNullOrWhiteSpace(pessoaDto.NomeFantasia))
                        pj.DefinirNomeFantasia(pessoaDto.NomeFantasia);
                    if (!string.IsNullOrWhiteSpace(pessoaDto.Cnpj))
                        pj.DefinirCnpj(Cnpj.Criar(pessoaDto.Cnpj));
                    break;

                default:
                    throw new ArgumentException("Tipo de pessoa inválido ou incompatível com a entidade existente.", nameof(pessoaDto));
            }

            await _pessoaRepository.AtualizarAsync(pessoaExistente);
        }

        public async Task<PessoaDto?> BuscarPessoaPorIdAsync(int id)
        {
            var pessoa = await _pessoaRepository.BuscarPorIdAsync(id);
            if (pessoa is PessoaFisica pf)
            {
                return new PessoaDto
                {
                    Id = pf.Id,
                    TipoPessoa = pf.TipoPessoa,
                    Nome = pf.Nome,
                    Sobrenome = pf.Sobrenome,
                    Cpf = pf.Cpf
                };
            }
            if (pessoa is PessoaJuridica pj)
            {
                return new PessoaDto
                {
                    Id = pj.Id,
                    TipoPessoa = pj.TipoPessoa,
                    RazaoSocial = pj.RazaoSocial,
                    NomeFantasia = pj.NomeFantasia,
                    Cnpj = pj.Cnpj,
                    Enderecos = pj.PessoasEnderecos
                        .Select(e => new PessoaEnderecoDto
                        {
                            Id = e.Endereco.Id,
                            Logradouro = e.Endereco.Logradouro,
                            Numero = e.Endereco.Numero,
                            Complemento = e.Endereco.Complemento,
                            Bairro = e.Endereco.Bairro,
                            estado = e.Endereco.IdEstado,
                            cidade = e.Endereco.IdCidade,
                            Cep = Cep.Criar(e.Endereco.Cep),
                            principal = e.Principal
                        })
                        .ToArray(),
                    Contatos = pj.PessoasContatos
                        .Select(c => new PessoaContatoDto
                        {
                            // TODO: (Filipe) revisar Contato para ajustar de acordo com a nossa entidade atualizada
                            //Id = c.Contato.Id,
                            //idPessoa = pessoa.Id,
                            //Nome = c.Contato?.Nome,
                            //Email = c.Contato?.Email,
                            //ativo = c.Contato.Ativo,
                            //Tipos = c.Contato?.Tipos?.Select(t => (int)t.TipoContato).ToArray(),
                            //Telefones = c.Contato?.Telefones?.Select(t => new PessoaContatoTelefoneDto
                            //{
                            //    id_contato_telefone = t.Id,
                            //    Tipo = (int)t.Tipo,
                            //    Numero = t.Numero,
                            //    Ramal = t.Ramal
                            //}

                            //).ToArray(),
                        })
                        .ToArray(),
                    DadosBancarios = pj.DadosBancarios
                        .Select(b => new PessoaDadoBancarioDto
                        {
                            id_dados_bancarios = b.DadoBancario.Id,
                            IdBanco = b.DadoBancario.IdBanco,
                            NomeBanco = $"{b.DadoBancario.Banco.Codigo} - {b.DadoBancario.Banco.Nome}",
                            Agencia = b.DadoBancario.Agencia.ToString(),
                            AgenciaDv = b.DadoBancario.AgenciaDV?.ToString() ?? "",
                            Conta = b.DadoBancario.Conta.ToString(),
                            ContaDv = b.DadoBancario.ContaDv?.ToString() ?? "",
                            TipoConta = b.DadoBancario.TipoConta,
                            Titular = b.DadoBancario.Titular,
                            CpfCnpjTitular = b.DadoBancario.CpfCnpjTitular,
                            Pix = b.DadoBancario.Pix,
                            Principal = b.Principal
                        })
                        .ToArray(),
                };
            }
            if (pessoa != null)
            {
                return new PessoaDto
                {
                    Id = pessoa.Id,
                    TipoPessoa = pessoa.TipoPessoa
                };
            }
            return null;
        }

        public async Task<int> CriaPessoaAsync(CriaPessoaDto dto)
        {
            Pessoa pessoa;

            //PESSOA
            if (dto.TipoPessoa == TipoPessoa.Fisica)
            {
                pessoa = new PessoaFisica
                {
                    TipoPessoa = dto.TipoPessoa,
                    Nome = dto.Nome!,
                    Sobrenome = dto.Sobrenome!,
                    Cpf = dto.Cpf!
                };
            }
            else if (dto.TipoPessoa == TipoPessoa.Juridica)
                pessoa = new PessoaJuridica(Cnpj.Criar(dto.Cnpj), dto.RazaoSocial ?? "", dto.NomeFantasia ?? "");
            else
                throw new ArgumentException("Tipo de pessoa inválido.", nameof(dto));

            //ENDEREÇOS
            if (dto.Enderecos != null && dto.Enderecos.Count != 0)
            {
                pessoa.PessoasEnderecos = [.. dto.Enderecos.Select(e => new PessoaEndereco
                    {
                        Endereco = new Domain.Entidades.Cadastros.Enderecos.Endereco
                        {
                            Logradouro = e.Logradouro ?? "",
                            Numero = e.Numero,
                            Complemento = e.Complemento ?? "",
                            Bairro = e.Bairro ?? "",
                            IdEstado = e.estado,
                            IdCidade = e.cidade,
                            // TODO: (Filipe) Rever o tipo do CEP
                            // Cep = e.Cep ?? 0
                            Cep = string.Empty
                        }
                    })];
            }

            //CONTATOS
            if (dto.Contatos != null && dto.Contatos.Count() != 0)
            {
                pessoa.PessoasContatos = [.. dto.Contatos.Select(c => new PessoaContato
                    {
                        // TODO: (Filipe) Rever o DTO de Contatos para ajustar de acordo com a nossa entidade atualizada
                        //Contato = new Contato
                        //{
                        //    Nome = c.Nome,
                        //    Email = c.Email,
                        //    Ativo = c.Ativo,
                        //    Tipos = c.Tipos?.Select(t => new ContatoTipo
                        //    {
                        //        TipoContato = (TipoContato)(int)t
                        //    }).ToList(),
                        //    Telefones = c.Telefones?.Select(t => new ContatoTelefone
                        //    {
                        //        Tipo = (TipoTelefone)(int)t.Tipo,
                        //        Numero = t.Numero,
                        //        Ramal = t.Ramal
                        //    }).ToList()
                        //}
                    }).ToList()];
            }

            //DADOS BANCÁRIOS
            if (dto.DadosBancarios != null && dto.DadosBancarios.Count() != 0)
            {
                pessoa.DadosBancarios = [.. dto.DadosBancarios.Select(d => new PessoaDadoBancario
                    {
                        // TODO: (Filipe) Rever o DTO de DadoBancario pois não está compatível com a nossa entidade atualizada
                        // Também o .net não terá o Id do banco, apenas o código
                        //DadoBancario = new DadoBancario
                        //{
                        //    IdBanco = d.IdBanco,
                        //    Agencia = d.Agencia,
                        //    AgenciaDV = d.AgenciaDv,
                        //    Conta = BigInteger.Parse(d.Conta),
                        //    ContaDv = d.ContaDv,
                        //    TipoConta = (TipoConta)(int)d.TipoConta,
                        //    Pix = d.Pix,
                        //    Titular = d.Titular,
                        //    CpfCnpjTitular = d.CpfCnpjTitular,
                        //}
                    })];
            }

            return await _pessoaRepository.InserirAsync(pessoa);
        }

        public async Task DeletarPessoaAsync(int id)
        {
            await _pessoaRepository.DeletarAsync(id);
        }

        public async Task<IEnumerable<PessoaDto>> ListarPessoasAsync(TipoPessoa? tipoPessoa, string? razaoSocial, string? nomeFantasia, string? cnpj)
        {
            var pessoas = await _pessoaRepository.BuscarAsync(p =>
                (tipoPessoa == null || (p.TipoPessoa == tipoPessoa.Value)) &&
                (razaoSocial == null || (p is PessoaJuridica && EF.Functions.Like(((PessoaJuridica)p).RazaoSocial.ToLower(), $"%{razaoSocial.ToLower()}%"))) &&
                (nomeFantasia == null || (p is PessoaJuridica && EF.Functions.Like(((PessoaJuridica)p).NomeFantasia.ToLower(), $"%{nomeFantasia.ToLower()}%"))) &&
                (cnpj == null || (p is PessoaJuridica && EF.Functions.Like(((PessoaJuridica)p).Cnpj.ToLower(), $"%{cnpj.ToLower()}%")))
            );

            return pessoas.Select(p =>
                p switch
                {
                    PessoaFisica pf => new PessoaDto
                    {
                        Id = pf.Id,
                        TipoPessoa = pf.TipoPessoa,
                        Nome = pf.Nome,
                        Sobrenome = pf.Sobrenome,
                        Cpf = pf.Cpf
                    },
                    PessoaJuridica pj => new PessoaDto
                    {
                        Id = pj.Id,
                        TipoPessoa = pj.TipoPessoa,
                        RazaoSocial = pj.RazaoSocial,
                        NomeFantasia = pj.NomeFantasia,
                        Cnpj = pj.Cnpj
                    },
                    _ => new PessoaDto
                    {
                        Id = p.Id,
                        TipoPessoa = p.TipoPessoa
                    }
                }
            ).OrderBy(p => p.RazaoSocial);
        }

        public async Task<int> IncluirEnderecoAsync(CriaPessoaEnderecoDto dto)
        {
            if (dto == null)
                throw new ArgumentNullException(nameof(dto), "DTO de endereço não pode ser nulo.");

            // Busca a pessoa pelo id informado
            var pessoa = await _pessoaRepository.BuscarPorIdAsync(dto.pessoa);
            if (pessoa == null)
                throw new InvalidOperationException($"Pessoa com id {dto.pessoa} não encontrada.");

            // Cria o endereço
            var endereco = new Domain.Entidades.Cadastros.Enderecos.Endereco
            {
                Logradouro = dto.Logradouro ?? "",
                Numero = dto.Numero,
                Complemento = dto.Complemento ?? "",
                Bairro = dto.Bairro ?? "",
                IdEstado = dto.estado,
                IdCidade = dto.cidade,
                // TODO: (Filipe) Rever o tipo do CEP
                // Cep = e.Cep ?? 0
                Cep = string.Empty
            };

            // Cria o vínculo entre pessoa e endereço
            var pessoaEndereco = new PessoaEndereco
            {
                Endereco = endereco
            };

            // Adiciona o endereço à pessoa
            if (pessoa.PessoasEnderecos == null)
                pessoa.PessoasEnderecos = new List<PessoaEndereco>();

            pessoa.PessoasEnderecos.Add(pessoaEndereco);

            // Atualiza a pessoa no repositório
            await _pessoaRepository.AtualizarAsync(pessoa);

            // Retorna o id do endereço criado
            return endereco.Id;
        }

        public async Task<bool> AlteraEnderecoPrincipal(int idPessoa, int idEndereco)
        {
            // Busca a pessoa pelo id informado
            var pessoa = await _pessoaRepository.BuscarPorIdAsync(idPessoa);
            if (pessoa == null || pessoa.PessoasEnderecos == null || !pessoa.PessoasEnderecos.Any())
                return false;

            bool enderecoEncontrado = false;

            foreach (var pessoaEndereco in pessoa.PessoasEnderecos)
            {
                if (pessoaEndereco.Endereco.Id == idEndereco)
                {
                    pessoaEndereco.Principal = true;
                    enderecoEncontrado = true;
                }
                else
                {
                    pessoaEndereco.Principal = false;
                }
            }

            if (!enderecoEncontrado)
                return false;

            await _pessoaRepository.AtualizarAsync(pessoa);
            return true;
        }

        public async Task<int> IncluirContatoAsync(CriaPessoaContatoDto dto)
        {
            if (dto == null)
                throw new ArgumentNullException(nameof(dto), "DTO de contato não pode ser nulo.");

            // Busca a pessoa pelo id informado
            var pessoa = await _pessoaRepository.BuscarPorIdAsync(dto.idPessoa.Value);
            if (pessoa == null)
                throw new InvalidOperationException($"Pessoa com id {dto.idPessoa} não encontrada.");

            // Cria o contato
            // TODO: (Filipe) Rever o DTO de Contatos para ajustar de acordo com a nossa entidade atualizada
            //var contato = new Contato
            //{
            //    Nome = dto.Nome,
            //    Email = dto.Email,
            //    Tipos = dto.Tipos?.Select(t => new ContatoTipo
            //    {
            //        TipoContato = (TipoContato)(int)t
            //    }).ToList(),
            //    Ativo = dto.Ativo
            //};

            // Cria o vínculo entre pessoa e contato
            var pessoaContato = new PessoaContato
            {
                // TODO: (Filipe) Revisar essa parte do Contato
                //Contato = contato
            };

            // Adiciona o contato à pessoa
            if (pessoa.PessoasContatos == null)
                pessoa.PessoasContatos = new List<PessoaContato>();

            pessoa.PessoasContatos.Add(pessoaContato);

            // Atualiza a pessoa no repositório
            await _pessoaRepository.AtualizarAsync(pessoa);

            // Retorna o id do contato criado
            // TODO: (Filipe) Revisar retorno
            return 1;// contato.Id;
        }

        public async Task<int> IncluirDadoBancarioAsync(CriaPessoaDadoBancarioDto dto)
        {
            if (dto == null)
                throw new ArgumentNullException(nameof(dto), "DTO de dado bancário não pode ser nulo.");

            // Busca a pessoa pelo id informado
            var pessoa = await _pessoaRepository.BuscarPorIdAsync(dto.idPessoa ?? 0);
            if (pessoa == null)
                throw new InvalidOperationException($"Pessoa com id {dto.idPessoa} não encontrada.");

            // Cria o dado bancário
            // TODO: (Filipe) Rever o DTO de DadoBancario pois não está compatível com a nossa entidade atualizada
            // Também o .net não terá o Id do banco, apenas o código
            //var dadoBancario = new DadoBancario
            //{
            //    IdBanco = dto.IdBanco,
            //    Agencia = dto.Agencia,
            //    AgenciaDV = dto.AgenciaDv,
            //    Conta = BigInteger.Parse(dto.Conta),
            //    ContaDv = dto.ContaDv,
            //    TipoConta = (TipoConta)(int)dto.TipoConta,
            //    Pix = dto.Pix,
            //    Titular = dto.Titular,
            //    CpfCnpjTitular = dto.CpfCnpjTitular
            //};

            // Cria o vínculo entre pessoa e contato
            var pessoaDadoBancario = new PessoaDadoBancario
            {
                // TODO: (Filipe) revisar
                //DadoBancario = dadoBancario
            };

            // Adiciona o contato à pessoa
            if (pessoa.DadosBancarios == null)
                pessoa.DadosBancarios = new List<PessoaDadoBancario>();

            pessoa.DadosBancarios.Add(pessoaDadoBancario);

            // Atualiza a pessoa no repositório
            await _pessoaRepository.AtualizarAsync(pessoa);

            // Retorna o id do dado bancario criado
            // TODO: (Filipe) Revisar retorno
            return 1;// dadoBancario.Id;
        }

        public async Task<bool> AlteraDadoBancarioPrincipal(int idPessoa, int idDadoBancario)
        {
            // Busca a pessoa pelo id informado
            var pessoa = await _pessoaRepository.BuscarPorIdAsync(idPessoa);
            if (pessoa == null || pessoa.DadosBancarios == null || !pessoa.DadosBancarios.Any())
                return false;

            bool dadoBancarioEncontrado = false;

            foreach (var pessoaDadoBancario in pessoa.DadosBancarios)
            {
                if (pessoaDadoBancario.DadoBancario.Id == idDadoBancario)
                {
                    pessoaDadoBancario.Principal = true;
                    dadoBancarioEncontrado = true;
                }
                else
                {
                    pessoaDadoBancario.Principal = false;
                }
            }

            if (!dadoBancarioEncontrado)
                return false;

            await _pessoaRepository.AtualizarAsync(pessoa);
            return true;
        }
    }
}