using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class DivergenciaService : IDivergenciaService
    {
        private readonly IDivergenciaRepository _divergenciaRepository;

        public DivergenciaService(IDivergenciaRepository divergenciaRepository)
        {
            _divergenciaRepository = divergenciaRepository;
        }

        public async Task<bool> AtualizarDecisaoAsync(Guid idDivergencia, DecisaoDivergenciaOuCondicao decisao, string usuarioDecisao)
        {
            var divergencia = await _divergenciaRepository.BuscarPorIdAsync(idDivergencia);
            if (divergencia == null)
                return false;

            divergencia.Decisao = decisao;
            divergencia.DataDecisao = DateTime.UtcNow;
            divergencia.UsuarioDecisao = usuarioDecisao;

            await _divergenciaRepository.AtualizarAsync(divergencia);
            return true;
        }
    }
}
