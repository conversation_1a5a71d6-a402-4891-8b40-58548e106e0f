using System.Reflection.Metadata.Ecma335;
using System.Text.Json;
using System.Text.Json.Nodes;
using AutoMapper;
using Zin.Application.DTOs.Dashboard;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class DashboardService(IDashboardRepository dashboardRepository,
        IRessarcimentoRepository ressarcimentoRepository,
        IPagamentoRepository pagamentoRepository) : IDashboardService
    {
        private readonly IDashboardRepository _dashboardRepository = dashboardRepository;
        private readonly IRessarcimentoRepository _ressarcimentoRepository = ressarcimentoRepository;
        private readonly IPagamentoRepository _pagamentoRepository = pagamentoRepository;
        private readonly JsonSerializerOptions options = 
            new()
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

        public async Task<CardDTO> GerarInformacoesCardsAsync() // TODO: Criar uma dto para fazer os filtros, atualmente pega todo os dados
        {
            #region BASE
            var pagamentos = await _pagamentoRepository.ListarAsync();

            var ressarcimentos = await _ressarcimentoRepository.ListarAsync();
            #endregion

            if (!pagamentos.Any() && !ressarcimentos.Any())
                return new CardDTO();

            #region AUTORIZADO BRUTO
            var exclusaoAntesDoPagamento = 126.73m; //TODO: Falta fazer a consulta dessa informação

            var pagoSemExclusao = 2200.00m; //TODO: Falta fazer a consulta dessa informação

            var pagoComExclusao = ressarcimentos
                .Sum(ressarcimento =>
                    ressarcimento.Valor);

            var apagar = pagamentos
                .Sum(pagamento =>
                    pagamento.Valor);
            #endregion

            #region APAGAR
            var pagar = pagamentos
                .Where(pagamento => pagamento.LiquidacoesPagamentos.Count == 0).Sum(item => item.Valor);

            var pagos = pagamentos
                .Where(pagamento => pagamento.LiquidacoesPagamentos.Count != 0).Sum(item => item.Valor);
            #endregion

            #region RESSARCIMENTO
            var ressarcir = ressarcimentos
                .Where(ressarcimento => ressarcimento.LiquidacoesRessarcimentos.Count == 0)
                .Sum(item => item.Valor)
                +
                ressarcimentos
                .Where(ressarcimento => ressarcimento.LiquidacoesRessarcimentos.Count != 0)
                .Sum(item => item.Valor - item.LiquidacoesRessarcimentos.Sum(lr => lr.Valor));

            var ressarcidos = ressarcimentos
                .Where(ressarcimento => ressarcimento.LiquidacoesRessarcimentos.Count != 0)
                .Sum(item => item.LiquidacoesRessarcimentos.Sum(item => item.Valor));
            #endregion

            var totalAutorizado = exclusaoAntesDoPagamento + pagoSemExclusao + pagoComExclusao + apagar;
            var totalPagar = pagar + pagos;
            var totalRessarcimento = ressarcir + ressarcidos;

            decimal Round(decimal value) => Math.Round(value, 2);

            var cardDto = new CardDTO()
            {
                Autorizado = new AutorizadoDTO()
                {
                    Total = Round(totalAutorizado),
                    ExclusaoAntesPagamento = new CardItemDTO()
                    {
                        Valor = Round(exclusaoAntesDoPagamento),
                        Porcentagem = Round(totalAutorizado == 0 ? 0 : (exclusaoAntesDoPagamento / totalAutorizado) * 100)
                    },
                    PagoSemExclusao = new CardItemDTO()
                    {
                        Valor = Round(pagoSemExclusao),
                        Porcentagem = Round(totalAutorizado == 0 ? 0 : (pagoSemExclusao / totalAutorizado) * 100)
                    },
                    PagoComExclusao = new CardItemDTO()
                    {
                        Valor = Round(pagoComExclusao),
                        Porcentagem = Round(totalAutorizado == 0 ? 0 : (pagoComExclusao / totalAutorizado) * 100)
                    },
                    Pagar = new CardItemDTO()
                    {
                        Valor = Round(apagar),
                        Porcentagem = Round(totalAutorizado == 0 ? 0 : (apagar / totalAutorizado) * 100)
                    }
                },
                Pagar = new PagarDTO()
                {
                    Total = Round(totalPagar),
                    NfsIncluidas = new CardItemDTO()
                    {
                        Valor = Round(pagos),
                        Porcentagem = Round(totalPagar == 0 ? 0 : (pagos / totalPagar) * 100)
                    },
                    NfsPendentes = new CardItemDTO()
                    {
                        Valor = Round(pagar),
                        Porcentagem = Round(totalPagar == 0 ? 0 : (pagar / totalPagar) * 100)
                    }
                },
                Ressarcimento = new RessarcimentoDTO()
                {
                    Total = Round(totalRessarcimento),
                    Realizados = new CardItemDTO()
                    {
                        Valor = Round(ressarcidos),
                        Porcentagem = Round(totalRessarcimento == 0 ? 0 : (ressarcidos / totalRessarcimento) * 100)
                    },
                    Pendentes = new CardItemDTO()
                    {
                        Valor = Round(ressarcir),
                        Porcentagem = Round(totalRessarcimento == 0 ? 0 : (ressarcir / totalRessarcimento) * 100)
                    }
                }
            };

            var dashboard =
                await _dashboardRepository.ListarAsync();

            foreach (var dash in dashboard)
            {
                dash.DataRegistro = DateTime.UtcNow;

                if (dash.Tipo == TipoProcessamentoDashboard.APagar)
                {
                    dash.Dados = JsonSerializer.Serialize(cardDto.Pagar);
                }
                else if (dash.Tipo == TipoProcessamentoDashboard.AutorizadoBruto)
                {
                    dash.Dados = JsonSerializer.Serialize(cardDto.Autorizado);
                }
                else if (dash.Tipo == TipoProcessamentoDashboard.Ressarcimento)
                {
                    dash.Dados = JsonSerializer.Serialize(cardDto.Ressarcimento);
                }

                await _dashboardRepository.AtualizarAsync(dash);
            }

            return cardDto;
        }

        public async Task<CardDTO> ObterCardsAsync()
        {
            var processamentoDashboards = await _dashboardRepository.ListarAsync();

            if (!processamentoDashboards.Any())
                return new CardDTO();

            var pagarDTO = ObterPagar(processamentoDashboards);
            var autorizadoDTO = ObterAutorizado(processamentoDashboards);
            var ressarcimentoDTO = ObterRessarcimento(processamentoDashboards);

            #region TESTE
            //pagarDTO = new PagarDTO()
            //{
            //    Total = 3167.76m,
            //    NfsIncluidas = new CardItemDTO()
            //    {
            //        Valor = 1697.76m,
            //        Porcentagem = 53.59m
            //    },
            //    NfsPendentes = new CardItemDTO()
            //    {
            //        Valor = 1470.00m,
            //        Porcentagem = 46.41m
            //    }
            //};

            //autorizadoDTO = new AutorizadoDTO()
            //{
            //    Total = 6160.25m,
            //    ExclusaoAntesPagamento = new CardItemDTO()
            //    {
            //        Valor = 126.73m,
            //        Porcentagem = 2.06m
            //    },
            //    PagoSemExclusao = new CardItemDTO()
            //    {
            //        Valor = 2200.00m,
            //        Porcentagem = 35.71m
            //    },
            //    PagoComExclusao = new CardItemDTO()
            //    {
            //        Valor = 665.76m,
            //        Porcentagem = 10.81m
            //    },
            //    Pagar = new CardItemDTO()
            //    {
            //        Valor = 3167.76m,
            //        Porcentagem = 51.42m
            //    },
            //};

            //ressarcimentoDTO = new RessarcimentoDTO()
            //{
            //    Total = 665.76m,
            //    Realizados = new CardItemDTO()
            //    {
            //        Valor = 300.00m,
            //        Porcentagem = 45.07m
            //    },
            //    Pendentes = new CardItemDTO()
            //    {
            //        Valor = 365.76m,
            //        Porcentagem = 54.93m
            //    }
            //};
            #endregion

            return new CardDTO()
            {
                Autorizado = autorizadoDTO,
                Pagar = pagarDTO,
                Ressarcimento = ressarcimentoDTO
            };
        }

        private PagarDTO? ObterPagar(IEnumerable<ProcessamentoDashboard> processamentoDashboards)
        {
            var pagarDTO = processamentoDashboards
                .Where(item => item.Tipo == TipoProcessamentoDashboard.APagar)
                .MaxBy(item => item.DataRegistro)?.Dados;

            return JsonSerializer.Deserialize<PagarDTO>($"{pagarDTO}", options);
        }

        private AutorizadoDTO? ObterAutorizado(IEnumerable<ProcessamentoDashboard> processamentoDashboards)
        {
            var autorizadoDTO = processamentoDashboards
                .Where(item => item.Tipo == TipoProcessamentoDashboard.AutorizadoBruto)
                .MaxBy(item => item.DataRegistro)?.Dados;

            return JsonSerializer.Deserialize<AutorizadoDTO>($"{autorizadoDTO}", options);
        }

        private RessarcimentoDTO? ObterRessarcimento(IEnumerable<ProcessamentoDashboard> processamentoDashboards)
        {
            var ressarcimentoDTO = processamentoDashboards
                .Where(item => item.Tipo == TipoProcessamentoDashboard.Ressarcimento)
                .MaxBy(item => item.DataRegistro)?.Dados;

            return JsonSerializer.Deserialize<RessarcimentoDTO>($"{ressarcimentoDTO}", options);
        }
    }
}
