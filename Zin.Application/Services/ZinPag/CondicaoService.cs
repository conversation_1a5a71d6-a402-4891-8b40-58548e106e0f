using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class CondicaoService : ICondicaoService
    {
        private readonly ICondicaoRepository _condicaoRepository;

        public CondicaoService(ICondicaoRepository condicaoRepository)
        {
            _condicaoRepository = condicaoRepository;
        }

        public async Task<bool> AtualizarDecisaoAsync(Guid idCondicao, DecisaoDivergenciaOuCondicao decisao, string usuarioDecisao)
        {
            var divergencia = await _condicaoRepository.BuscarPorIdAsync(idCondicao);
            if (divergencia == null)
                return false;

            divergencia.Decisao = decisao;
            divergencia.DataDecisao = DateTime.UtcNow;
            divergencia.UsuarioDecisao = usuarioDecisao;

            await _condicaoRepository.AtualizarAsync(divergencia);
            return true;
        }

    }
}
