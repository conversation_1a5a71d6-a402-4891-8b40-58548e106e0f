using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using Zin.Application.DTOs.Movimentacoes;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;

namespace Zin.Application.Services.ZinPag.Interfaces
{
    public interface IMovimentacoesService
    {
        Task<List<Movimentacao>> ObterOuAtualizarMovimentacoesAsync(List<Item> itensDaImportacao);
        Task<ObterMovimentacaoDetalhesDto?> ObterDetalhesMovimentacaoAsync(Guid idMovimentacao);
        Task<List<MovimentacaoDto>> ListarMovimentacoesAsync();
        Task<List<ItemAgregadorDto>> ObterItensOutrasMovimentacoesAsync(int idAgregador, Guid movId);
        Task<List<ItemAgregadorDto>> ObterItensMesmaMovimentacaoAsync(int idAgregador, Guid movId);
    }
}
