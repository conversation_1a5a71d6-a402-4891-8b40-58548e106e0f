using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Application.DTOs.Ressarcimentos;
using Zin.Domain.Enums;

namespace Zin.Application.Services.ZinPag.Interfaces
{
    public interface IRessarcimentoService
    {
        Task<int> CriarRessarcimentoAsync(CriaRessarcimentoDto dto);
        Task <bool> CancelaRessarcimentoAsync(int id);
        Task<bool> GerarCobrancaAsync(GeraCobrancaDTO idMovimentacao);
        Task<IReadOnlyList<ListagemDTO>> ListarCobradosAsync(FiltrosDTO filtros);
        Task<IReadOnlyList<ListagemDTO>> ListarConcluidosAsync(FiltrosDTO filtros);
        Task<(byte[] fileContents, string contentType, string fileName)?> DownloadComprovanteLiquidacaoRessarcimentoAsync(int liquidacaoId);
    }
}
