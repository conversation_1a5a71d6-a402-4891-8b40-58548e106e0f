using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Handlers;
using Zin.Domain.Entidades.ZinPag.Pagamentos;

namespace Zin.Application.Services.ZinPag.Interfaces
{
     public interface IPagamentoService
     {
        Task<int> CriarPagamentoAsync(CriaPagamentoDTO dto);
        Task<PagamentoManualResult> CancelarPagamentoAsync(int pagamentoId);
        Task<bool>ConfirmarPagamentoManualAsync(int pagamentoId, decimal valorPago);
        Task<PagamentoProcessamentoResponseDTO> ProcessarPagamentosImportadosAsync(List<PagamentoExcelDTO> pagamentosExcel);
        Task<(byte[] fileContents, string contentType, string fileName)?> DownloadComprovanteLiquidacaoAsync(int liquidacaoId);
     }
}
