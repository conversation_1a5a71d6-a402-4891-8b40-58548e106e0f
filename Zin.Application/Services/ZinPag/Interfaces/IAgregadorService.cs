using Zin.Application.DTOs.Agregadores;

namespace Zin.Application.Interfaces
{
    public interface IAgregadorService
    {
        Task<IEnumerable<AgregadorDto>> ListarAgregadoresAsync();
        Task<AgregadorDto?> ObterAgregadorPorIdAsync(int id);
        Task<AgregadorDto> CriarAgregadorAsync(CriaAgregadorDto dto);
        Task AtualizarAgregadorAsync(int id, AtualizaAgregadorDto dto);
        Task RemoverAgregadorAsync(int id);
    }
}