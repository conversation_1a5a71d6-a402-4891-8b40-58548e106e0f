using Zin.Application.DTOs.Documentos;

namespace Zin.Application.Services.ZinPag.Interfaces
{
    public interface IDocumentoService
    {
        Task<int> CriarDocumentoAsync(CriaDocumentoDTO dto, string? cnpjCliente);
        Task<int> CriarNotaFiscalAsync(CriaNotaFiscalDTO notaFiscal);
        Task<bool> CancelarDocumentoAsync(int id);
        Task<DownloadDocumentoDto> DownloadDocumentoAsync(int documentoId);
    }
}
