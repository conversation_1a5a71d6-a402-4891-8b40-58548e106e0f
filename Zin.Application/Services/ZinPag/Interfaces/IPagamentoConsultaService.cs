using Zin.Application.DTOs.Pagamentos;
using Zin.Domain.Enums;

namespace Zin.Application.Services.ZinPag.Interfaces;

public interface IPagamentoConsultaService
{
    Task<PagamentoListagemResponseDTO> ListarPagamentosAsync(
        string idCliente,
        int page,
        int pageSize,
        StatusPagamento? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? cnpjBeneficiario = null,
        string? numeroNF = null);

    Task<LoteImportacaoListagemResponseDTO> ListarLotesImportacaoAsync(
        string idCliente,
        int page,
        int pageSize,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null);

    Task<LoteImportacaoDetalheDTO?> ObterDetalhesLoteAsync(int loteId, string idCliente);

    Task<LinhaImportacaoListagemResponseDTO> ListarLinhasLoteAsync(
        int loteId,
        string idCliente,
        int page,
        int pageSize,
        StatusLinhaPagamento? status = null,
        OperacaoLinhaPagamento? operacao = null);

    Task<(byte[] fileContents, string contentType, string fileName)?> DownloadArquivoLoteAsync(int loteId, string idCliente);
}
