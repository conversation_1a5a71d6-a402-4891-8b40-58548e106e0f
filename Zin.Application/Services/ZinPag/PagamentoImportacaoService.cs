using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Zin.Application.Configuration;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Extension;
using Zin.Infrastructure.Dados;
using Microsoft.AspNetCore.Http;

namespace Zin.Application.Services.ZinPag;

public class PagamentoImportacaoService : IPagamentoImportacaoService
{
    private readonly IExcelMappingConfigurationProvider _mappingProvider;
    private readonly IPagamentoService _pagamentoService;
    private readonly ZinDbContext _context;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public PagamentoImportacaoService(
        IExcelMappingConfigurationProvider mappingProvider,
        IPagamentoService pagamentoService,
        ZinDbContext context,
        IHttpContextAccessor httpContextAccessor)
    {
        _mappingProvider = mappingProvider;
        _pagamentoService = pagamentoService;
        _context = context;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<PagamentoProcessamentoResponseDTO> ImportarAsync(IFormFile file, string cnpj)
    {
        if (file == null || file.Length == 0)
            throw new ArgumentException("Nenhum arquivo enviado.");

        var mapping = _mappingProvider.GetMappingConfig(cnpj);

        using var stream = file.OpenReadStream();
        var pagamentos = LerPagamentosDoExcel(stream, mapping);

        // Processar os pagamentos
        var resultado = await _pagamentoService.ProcessarPagamentosImportadosAsync(pagamentos);

        // Se mais de 1 pagamento foi processado com sucesso, criar lote
        if (resultado.PagamentosComSucesso.Count > 1)
        {
            await CriarLoteParaPagamentosAsync(file.FileName, cnpj, resultado.PagamentosComSucesso);
        }

        return resultado;
    }

    private async Task CriarLoteParaPagamentosAsync(string nomeArquivo, string cnpj, List<DetalhePagamentoProcessadoDTO> pagamentosComSucesso)
    {
        try
        {
            // Obter informações do contexto
            var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "Sistema";
            var idCliente = ObterIdClienteDoContexto();

            // Criar o lote usando o helper
            var idLote = await LoteImportacaoHelper.CriarLoteAsync(_context, idCliente, nomeArquivo, usuario);

            // Associar cada pagamento ao lote
            var numeroLinha = 2; // Linha 1 é cabeçalho, dados começam na linha 2
            foreach (var pagamento in pagamentosComSucesso)
            {
                // Extrair ID do pagamento da mensagem ou usar outro método para identificar
                var idPagamento = ExtrairIdPagamentoDaResposta(pagamento);
                if (idPagamento.HasValue)
                {
                    await LoteImportacaoHelper.AssociarPagamentoAoLoteAsync(_context, idPagamento.Value, idLote, numeroLinha);
                }
                numeroLinha++;
            }

            // Finalizar o lote
            await LoteImportacaoHelper.FinalizarLoteAsync(_context, idLote);
        }
        catch (Exception ex)
        {
            // Log do erro, mas não falha a importação
            // TODO: Adicionar log adequado
            Console.WriteLine($"Erro ao criar lote: {ex.Message}");
        }
    }

    private string ObterIdClienteDoContexto()
    {
        // Assumindo que o cliente está no contexto HTTP (como no controller)
        if (_httpContextAccessor.HttpContext?.Items["ClienteSelecionado"] is Helpers.Clientes.Models.Cliente cliente)
        {
            return cliente.Id;
        }
        return "cliente-desconhecido";
    }

    private int? ExtrairIdPagamentoDaResposta(DetalhePagamentoProcessadoDTO pagamento)
    {
        return pagamento.IdPagamento;
    }

    private static List<PagamentoExcelDTO> LerPagamentosDoExcel(Stream stream, ExcelColumnMappingConfig mapping)
    {
        var resultado = new List<PagamentoExcelDTO>();

        using var workbook = new XLWorkbook(stream);
        var worksheet = workbook.Worksheet(1);
        var dataRows = worksheet.RowsUsed().Skip(1);

        foreach (var row in dataRows)
        {
            var dto = new PagamentoExcelDTO();
            foreach (var prop in typeof(PagamentoExcelDTO).GetProperties())
            {
                if (mapping.ColumnMappings.TryGetValue(prop.Name, out var col))
                    prop.SetValue(dto, row.Cell(col).GetValue<string>());
            }

            if (dto.StatusPagamento?.Equals(StatusPagamento.Pago.GetDescription(), StringComparison.OrdinalIgnoreCase) == true)
                resultado.Add(dto);
        }

        return resultado;
    }
}