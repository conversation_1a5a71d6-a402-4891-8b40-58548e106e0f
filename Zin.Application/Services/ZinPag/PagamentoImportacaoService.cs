using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Zin.Application.Configuration;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Extension;

namespace Zin.Application.Services.ZinPag;

public class PagamentoImportacaoService : IPagamentoImportacaoService
{
    private readonly IExcelMappingConfigurationProvider _mappingProvider;
    private readonly IPagamentoService _pagamentoService;

    public PagamentoImportacaoService(
        IExcelMappingConfigurationProvider mappingProvider,
        IPagamentoService pagamentoService)
    {
        _mappingProvider = mappingProvider;
        _pagamentoService = pagamentoService;
    }

    public async Task<PagamentoProcessamentoResponseDTO> ImportarAsync(IFormFile file, string cnpj)
    {
        if (file == null || file.Length == 0)
            throw new ArgumentException("Nenhum arquivo enviado.");

        var mapping = _mappingProvider.GetMappingConfig(cnpj);

        using var stream = file.OpenReadStream();
        var pagamentos = LerPagamentosDoExcel(stream, mapping);

        return await _pagamentoService.ProcessarPagamentosImportadosAsync(pagamentos);
    }
     
    private static List<PagamentoExcelDTO> LerPagamentosDoExcel(Stream stream, ExcelColumnMappingConfig mapping)
    {
        var resultado = new List<PagamentoExcelDTO>();

        using var workbook = new XLWorkbook(stream);
        var worksheet = workbook.Worksheet(1);
        var dataRows = worksheet.RowsUsed().Skip(1);

        foreach (var row in dataRows)
        {
            var dto = new PagamentoExcelDTO();
            foreach (var prop in typeof(PagamentoExcelDTO).GetProperties())
            {
                if (mapping.ColumnMappings.TryGetValue(prop.Name, out var col))
                    prop.SetValue(dto, row.Cell(col).GetValue<string>());
            }

            if (dto.StatusPagamento?.Equals(StatusPagamento.Pago.GetDescription(), StringComparison.OrdinalIgnoreCase) == true)
                resultado.Add(dto);
        }

        return resultado;
    }
}