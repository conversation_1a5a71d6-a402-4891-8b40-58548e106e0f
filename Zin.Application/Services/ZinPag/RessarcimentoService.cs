using AutoMapper;
using System.Linq.Expressions;
using Zin.Application.DTOs.Movimentacoes;
using Zin.Application.DTOs.Ressarcimentos;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Repositorios.ZinPag;
using System.IO;

namespace Zin.Application.Services.ZinPag
{
    // TODO: (Diego) Revisar toda essa classe
    public class RessarcimentoService(
        IRessarcimentoRepository ressarcimentoRepository,
        IProcessosService processosService,
        IFilaProcessamentoRepository filaProcessamentoRepository,
        IMapper mapper,
        IPagamentoRepository pagamentoRepository,
        ILiquidacaoRessarcimentoRepository liquidacaoRessarcimentoRepository,
        IDocumentoRessarcimentoRepository documentoRessarcimentoRepository,
        IMovimentacoesRepository movimentacoesRepository,
        IRessarcimentoItemVersaoRepository ressarcimentoItemVersaoRepository,
        IPessoaRepository pessoaRepository,
        IAtivoRepository ativoRepository) : IRessarcimentoService
    {
        private readonly IRessarcimentoRepository _ressarcimentoRepository = ressarcimentoRepository;
        private readonly IMapper _mapper = mapper;
        private readonly IPagamentoRepository _pagamentoRepository = pagamentoRepository;
        private readonly IFilaProcessamentoRepository filaProcessamentoRepository = filaProcessamentoRepository;
        private readonly IMovimentacoesRepository _movimentacoesRepository = movimentacoesRepository;
        private readonly IRessarcimentoItemVersaoRepository _ressarcimentoItemVersaoRepository = ressarcimentoItemVersaoRepository;
        private readonly IPessoaRepository _pessoaRepository = pessoaRepository;
        private readonly IAtivoRepository _ativoRepository = ativoRepository;
        private readonly IProcessosService _processosService = processosService;
        private readonly ILiquidacaoRessarcimentoRepository _liquidacaoRessarcimentoRepository = liquidacaoRessarcimentoRepository;
        private readonly IDocumentoRessarcimentoRepository _documentoRessarcimentoRepository = documentoRessarcimentoRepository;

        public Task AtualizaRessarcimentoAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task AtualizaStatusRessarcimento(int id)
        {
            throw new NotImplementedException();
        }

        public async Task<int> CriarRessarcimentoAsync(CriaRessarcimentoDto dto)
        {
            throw new NotImplementedException();
        }


        public Task<bool> CancelaRessarcimentoAsync(int id)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> GerarCobrancaAsync(GeraCobrancaDTO dto)
        {
            if (dto.IdMovimentacao == Guid.Empty) 
                throw new ArgumentNullException(nameof(dto.IdMovimentacao), "Id da movimentação não pode ser vazio.");

            var movimentacao = await _movimentacoesRepository.BuscarPorIdAsync(dto.IdMovimentacao);
            if (movimentacao == null)
                throw new InvalidOperationException($"Movimentação com Id {dto.IdMovimentacao} não encontrada.");

            var pessoa = await _pessoaRepository.BuscarPorIdAsync(movimentacao!.Agregador!.IdClienteEmpresa);

            //var ativo = await _ativoRepository.BuscarPorAgregadorAsync(movimentacao.IdAgregador);

            // Buscar o próximo número de ressarcimento
            var ressarcimentos = await _ressarcimentoRepository.ListarAsync();
            int proximoNumero = ressarcimentos.Any()
                ? ressarcimentos.Max(r => int.TryParse(r.NumeroRessarcimento, out var n) ? n : 0) + 1
                : 1;

            var ressarcimento = new Ressarcimento
            {
                IdClienteEmpresa = pessoa.Id,
                NumeroRessarcimento = proximoNumero.ToString(),
                //IdFornecedor = movimentacao.ItensVersoes.FirstOrDefault().IdPessoaFornecedora,
                IdAgregador = movimentacao.IdAgregador,
                //IdAtivo = ativo.FirstOrDefault()?.Id ?? 0,
                //IdDocumento = movimentacao.IdDocumento,
                //Valor = movimentacao.ItensVersoes.Sum(i => i.ValorTotal),
                DataSolicitacao = DateTime.UtcNow,
                FormaPagamentoRessarcimento = FormasPagamentoRessarcimento.Pix,
                StatusRessarcimento = StatusRessarcimento.Pendente,
                Cancelado = false,
                Descricao = $"Cobrança gerada para movimentação {dto.IdMovimentacao}"
            };

            var ressarcimentoNovo = await _ressarcimentoRepository.InserirAsync(ressarcimento);

            var ressarcimentoItemVersao = new RessarcimentoItemVersao
            {
                IdRessarcimento = ressarcimento.Id,
                //IdItemVersao = movimentacao.ItensVersoes.FirstOrDefault()?.Id ?? 0,
                //DataAutorizacao = movimentacao.ItensVersoes.FirstOrDefault()?.DataHoraAutorizacao ?? DateTime.UtcNow
            };

            if (ressarcimentoItemVersao.IdItemVersao > 0)
            {
                await _ressarcimentoItemVersaoRepository.InserirAsync(ressarcimentoItemVersao);
            }

            //Envio do e-mail, que será implementado na sequência.

            return ressarcimentoNovo > 0;
        }

        public async Task<IReadOnlyList<ListagemDTO>> ListarCobradosAsync(FiltrosDTO filtros)
        {
            Expression<Func<Ressarcimento, bool>> predicate = r =>
                r.StatusRessarcimento == StatusRessarcimento.Pendente &&
                (filtros.NumeroAgregador == null || (r.Agregador != null && r.Agregador.Numero == filtros.NumeroAgregador)) &&
                (filtros.Fornecedor == null || (r.Fornecedor != null && r.Fornecedor is PessoaJuridica && ((PessoaJuridica)r.Fornecedor).NomeFantasia == filtros.Fornecedor)) &&
                (
                    (filtros.DataAutorizacaoInicial == null ||
                        (r.RessarcimentoItemVersoes.Any() &&
                         r.RessarcimentoItemVersoes.FirstOrDefault() != null &&
                         r.RessarcimentoItemVersoes.FirstOrDefault()!.DataAutorizacao >= filtros.DataAutorizacaoInicial)
                    ) &&
                    (filtros.DataAutorizacaoFinal == null ||
                        (r.RessarcimentoItemVersoes.Any() &&
                         r.RessarcimentoItemVersoes.FirstOrDefault() != null &&
                         r.RessarcimentoItemVersoes.FirstOrDefault()!.DataAutorizacao <= filtros.DataAutorizacaoFinal)
                    )
                ) &&
                (
                    (filtros.DataRessarcimentoInicial == null || r.DataSolicitacao >= filtros.DataRessarcimentoInicial) &&
                    (filtros.DataRessarcimentoFinal == null || r.DataSolicitacao <= filtros.DataRessarcimentoFinal)
                ) &&
                //(filtros.NumeroDocumento == null || (r.Documento != null && r.Documento.Numero == filtros.NumeroDocumento)) &&
                (filtros.StatusVeiculo == null || (r.Ativo != null && r.Ativo is Veiculo && ((Veiculo)r.Ativo).StatusVeiculo == filtros.StatusVeiculo)) &&
                (filtros.Placa == null || (r.Ativo != null && r.Ativo is Veiculo && ((Veiculo)r.Ativo).Placa != null && ((Veiculo)r.Ativo).Placa.ToUpper() == filtros.Placa.ToUpper())
                //&&
                //(filtros.Divergencia == null || (
                //    filtros.Divergencia == true
                //        ? r.RessarcimentoItemVersoes.Any(riv => riv.ItemVersao != null && riv.ItemVersao.Divergencias.Any())
                //        : r.RessarcimentoItemVersoes.All(riv => riv.ItemVersao == null || !riv.ItemVersao.Divergencias.Any())
                //)) &&
                //(filtros.Condicao == null || (
                //    filtros.Condicao == true
                //        ? r.RessarcimentoItemVersoes.Any(riv => riv.ItemVersao != null && riv.ItemVersao.Condicoes.Any())
                //        : r.RessarcimentoItemVersoes.All(riv => riv.ItemVersao == null || !riv.ItemVersao.Condicoes.Any())
                );

            var ressarcimentos = await _ressarcimentoRepository.BuscarAsync(predicate);

            var cobrados = ressarcimentos.Select(r =>
            {
                var agregador = r.Agregador?.Numero;
                var fornecedor = (r.Fornecedor is PessoaJuridica pj) ? pj.NomeFantasia : string.Empty;
                var dataAutorizacao = r.RessarcimentoItemVersoes.FirstOrDefault()?.DataAutorizacao ?? default;
                //var numeroDocumento = r.Agregador?.Documentos?.FirstOrDefault()?.Numero ?? r.Documento?.Numero ?? string.Empty;
                var valorTotal = r.Valor;
                var pago = 0m;
                var aPagar = valorTotal - pago;
                var ressarcir = r.Valor;
                //var ressarcido = r.LiquidacoesRessarcimento?.Sum(lr => lr.ValorLiquidado) ?? 0m;
                //var placa = (r.Ativo is Veiculo v) ? v.Placa ?? string.Empty : string.Empty;
                //var statusReparo = (r.Ativo is Veiculo v2) ? new SituacaoDto<StatusVeiculo>(v2.StatusVeiculo) : null;
                //var itemVersao = r.RessarcimentoItemVersoes.FirstOrDefault()?.ItemVersao;
                //var divergencia = itemVersao != null && itemVersao.Divergencias.Count() > 0;
                //var condicao = itemVersao != null && itemVersao.Condicoes.Count() > 0;

                //var possuiDivergencia = divergencia;
                //var possuiCondicao = condicao;

                return new ListagemDTO
                {
                    Agregador = agregador,
                    Fornecedor = fornecedor,
                    DataAutorizacao = dataAutorizacao,
                    //NumeroDocumento = numeroDocumento,
                    //ValorTotal = valorTotal,
                    //APagar = aPagar,
                    //Pago = pago,
                    //Ressarcir = ressarcir,
                    //Ressarcido = ressarcido,
                    //Placa = placa,
                    //StatusReparo = statusReparo?.Descricao,
                    //Divergencia = possuiDivergencia,
                    //Condicao = possuiCondicao
                };
            }).ToList();

            return cobrados;
        }

        public async Task<IReadOnlyList<ListagemDTO>> ListarConcluidosAsync(FiltrosDTO filtros)
        {
            Expression<Func<Ressarcimento, bool>> predicate = r =>
                r.StatusRessarcimento == StatusRessarcimento.Pago &&
                (filtros.NumeroAgregador == null || (r.Agregador != null && r.Agregador.Numero == filtros.NumeroAgregador)) &&
                (filtros.Fornecedor == null || (r.Fornecedor != null && r.Fornecedor is PessoaJuridica && ((PessoaJuridica)r.Fornecedor).NomeFantasia == filtros.Fornecedor)) &&
                (
                    (filtros.DataAutorizacaoInicial == null ||
                        (r.RessarcimentoItemVersoes.Any() &&
                         r.RessarcimentoItemVersoes.FirstOrDefault() != null &&
                         r.RessarcimentoItemVersoes.FirstOrDefault()!.DataAutorizacao >= filtros.DataAutorizacaoInicial)
                    ) &&
                    (filtros.DataAutorizacaoFinal == null ||
                        (r.RessarcimentoItemVersoes.Any() &&
                         r.RessarcimentoItemVersoes.FirstOrDefault() != null &&
                         r.RessarcimentoItemVersoes.FirstOrDefault()!.DataAutorizacao <= filtros.DataAutorizacaoFinal)
                    )
                ) &&
                (
                    (filtros.DataRessarcimentoInicial == null || r.DataSolicitacao >= filtros.DataRessarcimentoInicial) &&
                    (filtros.DataRessarcimentoFinal == null || r.DataSolicitacao <= filtros.DataRessarcimentoFinal)
                ) &&
                //(filtros.NumeroDocumento == null || (r.Documento != null && r.Documento.Numero == filtros.NumeroDocumento)) &&
                (filtros.StatusVeiculo == null || (r.Ativo != null && r.Ativo is Veiculo && ((Veiculo)r.Ativo).StatusVeiculo == filtros.StatusVeiculo)) &&
                (filtros.Placa == null || (r.Ativo != null && r.Ativo is Veiculo && ((Veiculo)r.Ativo).Placa != null && ((Veiculo)r.Ativo).Placa.ToUpper() == filtros.Placa.ToUpper())
                //&&
                //(filtros.Divergencia == null || (
                //    filtros.Divergencia == true
                //        ? r.RessarcimentoItemVersoes.Any(riv => riv.ItemVersao != null && riv.ItemVersao.Divergencias.Any())
                //        : r.RessarcimentoItemVersoes.All(riv => riv.ItemVersao == null || !riv.ItemVersao.Divergencias.Any())
                //)) &&
                //(filtros.Condicao == null || (
                //    filtros.Condicao == true
                //        ? r.RessarcimentoItemVersoes.Any(riv => riv.ItemVersao != null && riv.ItemVersao.Condicoes.Any())
                //        : r.RessarcimentoItemVersoes.All(riv => riv.ItemVersao == null || !riv.ItemVersao.Condicoes.Any())
                );

            var ressarcimentos = await _ressarcimentoRepository.BuscarAsync(predicate);

            var concluidos = ressarcimentos.Select(r =>
            {
                var agregador = r.Agregador?.Numero;
                var fornecedor = (r.Fornecedor as PessoaJuridica)?.NomeFantasia ?? string.Empty;
                var dataAutorizacao = r.RessarcimentoItemVersoes.FirstOrDefault()?.DataAutorizacao ?? default;
                //var numeroDocumento = r.Agregador?.Documentos?.FirstOrDefault()?.Numero ?? r.Documento?.Numero ?? string.Empty;
                var valorTotal = r.Valor;
                var pago = 0m;
                var aPagar = valorTotal - pago;
                var ressarcir = r.Valor;
                //var ressarcido = r.LiquidacoesRessarcimento?.Sum(lr => lr.ValorLiquidado) ?? 0m;
                //var placa = (r.Ativo as Veiculo)?.Placa ?? string.Empty;
                //var statusVeiculo = (r.Ativo as Veiculo)?.StatusVeiculo;
                //var statusReparo = statusVeiculo != null ? new SituacaoDto<StatusVeiculo>(statusVeiculo.Value) : null;
                //var itemVersao = r.RessarcimentoItemVersoes.FirstOrDefault()?.ItemVersao;
                //var divergencia = itemVersao != null && itemVersao.Divergencias.Count() > 0;
                //var condicao = itemVersao != null && itemVersao.Condicoes.Count() > 0;

                //var possuiDivergencia = divergencia;
                //var possuiCondicao = condicao;

                return new ListagemDTO
                {
                    Agregador = agregador,
                    Fornecedor = fornecedor,
                    //DataAutorizacao = dataAutorizacao,
                    //NumeroDocumento = numeroDocumento,
                    //ValorTotal = valorTotal,
                    //APagar = aPagar,
                    //Pago = pago,
                    //Ressarcir = ressarcir,
                    //Ressarcido = ressarcido,
                    //Placa = placa,
                    //StatusReparo = statusReparo?.Descricao,
                    //Divergencia = possuiDivergencia,
                    //Condicao = possuiCondicao
                };
            }).ToList();

            return concluidos;
        }

        public async Task<(byte[] fileContents, string contentType, string fileName)?> DownloadComprovanteLiquidacaoRessarcimentoAsync(int liquidacaoId)
        {
            var liquidacao = await _liquidacaoRessarcimentoRepository.BuscarLiquidacaoComRessarcimentoEDocumentoAsync(liquidacaoId);

            if (liquidacao == null || liquidacao.Ressarcimento == null || !liquidacao.Ressarcimento.DocumentoRessarcimentos.Any())
            {
                return null;
            }

            var documentoRessarcimento = liquidacao.Ressarcimento.DocumentoRessarcimentos.FirstOrDefault();
            if (documentoRessarcimento == null || documentoRessarcimento.Documento == null || string.IsNullOrEmpty(documentoRessarcimento.Documento.CaminhoArquivo))
            {
                return null;
            }

            var filePath = documentoRessarcimento.Documento.CaminhoArquivo;

            if (!File.Exists(filePath))
            {
                return null;
            }

            var fileContents = await File.ReadAllBytesAsync(filePath);
            var contentType = "application/octet-stream";
            var fileName = Path.GetFileName(filePath);

            return (fileContents, contentType, fileName);
        }
    }
}
