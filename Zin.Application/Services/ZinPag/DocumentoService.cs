using AutoMapper;
using Microsoft.Extensions.Options;
using System.Text;
using Zin.Api.ConfigsMap;
using Zin.Application.DTOs.Documentos;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class DocumentoService(
        IDocumentoRepository documentoRepository,
        IDocumentoPagamentoRepository documentoPagamentoRepository,
        IPagamentoRepository pagamentoRepository,
        IAgregadorRepository agregadorRepository,
        IPessoaRepository pessoaRepository,
        IOptions<CaminhosConfig> caminhosOptions
    ) : IDocumentoService
    {
        private readonly IDocumentoRepository _documentoRepository = documentoRepository;
        private readonly IDocumentoPagamentoRepository _documentoPagamentoRepository = documentoPagamentoRepository;
        private readonly IPagamentoRepository _pagamentoRepository = pagamentoRepository;
        private readonly IAgregadorRepository _agregadorRepository = agregadorRepository;
        private readonly IPessoaRepository _pessoaRepository = pessoaRepository;
        private readonly CaminhosConfig _caminhosConfig = caminhosOptions.Value;


        public Task<bool> CancelarDocumentoAsync(int id)
        {
           throw new NotImplementedException();
        }

        public async Task<int> CriarNotaFiscalAsync(CriaNotaFiscalDTO dto)
        { 
            var tipos = new TipoDocumento[] { TipoDocumento.NotaFiscalDevolucao, TipoDocumento.NotaFiscalVenda };
            if (!tipos.Contains(dto.Tipo))
                throw new InvalidOperationException("Tipo de documento inválido para nota fiscal.");

            var documento = new Documento
            {
                TipoDocumento = dto.Tipo,
                Numero = dto.Numero,
                Serie = dto.Serie,
                DataEmissao = dto.DataEmissao,
                ValorTotal = dto.ValorTotal,
                Agregador = dto.Agregador,
                Emitente = dto.Fornecedor,
                Destinatario = dto.Cliente
            };

            var temArquivo = !string.IsNullOrEmpty(dto.Nome) 
                && !string.IsNullOrEmpty(dto.Base64);

            if (temArquivo)
            {
                var extensao = Path.GetExtension(dto.Nome);
                var relativePath = GetDocumentoRelativePath(dto.Cliente.Cnpj!, dto.Tipo, DateTime.UtcNow, dto.Numero, extensao);
                var fullPath = GetDocumentoFullPath(relativePath);

                var directory = Path.GetDirectoryName(fullPath);
                if (!Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                dto.SalvarArquivo(fullPath);

                documento.CaminhoArquivo = relativePath;
            }

            return await _documentoRepository.InserirAsync(documento);
        }

        public async Task<int> CriarDocumentoAsync(ItemVersao versao, CriaDocumentoDTO dto)
        { 
            var agregador = versao.Item.Agregador
                ?? throw new Exception("O item não possui agregador associado.");

            var emitente = versao.PessoaFornecedora 
                ?? throw new Exception("A versão do item não possui pessoa fornecedora associada.");

            var destinatario = agregador.ClienteEmpresa
                ?? throw new Exception("O agregador do item não possui pessoa cliente associada.");

            return await CriarDocumentoAsync
            (
                agregador, 
                dto, 
                emitente, 
                destinatario as Pessoa,
                agregador.ClienteEmpresa.Cnpj
            ); 
        }

        public async Task<int> CriarDocumentoAsync(CriaDocumentoDTO dto, string? cnpjCliente)
        {
            var agregador = await _agregadorRepository.BuscarPorNumeroAsync(dto.NumeroAgregador)
                 ?? throw new Exception("Agregador não encontrado.");

            if (dto.PessoaEmitente.Cnpj == null)
                throw new ArgumentNullException(nameof(dto.PessoaEmitente.Cnpj), "O CNPJ do emitente não pode ser nulo.");

            var emitente = await _pessoaRepository.BuscarPorDocumentoAsync(dto.PessoaEmitente.Cnpj)
                                 ?? throw new Exception("Pessoa emitente não encontrada.");

            var destinatario = await _pessoaRepository.BuscarPorDocumentoAsync(dto.CnpjPessoaDestinatario)
                                     ?? throw new Exception("Pessoa destinatário não encontrada.");

            return await CriarDocumentoAsync(agregador, dto, emitente, destinatario, cnpjCliente);  

        }

        private async Task<int> CriarDocumentoAsync
        (
            Agregador agregador, 
            CriaDocumentoDTO dto,
            Pessoa pessoaEmitente,
            Pessoa pessoaDestinatario,
            string? cnpjCliente
        )
        {
            if (dto.TipoDocumento != TipoDocumento.NotaFiscalVenda) return 0;

            var documento = new Documento
            {
                TipoDocumento = dto.TipoDocumento,
                Numero = dto.Numero,
                DataEmissao = dto.DataEmissao,
                ValorTotal = dto.ValorTotal,
                IdAgregador = agregador.Id,
                IdPessoaEmitente = pessoaEmitente.Id,
                IdPessoaDestinatario = pessoaDestinatario.Id,
            };

            if (dto.XmlFile != null)
            {
                var extensao = Path.GetExtension(dto.XmlFile.FileName);
                var relativePath = GetDocumentoRelativePath(cnpjCliente!, dto.TipoDocumento, DateTime.UtcNow, dto.Numero, extensao);
                var fullPath = GetDocumentoFullPath(relativePath);

                var directory = Path.GetDirectoryName(fullPath);
                if (!Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                using (var fileStream = new FileStream(fullPath, FileMode.Create))
                {
                    await dto.XmlFile.CopyToAsync(fileStream);
                }
                documento.CaminhoArquivo = relativePath;
            }

            await _documentoRepository.InserirAsync(documento);

            var pagamento = await _pagamentoRepository.BuscarPorIdAsync(dto.IdPagamento);
            if (pagamento != null)
            {
                if (pagamento.IdAgregador != agregador.Id)
                    throw new Exception("Pagamento não pertence ao agregador informado.");

                pagamento.StatusPagamento = pagamento.Valor > dto.ValorTotal ? StatusPagamento.PagoParcialmente : StatusPagamento.Pago;
                await _pagamentoRepository.AtualizarAsync(pagamento);

                var documentoPagamento = new DocumentoPagamento
                {
                    IdPagamento = pagamento.Id,
                    Documento = documento
                };
                await _documentoPagamentoRepository.InserirAsync(documentoPagamento);
                documento.DocumentoPagamento = documentoPagamento;
            }

            var notaFiscal = new NotaFiscal
            {
                TipoDocumento = TipoDocumento.NotaFiscalVenda,
                Numero = dto.Numero,
                DataEmissao = dto.DataEmissao,
                ValorTotal = dto.ValorTotal,
                IdAgregador = agregador.Id,
                IdPessoaEmitente = pessoaEmitente.Id,
                IdPessoaDestinatario = pessoaDestinatario.Id,
                Serie = dto.Serie,
                ValorICMS = dto.ValorICMS,
                ValorIPI = dto.ValorIPI
            };
            await _documentoRepository.InserirAsync(notaFiscal);

            return documento.Id;
        }

        public static string RemoverCaracteresEspeciais(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            var sb = new StringBuilder();
            foreach (char c in input)
            {
                if (char.IsLetterOrDigit(c) || char.IsWhiteSpace(c))
                    sb.Append(c);
            }
            return sb.ToString();
        }

        public async Task<DownloadDocumentoDto> DownloadDocumentoAsync(int documentoId)
        {
            var documento = await _documentoRepository.BuscarPorIdAsync(documentoId)
                            ?? throw new Exception("Documento não encontrado ou sem arquivo associado.");

            if (string.IsNullOrEmpty(documento.CaminhoArquivo))
                throw new Exception("Documento sem arquivo associado.");

            var fullPath = GetDocumentoFullPath(documento.CaminhoArquivo);

            if (!File.Exists(fullPath))
                throw new FileNotFoundException("O arquivo do documento não foi encontrado no servidor.", fullPath);

            var fileBytes = await File.ReadAllBytesAsync(fullPath);
            var contentType = GetContentType(fullPath);
            var fileName = Path.GetFileName(fullPath);

            return new DownloadDocumentoDto
            {
                FileBytes = fileBytes,
                ContentType = contentType,
                FileName = fileName
            };
        }

        private string GetDocumentoFullPath(string relativePath)
        {
            var basePath = _caminhosConfig.Documentos ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "XmlDocumentos");
            return Path.Combine(basePath, relativePath.TrimStart('/'));
        }

        private string GetDocumentoRelativePath(string cnpjCliente, TipoDocumento tipoDocumento, DateTime data, string numero, string extensao)
        {
            var cnpjLimpo = RemoverCaracteresEspeciais(cnpjCliente);
            var ano = data.Year.ToString();
            var mes = data.Month.ToString("D2");
            var dia = data.Day.ToString("D2");
            return $"/{cnpjLimpo}/{tipoDocumento}/{ano}/{mes}/{dia}/{numero}{extensao}";
        }

        private string GetContentType(string path)
        {
            var extension = Path.GetExtension(path).ToLowerInvariant();
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".xml" => "application/xml",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                _ => "application/octet-stream",
            };
        }
    }
}
