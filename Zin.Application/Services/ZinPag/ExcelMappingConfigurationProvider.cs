using Microsoft.Extensions.Options;
using Zin.Application.Configuration;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Application.Services.ZinPag;

public class ExcelMappingConfigurationProvider : IExcelMappingConfigurationProvider
{
    private readonly List<ExcelColumnMappingConfig> _mappings;

    public ExcelMappingConfigurationProvider(IOptions<List<ExcelColumnMappingConfig>> mappingsOptions)
    {
        _mappings = mappingsOptions.Value;
    }

    public ExcelColumnMappingConfig GetMappingConfig(string cnpjCliente)
    {
        var normalizedCnpj = cnpjCliente.Replace(".", "").Replace("/", "").Replace("-", "");
        var mapping = _mappings.FirstOrDefault(m => m.Cnpj == normalizedCnpj) 
                      ?? _mappings.FirstOrDefault(m => string.IsNullOrEmpty(m.Cnpj));

        return mapping is null
            ? throw new InvalidOperationException($"Nenhuma configuração de mapeamento do Excel foi encontrada para o CNPJ {cnpjCliente} ou como padrão.")
            : mapping;
    }
}