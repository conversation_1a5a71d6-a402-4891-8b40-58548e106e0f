using Microsoft.EntityFrameworkCore;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;

namespace Zin.Application.Services.ZinPag;

public class PagamentoConsultaService : IPagamentoConsultaService
{
    private readonly IPagamentoRepository _pagamentoRepository;
    private readonly ZinDbContext _context;

    public PagamentoConsultaService(IPagamentoRepository pagamentoRepository, ZinDbContext context)
    {
        _pagamentoRepository = pagamentoRepository;
        _context = context;
    }

    public async Task<PagamentoListagemResponseDTO> ListarPagamentosAsync(
        string idCliente,
        int page,
        int pageSize,
        StatusPagamento? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? cnpjBeneficiario = null,
        string? numeroNF = null)
    {
        var query = _context.Pagamentos
            .Include(p => p.Beneficiario)
            .Include(p => p.Agregador)
            .Include(p => p.LiquidacoesPagamentos)
            .Include(p => p.DocumentoPagamentos)
                .ThenInclude(dp => dp.Documento)
            .Where(p => p.Agregador != null && p.Agregador.IdCliente == idCliente);

        // Aplicar filtros
        if (status.HasValue)
            query = query.Where(p => p.StatusPagamento == status.Value);

        if (dataInicio.HasValue)
            query = query.Where(p => p.DataCriacao >= dataInicio.Value);

        if (dataFim.HasValue)
            query = query.Where(p => p.DataCriacao <= dataFim.Value);

        if (!string.IsNullOrEmpty(cnpjBeneficiario))
        {
            var cnpjNormalizado = cnpjBeneficiario.Replace(".", "").Replace("/", "").Replace("-", "");
            query = query.Where(p => p.Beneficiario != null && 
                p.Beneficiario.Cnpj.Replace(".", "").Replace("/", "").Replace("-", "").Contains(cnpjNormalizado));
        }

        if (!string.IsNullOrEmpty(numeroNF))
        {
            query = query.Where(p => p.DocumentoPagamentos.Any(dp => 
                dp.Documento != null && dp.Documento.NumeroDocumento.Contains(numeroNF)));
        }

        var totalRegistros = await query.CountAsync();
        var totalPaginas = (int)Math.Ceiling((double)totalRegistros / pageSize);

        var pagamentos = await query
            .OrderByDescending(p => p.DataCriacao)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var pagamentosDto = pagamentos.Select(p => new PagamentoListagemDTO
        {
            Id = p.Id,
            IdPessoaBeneficiaria = p.IdPessoaBeneficiaria,
            NomeBeneficiario = p.Beneficiario?.Nome,
            CnpjCpfBeneficiario = p.Beneficiario?.Cnpj,
            IdAgregador = p.IdAgregador,
            NumeroAgregador = p.Agregador?.Numero,
            StatusPagamento = p.StatusPagamento,
            DataCriacao = p.DataCriacao,
            DataAtualizacao = p.DataAtualizacao,
            DataPrevisao = p.DataPrevisao,
            Valor = p.Valor,
            FormaPagamento = p.FormaPagamento,
            Descricao = p.Descricao,
            Cancelado = p.Cancelado,
            ValorPago = p.LiquidacoesPagamentos.Sum(l => l.ValorPago),
            ValorPendente = p.Valor - p.LiquidacoesPagamentos.Sum(l => l.ValorPago),
            DataUltimaLiquidacao = p.LiquidacoesPagamentos.OrderByDescending(l => l.Data).FirstOrDefault()?.Data,
            QuantidadeLiquidacoes = p.LiquidacoesPagamentos.Count
        }).ToList();

        // Calcular resumo
        var resumo = new PagamentoResumoDTO
        {
            ValorTotalPendente = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pendente).Sum(p => p.Valor),
            ValorTotalPago = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pago).Sum(p => p.Valor),
            ValorTotalCancelado = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Cancelado).Sum(p => p.Valor),
            QuantidadePendente = pagamentos.Count(p => p.StatusPagamento == StatusPagamento.Pendente),
            QuantidadePago = pagamentos.Count(p => p.StatusPagamento == StatusPagamento.Pago),
            QuantidadeCancelado = pagamentos.Count(p => p.StatusPagamento == StatusPagamento.Cancelado)
        };

        return new PagamentoListagemResponseDTO
        {
            Pagamentos = pagamentosDto,
            TotalRegistros = totalRegistros,
            PaginaAtual = page,
            TamanhoPagina = pageSize,
            TotalPaginas = totalPaginas,
            Resumo = resumo
        };
    }

    public async Task<LoteImportacaoListagemResponseDTO> ListarLotesImportacaoAsync(
        string idCliente,
        int page,
        int pageSize,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null)
    {
        var query = _context.LotesImportacao
            .Where(l => l.IdCliente == idCliente);

        // Aplicar filtros
        if (status.HasValue)
            query = query.Where(l => l.Status == status.Value);

        if (dataInicio.HasValue)
            query = query.Where(l => l.DataProcessamento >= dataInicio.Value);

        if (dataFim.HasValue)
            query = query.Where(l => l.DataProcessamento <= dataFim.Value);

        if (!string.IsNullOrEmpty(usuario))
            query = query.Where(l => l.Usuario.Contains(usuario));

        var totalRegistros = await query.CountAsync();
        var totalPaginas = (int)Math.Ceiling((double)totalRegistros / pageSize);

        var lotes = await query
            .OrderByDescending(l => l.DataProcessamento)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var lotesDto = lotes.Select(l => new LoteImportacaoDTO
        {
            Id = l.Id,
            NomeArquivo = l.NomeArquivo,
            DataProcessamento = l.DataProcessamento,
            UsuarioProcessamento = l.Usuario,
            Status = l.Status,
            TotalLinhasLidas = l.TotalPagamentos,
            TotalProgramados = 0, // Será calculado conforme necessário
            TotalLiquidados = 0,  // Será calculado conforme necessário
            TotalCriados = l.TotalPagamentos,
            TotalIgnorados = 0,
            TotalDuplicados = 0,
            TotalErros = 0
        }).ToList();

        return new LoteImportacaoListagemResponseDTO
        {
            Lotes = lotesDto,
            TotalRegistros = totalRegistros,
            PaginaAtual = page,
            TamanhoPagina = pageSize,
            TotalPaginas = totalPaginas
        };
    }

    public async Task<LoteImportacaoDetalheDTO?> ObterDetalhesLoteAsync(int loteId, string idCliente)
    {
        var lote = await _context.LotesImportacao
            .FirstOrDefaultAsync(l => l.Id == loteId && l.IdCliente == idCliente);

        if (lote == null) return null;

        return new LoteImportacaoDetalheDTO
        {
            Id = lote.Id,
            NomeArquivo = lote.NomeArquivo,
            DataProcessamento = lote.DataProcessamento,
            UsuarioProcessamento = lote.Usuario,
            Status = lote.Status,
            TotalLinhasLidas = lote.TotalPagamentos,
            TotalProgramados = 0, // Pode ser calculado se necessário
            TotalLiquidados = 0,  // Pode ser calculado se necessário
            TotalCriados = lote.TotalPagamentos,
            TotalIgnorados = 0,
            TotalDuplicados = 0,
            TotalErros = 0,
            TipoArquivo = "xlsx", // Assumindo Excel por padrão
            TamanhoArquivo = 0    // Pode ser adicionado à entidade se necessário
        };
    }

    public async Task<LinhaImportacaoListagemResponseDTO> ListarLinhasLoteAsync(
        int loteId,
        string idCliente,
        int page,
        int pageSize,
        StatusLinhaPagamento? status = null,
        OperacaoLinhaPagamento? operacao = null)
    {
        // Verificar se o lote pertence ao cliente
        var loteInfo = await ObterDetalhesLoteAsync(loteId, idCliente);
        if (loteInfo == null)
        {
            return new LinhaImportacaoListagemResponseDTO
            {
                Linhas = new List<LinhaImportacaoDTO>(),
                TotalRegistros = 0,
                PaginaAtual = page,
                TamanhoPagina = pageSize,
                TotalPaginas = 0,
                LoteInfo = new LoteImportacaoDetalheDTO()
            };
        }

        // Buscar pagamentos do lote usando JOIN
        var query = _context.Pagamentos
            .Join(_context.PagamentosLotesImportacao,
                  p => p.Id,
                  pl => pl.IdPagamento,
                  (p, pl) => new { Pagamento = p, PagamentoLote = pl })
            .Where(x => x.PagamentoLote.IdLote == loteId)
            .Include(x => x.Pagamento.Beneficiario)
            .Include(x => x.Pagamento.DocumentoPagamentos)
                .ThenInclude(dp => dp.Documento);

        var totalRegistros = await query.CountAsync();
        var totalPaginas = (int)Math.Ceiling((double)totalRegistros / pageSize);

        var pagamentosComLote = await query
            .OrderBy(x => x.PagamentoLote.NumeroLinha ?? 0)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var linhasDto = pagamentosComLote.Select(x => new LinhaImportacaoDTO
        {
            Id = x.Pagamento.Id,
            NumeroLinha = x.PagamentoLote.NumeroLinha ?? 0,
            CnpjPagador = "", // Pode ser obtido do agregador se necessário
            CnpjCpfFavorecido = x.Pagamento.Beneficiario?.Cnpj,
            NumeroNF = x.Pagamento.DocumentoPagamentos.FirstOrDefault()?.Documento?.Numero,
            DataProgramacao = x.Pagamento.DataPrevisao,
            DataLiquidacao = x.Pagamento.StatusPagamento == StatusPagamento.Pago ? x.Pagamento.DataAtualizacao : null,
            ValorPago = x.Pagamento.Valor,
            OperacaoAplicada = x.Pagamento.StatusPagamento == StatusPagamento.Pago ? OperacaoLinhaPagamento.Liquidar : OperacaoLinhaPagamento.Programar,
            Status = StatusLinhaPagamento.OK,
            Mensagem = "Processado com sucesso",
            IdPagamento = x.Pagamento.Id,
            PagamentoEncontrado = true,
            PagamentoCriado = true
        }).ToList();

        return new LinhaImportacaoListagemResponseDTO
        {
            Linhas = linhasDto,
            TotalRegistros = totalRegistros,
            PaginaAtual = page,
            TamanhoPagina = pageSize,
            TotalPaginas = totalPaginas,
            LoteInfo = loteInfo
        };
    }

    public async Task<(byte[] fileContents, string contentType, string fileName)?> DownloadArquivoLoteAsync(int loteId, string idCliente)
    {
        // Por enquanto retorna null - pode ser implementado posteriormente se necessário armazenar o arquivo original
        var lote = await _context.LotesImportacao
            .FirstOrDefaultAsync(l => l.Id == loteId && l.IdCliente == idCliente);

        if (lote == null) return null;

        // Retorna null por enquanto - implementar se necessário armazenar arquivos
        return null;
    }
}
