using Microsoft.EntityFrameworkCore;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;

namespace Zin.Application.Services.ZinPag;

public class PagamentoConsultaService : IPagamentoConsultaService
{
    private readonly IPagamentoRepository _pagamentoRepository;
    private readonly ILoteImportacaoPagamentoRepository _loteRepository;
    private readonly ILinhaImportacaoPagamentoRepository _linhaRepository;
    private readonly ZinDbContext _context;

    public PagamentoConsultaService(
        IPagamentoRepository pagamentoRepository,
        ILoteImportacaoPagamentoRepository loteRepository,
        ILinhaImportacaoPagamentoRepository linhaRepository,
        ZinDbContext context)
    {
        _pagamentoRepository = pagamentoRepository;
        _loteRepository = loteRepository;
        _linhaRepository = linhaRepository;
        _context = context;
    }

    public async Task<PagamentoListagemResponseDTO> ListarPagamentosAsync(
        string idCliente,
        int page,
        int pageSize,
        StatusPagamento? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? cnpjBeneficiario = null,
        string? numeroNF = null)
    {
        var query = _context.Pagamentos
            .Include(p => p.Beneficiario)
            .Include(p => p.Agregador)
            .Include(p => p.LiquidacoesPagamentos)
            .Include(p => p.DocumentoPagamentos)
                .ThenInclude(dp => dp.Documento)
            .Where(p => p.Agregador != null && p.Agregador.IdCliente == idCliente);

        // Aplicar filtros
        if (status.HasValue)
            query = query.Where(p => p.StatusPagamento == status.Value);

        if (dataInicio.HasValue)
            query = query.Where(p => p.DataCriacao >= dataInicio.Value);

        if (dataFim.HasValue)
            query = query.Where(p => p.DataCriacao <= dataFim.Value);

        if (!string.IsNullOrEmpty(cnpjBeneficiario))
        {
            var cnpjNormalizado = cnpjBeneficiario.Replace(".", "").Replace("/", "").Replace("-", "");
            query = query.Where(p => p.Beneficiario != null && 
                p.Beneficiario.Cnpj.Replace(".", "").Replace("/", "").Replace("-", "").Contains(cnpjNormalizado));
        }

        if (!string.IsNullOrEmpty(numeroNF))
        {
            query = query.Where(p => p.DocumentoPagamentos.Any(dp => 
                dp.Documento != null && dp.Documento.NumeroDocumento.Contains(numeroNF)));
        }

        var totalRegistros = await query.CountAsync();
        var totalPaginas = (int)Math.Ceiling((double)totalRegistros / pageSize);

        var pagamentos = await query
            .OrderByDescending(p => p.DataCriacao)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var pagamentosDto = pagamentos.Select(p => new PagamentoListagemDTO
        {
            Id = p.Id,
            IdPessoaBeneficiaria = p.IdPessoaBeneficiaria,
            NomeBeneficiario = p.Beneficiario?.Nome,
            CnpjCpfBeneficiario = p.Beneficiario?.Cnpj,
            IdAgregador = p.IdAgregador,
            NumeroAgregador = p.Agregador?.Numero,
            StatusPagamento = p.StatusPagamento,
            DataCriacao = p.DataCriacao,
            DataAtualizacao = p.DataAtualizacao,
            DataPrevisao = p.DataPrevisao,
            Valor = p.Valor,
            FormaPagamento = p.FormaPagamento,
            Descricao = p.Descricao,
            Cancelado = p.Cancelado,
            ValorPago = p.LiquidacoesPagamentos.Sum(l => l.ValorPago),
            ValorPendente = p.Valor - p.LiquidacoesPagamentos.Sum(l => l.ValorPago),
            DataUltimaLiquidacao = p.LiquidacoesPagamentos.OrderByDescending(l => l.Data).FirstOrDefault()?.Data,
            QuantidadeLiquidacoes = p.LiquidacoesPagamentos.Count
        }).ToList();

        // Calcular resumo
        var resumo = new PagamentoResumoDTO
        {
            ValorTotalPendente = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pendente).Sum(p => p.Valor),
            ValorTotalPago = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pago).Sum(p => p.Valor),
            ValorTotalCancelado = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Cancelado).Sum(p => p.Valor),
            QuantidadePendente = pagamentos.Count(p => p.StatusPagamento == StatusPagamento.Pendente),
            QuantidadePago = pagamentos.Count(p => p.StatusPagamento == StatusPagamento.Pago),
            QuantidadeCancelado = pagamentos.Count(p => p.StatusPagamento == StatusPagamento.Cancelado)
        };

        return new PagamentoListagemResponseDTO
        {
            Pagamentos = pagamentosDto,
            TotalRegistros = totalRegistros,
            PaginaAtual = page,
            TamanhoPagina = pageSize,
            TotalPaginas = totalPaginas,
            Resumo = resumo
        };
    }

    public async Task<LoteImportacaoListagemResponseDTO> ListarLotesImportacaoAsync(
        string idCliente,
        int page,
        int pageSize,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null)
    {
        var totalRegistros = await _loteRepository.ContarPorClienteAsync(idCliente, status, dataInicio, dataFim, usuario);
        var totalPaginas = (int)Math.Ceiling((double)totalRegistros / pageSize);

        var lotes = await _loteRepository.BuscarPaginadoPorClienteAsync(
            idCliente, page, pageSize, status, dataInicio, dataFim, usuario);

        var lotesDto = lotes.Select(l => new LoteImportacaoDTO
        {
            Id = l.Id,
            NomeArquivo = l.NomeArquivo,
            DataProcessamento = l.DataProcessamento,
            UsuarioProcessamento = l.UsuarioProcessamento,
            Status = l.Status,
            TotalLinhasLidas = l.TotalLinhasLidas,
            TotalProgramados = l.TotalProgramados,
            TotalLiquidados = l.TotalLiquidados,
            TotalCriados = l.TotalCriados,
            TotalIgnorados = l.TotalIgnorados,
            TotalDuplicados = l.TotalDuplicados,
            TotalErros = l.TotalErros
        }).ToList();

        return new LoteImportacaoListagemResponseDTO
        {
            Lotes = lotesDto,
            TotalRegistros = totalRegistros,
            PaginaAtual = page,
            TamanhoPagina = pageSize,
            TotalPaginas = totalPaginas
        };
    }

    public async Task<LoteImportacaoDetalheDTO?> ObterDetalhesLoteAsync(int loteId, string idCliente)
    {
        var lote = await _loteRepository.BuscarComLinhasPorIdAsync(loteId, idCliente);
        if (lote == null) return null;

        return new LoteImportacaoDetalheDTO
        {
            Id = lote.Id,
            NomeArquivo = lote.NomeArquivo,
            DataProcessamento = lote.DataProcessamento,
            DataInicio = lote.DataInicio,
            DataFim = lote.DataFim,
            UsuarioProcessamento = lote.UsuarioProcessamento,
            Status = lote.Status,
            TotalLinhasLidas = lote.TotalLinhasLidas,
            TotalProgramados = lote.TotalProgramados,
            TotalLiquidados = lote.TotalLiquidados,
            TotalCriados = lote.TotalCriados,
            TotalIgnorados = lote.TotalIgnorados,
            TotalDuplicados = lote.TotalDuplicados,
            TotalErros = lote.TotalErros,
            TipoArquivo = lote.TipoArquivo,
            TamanhoArquivo = lote.TamanhoArquivo
        };
    }

    public async Task<LinhaImportacaoListagemResponseDTO> ListarLinhasLoteAsync(
        int loteId,
        string idCliente,
        int page,
        int pageSize,
        StatusLinhaPagamento? status = null,
        OperacaoLinhaPagamento? operacao = null)
    {
        // Verificar se o lote pertence ao cliente
        var loteInfo = await ObterDetalhesLoteAsync(loteId, idCliente);
        if (loteInfo == null)
        {
            return new LinhaImportacaoListagemResponseDTO
            {
                Linhas = new List<LinhaImportacaoDTO>(),
                TotalRegistros = 0,
                PaginaAtual = page,
                TamanhoPagina = pageSize,
                TotalPaginas = 0,
                LoteInfo = new LoteImportacaoDetalheDTO()
            };
        }

        var totalRegistros = await _linhaRepository.ContarPorLoteAsync(loteId, status, operacao);
        var totalPaginas = (int)Math.Ceiling((double)totalRegistros / pageSize);

        var linhas = await _linhaRepository.BuscarPaginadoPorLoteAsync(
            loteId, page, pageSize, status, operacao);

        var linhasDto = linhas.Select(l => new LinhaImportacaoDTO
        {
            Id = l.Id,
            NumeroLinha = l.NumeroLinha,
            CnpjPagador = l.CnpjPagador,
            CnpjCpfFavorecido = l.CnpjCpfFavorecido,
            NumeroNF = l.NumeroNF,
            DataProgramacao = l.DataProgramacao,
            DataLiquidacao = l.DataLiquidacao,
            ValorPago = l.ValorPago,
            OperacaoAplicada = l.OperacaoAplicada,
            Status = l.Status,
            Mensagem = l.Mensagem,
            IdPagamento = l.IdPagamento,
            PagamentoEncontrado = l.PagamentoEncontrado,
            PagamentoCriado = l.PagamentoCriado
        }).ToList();

        return new LinhaImportacaoListagemResponseDTO
        {
            Linhas = linhasDto,
            TotalRegistros = totalRegistros,
            PaginaAtual = page,
            TamanhoPagina = pageSize,
            TotalPaginas = totalPaginas,
            LoteInfo = loteInfo
        };
    }

    public async Task<(byte[] fileContents, string contentType, string fileName)?> DownloadArquivoLoteAsync(int loteId, string idCliente)
    {
        var lote = await _loteRepository.BuscarComLinhasPorIdAsync(loteId, idCliente);
        if (lote?.ArquivoOriginal == null) return null;

        var contentType = lote.TipoArquivo switch
        {
            "xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "xls" => "application/vnd.ms-excel",
            _ => "application/octet-stream"
        };

        return (lote.ArquivoOriginal, contentType, lote.NomeArquivo);
    }
}
