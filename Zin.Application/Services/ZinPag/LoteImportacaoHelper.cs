using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;
using Zin.Infrastructure.Dados;

namespace Zin.Application.Services.ZinPag;

/// <summary>
/// Helper para gerenciar lotes de importação de pagamentos
/// </summary>
public static class LoteImportacaoHelper
{
    /// <summary>
    /// Cria um novo lote de importação
    /// </summary>
    public static async Task<int> CriarLoteAsync(
        ZinDbContext context,
        string idCliente,
        string nomeArquivo,
        string usuario)
    {
        var lote = new LoteImportacao
        {
            IdCliente = idCliente,
            NomeArquivo = nomeArquivo,
            DataProcessamento = DateTime.UtcNow,
            Usuario = usuario,
            Status = StatusLoteImportacao.Processando,
            TotalPagamentos = 0,
            ValorTotal = 0
        };

        context.LotesImportacao.Add(lote);
        await context.SaveChangesAsync();
        
        return lote.Id;
    }

    /// <summary>
    /// Associa um pagamento a um lote de importação
    /// </summary>
    public static async Task AssociarPagamentoAoLoteAsync(
        ZinDbContext context,
        int idPagamento,
        int idLote,
        int? numeroLinha = null)
    {
        var pagamentoLote = new PagamentoLoteImportacao
        {
            IdPagamento = idPagamento,
            IdLote = idLote,
            NumeroLinha = numeroLinha,
            DataProcessamento = DateTime.UtcNow
        };

        context.PagamentosLotesImportacao.Add(pagamentoLote);
        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Finaliza um lote de importação atualizando os totais
    /// </summary>
    public static async Task FinalizarLoteAsync(
        ZinDbContext context,
        int idLote,
        StatusLoteImportacao status = StatusLoteImportacao.Concluido)
    {
        var lote = await context.LotesImportacao.FindAsync(idLote);
        if (lote == null) return;

        // Calcular totais baseado nos pagamentos associados
        var pagamentosDoLote = await context.PagamentosLotesImportacao
            .Where(pl => pl.IdLote == idLote)
            .Join(context.Pagamentos,
                  pl => pl.IdPagamento,
                  p => p.Id,
                  (pl, p) => p)
            .ToListAsync();

        lote.TotalPagamentos = pagamentosDoLote.Count;
        lote.ValorTotal = pagamentosDoLote.Sum(p => p.Valor);
        lote.Status = status;

        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Exemplo de uso durante a importação de Excel
    /// </summary>
    public static async Task<int> ExemploUsoNaImportacaoAsync(
        ZinDbContext context,
        string idCliente,
        string nomeArquivo,
        string usuario,
        List<int> idsPagamentosCriados)
    {
        // 1. Criar o lote
        var idLote = await CriarLoteAsync(context, idCliente, nomeArquivo, usuario);

        // 2. Associar cada pagamento ao lote
        for (int i = 0; i < idsPagamentosCriados.Count; i++)
        {
            await AssociarPagamentoAoLoteAsync(
                context, 
                idsPagamentosCriados[i], 
                idLote, 
                i + 2); // +2 porque linha 1 é cabeçalho, dados começam na linha 2
        }

        // 3. Finalizar o lote
        await FinalizarLoteAsync(context, idLote);

        return idLote;
    }
}
