using AutoMapper;
using Zin.Application.DTOs.Documentos;
using Zin.Application.DTOs.Movimentacoes;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class MovimentacoesService(
        IAgregadorRepository agregadorRepository,
        IDocumentoRepository documentoRepository,
        IItemRepository itemRepository,
        IItemVersaoRepository itemVersaoRepository,
        IMovimentacoesRepository movimentacoesRepository,
        IPagamentoRepository pagamentoRepository,
        IRessarcimentoRepository ressarcimentoRepository,
        IRegistroProcessamentoRepository registroProcessamentoRepository,
        IMapper mapper) : IMovimentacoesService
    {
        private readonly IAgregadorRepository _agregadorRepository = agregadorRepository;
        private readonly IDocumentoRepository _documentoRepository = documentoRepository;
        private readonly IItemRepository _itemRepository = itemRepository;
        private readonly IItemVersaoRepository _itemVersaoRepository = itemVersaoRepository;
        private readonly IMovimentacoesRepository _movimentacoesRepository = movimentacoesRepository;
        private readonly IPagamentoRepository _pagamentoRepository = pagamentoRepository;
        private readonly IRessarcimentoRepository _ressarcimentoRepository = ressarcimentoRepository;
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepository = registroProcessamentoRepository;
        private readonly IMapper _mapper = mapper;


        public async Task<List<Movimentacao>> ObterOuAtualizarMovimentacoesAsync(List<Item> itensDaImportacao)
        {
            var movimentacoes = new List<Movimentacao>();

            movimentacoes.AddRange(await ProcessarMovimentacoesComDocumento(itensDaImportacao));
            movimentacoes.AddRange(await ProcessarMovimentacoesSemDocumento(itensDaImportacao));
            // Adicione aqui a chamada do método de processamento de cancelamento, caso implemente futuramente.

            return movimentacoes;
        }

        private async Task<List<Movimentacao>> ProcessarMovimentacoesComDocumento(List<Item> itensDaImportacao)
        {
            var movimentacoes = new List<Movimentacao>();

            var itensComDocumento = itensDaImportacao
                .SelectMany(i => i.Versoes
                    .Where(v => v.IdDocumento.HasValue)
                    .Select(v => new { Item = i, Versao = v, IdDocumento = v.IdDocumento!.Value }))
                .GroupBy(x => new { x.IdDocumento, TipoItemVersao = x.Versao.TipoItemVersao })
                .ToList();

            foreach (var grupo in itensComDocumento)
            {
                var versoes = grupo.Select(x => x.Versao).ToList();

                foreach (var v in versoes)
                {
                    await InativarVinculosPorData(v.Id);
                }

                var movimentacaoExistente = await movimentacoesRepository
                    .BuscarAsync(m =>
                        m.Documentos.Any(d => d.Id == grupo.Key.IdDocumento)
                        && m.Ativo
                        && m.TipoMovimentacao == grupo.Key.TipoItemVersao);

                Movimentacao movimentacao;
                if (movimentacaoExistente != null)
                {
                    AtualizarOuCriarVinculos(versoes, movimentacaoExistente);
                    movimentacaoExistente.Ativo = true;
                    AtualizarValoresMovimentacao(movimentacaoExistente);
                    await movimentacoesRepository.AtualizarAsync(movimentacaoExistente);
                    movimentacao = movimentacaoExistente;
                }
                else
                {
                    movimentacao = CriarMovimentacaoPorDocumento(grupo, versoes);
                    AtualizarValoresMovimentacao(movimentacao);
                    await movimentacoesRepository.InserirAsync(movimentacao);
                }
                movimentacoes.Add(movimentacao);
            }
            return movimentacoes;
        }

        private async Task<List<Movimentacao>> ProcessarMovimentacoesSemDocumento(List<Item> itensDaImportacao)
        {
            var movimentacoes = new List<Movimentacao>();

            var itensSemDocumento = itensDaImportacao
                .SelectMany(i => i.Versoes
                    .Where(v => !v.IdDocumento.HasValue)
                    .Select(v => new { Item = i, Versao = v, Data = v.DataHoraAutorizacao?.Date }))
                .GroupBy(x => new { x.Data, TipoItemVersao = x.Versao.TipoItemVersao })
                .ToList();

            foreach (var grupo in itensSemDocumento)
            {
                var versoes = grupo.Select(x => x.Versao).ToList();

                var movimentacaoExistente = await movimentacoesRepository
                    .BuscarAsync(m =>
                        m.DataHoraAutorizacaoItem == grupo.Key.Data
                        && m.Documentos.Count == 0
                        && m.Ativo
                        && m.TipoMovimentacao == grupo.Key.TipoItemVersao);

                Movimentacao movimentacao;
                if (movimentacaoExistente != null)
                {
                    AtualizarOuCriarVinculos(versoes, movimentacaoExistente);
                    movimentacaoExistente.Ativo = movimentacaoExistente.MovimentacoesItensVersoes.Any(iv => iv.Ativo);
                    AtualizarValoresMovimentacao(movimentacaoExistente);
                    await movimentacoesRepository.AtualizarAsync(movimentacaoExistente);
                    movimentacao = movimentacaoExistente;
                }
                else
                {
                    movimentacao = CriarMovimentacaoPorData(grupo, versoes);
                    AtualizarValoresMovimentacao(movimentacao);
                    await movimentacoesRepository.InserirAsync(movimentacao);
                }
                movimentacoes.Add(movimentacao);
            }
            return movimentacoes;
        }

        private async Task InativarVinculosPorData(int idItemVersao)
        {
            var movsData = await movimentacoesRepository.BuscarMovimentacoesPorItemVersaoEAgrupamentoAsync(
                idItemVersao, agrupamentoPorData: true);

            foreach (var movData in movsData)
            {
                var vinculo = movData.MovimentacoesItensVersoes.FirstOrDefault(iv => iv.IdItemVersao == idItemVersao && iv.Ativo);
                if (vinculo != null)
                {
                    vinculo.Ativo = false;
                    vinculo.DataDesvinculo = DateTime.UtcNow;
                    if (movData.MovimentacoesItensVersoes.All(iv => !iv.Ativo))
                        movData.Ativo = false;
                    AtualizarValoresMovimentacao(movData);
                    await movimentacoesRepository.AtualizarAsync(movData);
                }
            }
        }

        private void AtualizarOuCriarVinculos(List<ItemVersao> versoes, Movimentacao movimentacao)
        {
            foreach (var v in versoes)
            {
                var vinculo = movimentacao.MovimentacoesItensVersoes.FirstOrDefault(iv => iv.IdItemVersao == v.Id);
                if (vinculo == null)
                {
                    movimentacao.MovimentacoesItensVersoes.Add(new MovimentacaoItemVersao
                    {
                        IdItemVersao = v.Id,
                        Ativo = true,
                        DataVinculo = DateTime.UtcNow
                    });
                }
                else
                {
                    vinculo.Ativo = true;
                    vinculo.DataDesvinculo = null;
                }
            }
        }

        private Movimentacao CriarMovimentacaoPorDocumento(IGrouping<dynamic, dynamic> grupo, List<ItemVersao> versoes)
        {
            return new Movimentacao
            {
                IdAgregador = grupo.First().Item.IdAgregador,
                Documentos = [grupo.First().Versao.Documento!],
                DataHoraAutorizacaoItem = grupo.Min(x => x.Versao.DataHoraAutorizacao),
                Ativo = true,
                TipoMovimentacao = grupo.Key.TipoItemVersao,
                MovimentacoesItensVersoes = versoes.Select(v => new MovimentacaoItemVersao
                {
                    IdItemVersao = v.Id,
                    Ativo = true,
                    DataVinculo = DateTime.UtcNow
                }).ToList()
            };
        }

        private Movimentacao CriarMovimentacaoPorData(IGrouping<dynamic, dynamic> grupo, List<ItemVersao> versoes)
        {
            return new Movimentacao
            {
                IdAgregador = grupo.First().Item.IdAgregador,
                DataHoraAutorizacaoItem = grupo.Key.Data,
                Ativo = true,
                TipoMovimentacao = grupo.Key.TipoItemVersao,
                MovimentacoesItensVersoes = versoes.Select(v => new MovimentacaoItemVersao
                {
                    IdItemVersao = v.Id,
                    Ativo = true,
                    DataVinculo = DateTime.UtcNow,
                    ItemVersao = v
                }).ToList()
            };
        }

        /// <summary>
        /// Atualiza os valores totais da movimentação conforme os itens ativos.
        /// </summary>
        private void AtualizarValoresMovimentacao(Movimentacao movimentacao)
        {
            var itensAtivos = movimentacao.MovimentacoesItensVersoes
                .Where(iv => iv.Ativo)
                .Select(iv => iv.ItemVersao)
                .Where(x => x != null)
                .ToList();

            movimentacao.ValorTotalAPagar = CalcularValorAPagar(itensAtivos);
            movimentacao.ValorTotalPago = CalcularValorPago(itensAtivos);
            movimentacao.ValorTotalARessarcir = CalcularValorARessarcir(itensAtivos);
            movimentacao.ValorTotalRessarcido = CalcularValorRessarcido(itensAtivos);
        }

        public static decimal CalcularValorAPagar(List<ItemVersao> itensVersao)
        {
            decimal valorTotalAutorizado = 0;
            decimal valorTotalPago = 0;

            // 1. Filtra apenas versões de AUTORIZAÇÃO que não foram excluídas
#warning NÃO USAR IDVERSAOANTERIOR
            var versoesAutorizadasNaoExcluidas = itensVersao
                .Where(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao)
                .Where(autorizada =>
                    !itensVersao.Any(e =>
                        e.TipoMovimento == TipoMovimentoItem.Exclusao &&
                        e.IdVersaoAnterior == autorizada.Id
                    )
                ).ToList();

            // 2. Soma o valor aprovado dessas versões
            valorTotalAutorizado = versoesAutorizadasNaoExcluidas.Sum(v => v.ValorTotal);

            // 3. Soma todos os pagamentos realizados sobre todas as versões
            valorTotalPago = itensVersao
                .SelectMany(v => v.Pagamentos ?? new List<PagamentoItemVersao>())
                .Sum(p => p.Pagamento?.Valor ?? 0);

            // 4. Retorna o valor a pagar
            return valorTotalAutorizado - valorTotalPago;
        }

        public static decimal CalcularValorPago(List<ItemVersao> itensVersao)
        {
            // Soma todos os pagamentos realizados nas versões informadas
            return itensVersao
                .SelectMany(v => v.Pagamentos ?? new List<PagamentoItemVersao>())
                .Sum(p => p.Pagamento?.Valor ?? 0);
        }

        public static decimal CalcularValorARessarcir(List<ItemVersao> itensVersao)
        {
            // Soma de todos os valores de exclusão
            var valorTotalExclusao = itensVersao
                .Where(v => v.TipoMovimento == TipoMovimentoItem.Exclusao)
                .Sum(v => v.ValorTotal);

            // Soma de todos os valores já ressarcidos para versões de exclusão
            var valorTotalRessarcido = itensVersao
                .Where(v => v.TipoMovimento == TipoMovimentoItem.Exclusao)
                .SelectMany(v => v.Ressarcimentos ?? new List<RessarcimentoItemVersao>())
                .Sum(r => r.Ressarcimento?.Valor ?? 0);

            // Valor a ressarcir = total de exclusão - já ressarcido
            return valorTotalExclusao - valorTotalRessarcido;
        }

        public static decimal CalcularValorRessarcido(List<ItemVersao> itensVersao)
        {
            // Soma todos os valores já ressarcidos nas versões informadas
            return itensVersao  
                .SelectMany(v => v.Ressarcimentos ?? new List<RessarcimentoItemVersao>())
                .Sum(r => r.Ressarcimento?.Valor ?? 0);
        }

        public async Task<List<MovimentacaoDto>> ListarMovimentacoesAsync()
        {
            var movimentacoes = await _movimentacoesRepository.BuscarVariosAsync(m => m.Ativo);

            return _mapper.Map<List<MovimentacaoDto>>(movimentacoes);
        }

        public async Task<List<ItemAgregadorDto>> ObterItensOutrasMovimentacoesAsync(int idAgregador, Guid movId)
        {
            var existemMovimentacoes = await _movimentacoesRepository
                .ExistemMovimentacoesPorAgregadorExcluindoMovimentacaoAsync(idAgregador, movId);

            if (!existemMovimentacoes)
                return [];

            var itensComVersoesPorAgregadorExcluindoMovimentacao = await _itemRepository
                .BuscarItensComVersoesPorAgregadorExcluindoMovimentacaoAsync(idAgregador, movId);

            if (!itensComVersoesPorAgregadorExcluindoMovimentacao.Any())
                return [];

            var result = new List<ItemAgregadorDto>();

            var movimentacao = await _movimentacoesRepository.BuscarDetalhesMovimentacaoAsync(movId);

            foreach (var item in itensComVersoesPorAgregadorExcluindoMovimentacao)
            {
                var versoesValidas = item.Versoes
                    .Where(v => !movimentacao!.MovimentacoesItensVersoes.Any(miv => miv.IdItemVersao == v.Id)).ToList();

                if (!versoesValidas.Any())
                    continue;

                var valorAtualizado = CalcularValorAtualizado(versoesValidas);

                var versaoIds = versoesValidas.Select(v => v.Id).ToList();

                var temDivergencias = await _registroProcessamentoRepository
                    .ExistemDivergenciasPorItensVersaoAsync(versaoIds);

                var versoesDto = new List<VersaoAgregadorDto>();

                foreach (var versao in versoesValidas)
                {
                    var nomeFornecedor = ObterNomeDoFornecedor(versao.PessoaFornecedora);

                    var documentos = ObterDocumentosDaVersao(versao.Documento);

                    var registrosProcessamento = await _registroProcessamentoRepository
                        .BuscarPorItensVersaoAsync(new[] { versao.Id });

                    var divergenciasVersao = ObterDivergencias(registrosProcessamento);

                    versoesDto.Add(new VersaoAgregadorDto
                    {
                        IdItemVersao = versao.Id,
                        Fornecedor = new FornecedorAgregadorDto { Nome = nomeFornecedor ?? string.Empty },
                        TipoMovimento = versao.TipoMovimento == TipoMovimentoItem.Autorizacao ? "Autorizacao" : "Exclusao",
                        DataAutorizacao = versao.DataHoraAutorizacao,
                        Documentos = documentos,
                        Quantidade = versao.Quantidade,
                        ValorTotal = versao.ValorTotal,
                        DataEntrega = versao.DataEntrega,
                        Divergencias = divergenciasVersao
                    });
                }

                result.Add(new ItemAgregadorDto
                {
                    IdItem = item.Id,
                    Codigo = item.Codigo,
                    Descricao = item.Descricao,
                    ValorAtualizado = valorAtualizado,
                    Divergencias = temDivergencias,
                    Versoes = versoesDto
                });
            }

            return result;
        }

        public async Task<List<ItemAgregadorDto>> ObterItensMesmaMovimentacaoAsync(int idAgregador, Guid movId)
        {
            var itensComVersoesPorAgregadorMesmaMovimentacao = await _itemRepository
                .BuscarItensComVersoesPorAgregadorMesmaMovimentacaoAsync(idAgregador, movId);

            if (!itensComVersoesPorAgregadorMesmaMovimentacao.Any())
                return [];

            var result = new List<ItemAgregadorDto>();

            var movimentacao = await _movimentacoesRepository.BuscarDetalhesMovimentacaoAsync(movId);

            foreach (var item in itensComVersoesPorAgregadorMesmaMovimentacao)
            {
                var versoesValidas = item.Versoes
                    .Where(v => movimentacao!.MovimentacoesItensVersoes.Any(miv => miv.IdItemVersao == v.Id))
                    .ToList();

                if (!versoesValidas.Any())
                    continue;

                var valorAtualizado = CalcularValorAtualizado(versoesValidas);
                var versaoIds = versoesValidas.Select(v => v.Id).ToList();

                var temDivergencias = await _registroProcessamentoRepository
                    .ExistemDivergenciasPorItensVersaoAsync(versaoIds);

                var registrosDeTodasVersoes = await _registroProcessamentoRepository
                    .BuscarPorItensVersaoAsync(versaoIds);

                var versoesDto = new List<VersaoAgregadorDto>();

                foreach (var versao in versoesValidas)
                {
                    var nomeFornecedor = ObterNomeDoFornecedor(versao.PessoaFornecedora);
                    var documentos = ObterDocumentosDaVersao(versao.Documento);

                    var registrosProcessamento = registrosDeTodasVersoes
                        .Where(r => r.IdItemVersao == versao.Id);

                    var divergenciasVersao = ObterDivergencias(registrosProcessamento);
                    var condicoesVersao = ObterCondicoes(registrosProcessamento);

                    versoesDto.Add(new VersaoAgregadorDto
                    {
                        IdItemVersao = versao.Id,
                        Fornecedor = new FornecedorAgregadorDto { Nome = nomeFornecedor ?? string.Empty },
                        TipoMovimento = versao.TipoMovimento == TipoMovimentoItem.Autorizacao ? "Autorizacao" : "Exclusao",
                        DataAutorizacao = versao.DataHoraAutorizacao,
                        Documentos = documentos,
                        Quantidade = versao.Quantidade,
                        ValorTotal = versao.ValorTotal,
                        DataEntrega = versao.DataEntrega,
                        Divergencias = divergenciasVersao,
                        Condicoes = condicoesVersao
                    });


                }

                var condicoesItem = ObterCondicoes(registrosDeTodasVersoes);

                result.Add(new ItemAgregadorDto
                {
                    IdItem = item.Id,
                    Codigo = item.Codigo,
                    Descricao = item.Descricao,
                    ValorAtualizado = valorAtualizado,
                    Divergencias = temDivergencias,
                    Versoes = versoesDto,
                    Condicoes = condicoesItem
                });
            }

            return result;
        }

        public async Task<ObterMovimentacaoDetalhesDto?> ObterDetalhesMovimentacaoAsync(Guid idMovimentacao)
        {
            var movimentacao = await _movimentacoesRepository.BuscarDetalhesMovimentacaoAsync(idMovimentacao);

            if (movimentacao == null || movimentacao.Agregador == null)
                return null;

            var detalhesDto = _mapper.Map<ObterMovimentacaoDetalhesDto>(movimentacao);

            var fornecedor = movimentacao.Documentos
                .Select(d => d.Emitente)
                .Where(e => e != null)
                .FirstOrDefault();

            if (fornecedor != null)
                detalhesDto.Fornecedor = _mapper.Map<FornecedorDto>(fornecedor);

            var veiculo = movimentacao.Agregador?.Ativos?.OfType<Veiculo>().FirstOrDefault();
            if (veiculo != null)
            {
                detalhesDto.Ativo = _mapper.Map<AtivoDto>(veiculo);

                var oficina = veiculo.Oficinas?.FirstOrDefault()?.Oficina;
                if (oficina != null)
                    detalhesDto.Oficina = _mapper.Map<OficinaDto>(oficina);
            }

            var pagamentos = await _pagamentoRepository.BuscarPorAgregadorIdAsync(movimentacao.Agregador.Id);
            var ressarcimentos = await _ressarcimentoRepository.BuscarPorAgregadorIdAsync(movimentacao.Agregador.Id);

            detalhesDto.ValoresConsolidados = new ValoresConsolidadosDto
            {
                APagar = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pendente || p.StatusPagamento == StatusPagamento.PagamentoAgendado || p.StatusPagamento == StatusPagamento.AguardandoConfirmacao).Sum(p => p.Valor),
                Pago = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pago || p.StatusPagamento == StatusPagamento.PagoParcialmente).Sum(p => p.Valor),
                ARessarcir = ressarcimentos.Where(r => r.StatusRessarcimento == StatusRessarcimento.Pendente || r.StatusRessarcimento == StatusRessarcimento.Aprovado).Sum(r => r.Valor),
                Ressarcido = ressarcimentos.Where(r => r.StatusRessarcimento == StatusRessarcimento.Pago).Sum(r => r.Valor)
            };

            var documentos = await _documentoRepository.BuscarAsync(d => d.IdAgregador == movimentacao.Agregador.Id);
            if (documentos.Any())
            {
                detalhesDto.Documentos = _mapper.Map<List<DocumentoDto>>(documentos);
            }

            detalhesDto.Itens = await ObterItensMesmaMovimentacaoAsync(movimentacao.Agregador.Id, movimentacao.Id);

            return detalhesDto;
        }

        private decimal CalcularValorAtualizado(List<ItemVersao> versoesValidas)
        {
            var valorAutorizacoes = versoesValidas
                .Where(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao)
                .Sum(v => v.ValorTotal);

            var valorExclusoes = versoesValidas
                .Where(v => v.TipoMovimento == TipoMovimentoItem.Exclusao)
                .Sum(v => v.ValorTotal);

            return (valorAutorizacoes - valorExclusoes);
        }

        private string ObterNomeDoFornecedor(Pessoa? pessoa)
        {
            var nomeFornecedor = pessoa switch
            {
                PessoaFisica pf => pf.Nome,
                PessoaJuridica pj => pj.RazaoSocial,
                _ => string.Empty
            };

            return nomeFornecedor;
        }

        private List<DocumentoAgregadorDto> ObterDocumentosDaVersao(Documento? documento)
        {
            //TODO: Hoje a classe ItemVersao mapeia apenas um documento e não uma array (public Documento? Documento { get; set; })
            var documentos = new List<DocumentoAgregadorDto>();

            if (documento != null)
            {
                documentos.Add(new DocumentoAgregadorDto
                {
                    TipoDocumento = documento.TipoDocumento,
                    Numero = documento.Numero ?? string.Empty
                });
            }

            return documentos;
        }

        private List<DivergenciaAgregadorDto> ObterDivergencias(IEnumerable<RegistroProcessamentoItemVersao> registrosProcessamento)
        {
            var divergenciasVersao = registrosProcessamento
                .SelectMany(r => r.Divergencias)
                .Select(d => new DivergenciaAgregadorDto
                {
                    Tipo = d.Decisao?.ToString() ?? "Divergência",
                    Descricao = d.Decisao.ToString() ?? "Divergência",
                    Detalhe = d.Detalhe
                })
                .ToList();

            return divergenciasVersao;
        }

        private List<CondicaoAgregadorDto> ObterCondicoes(IEnumerable<RegistroProcessamentoItemVersao> registrosProcessamento)
        {
            var condicoesVersao = registrosProcessamento
                .SelectMany(r => r.Condicoes)
                .Select(c => new CondicaoAgregadorDto
                {
                    Tipo = c.TipoConfiguracao?.ToString() ?? "Condição",
                    Descricao = c.Regra ?? "Condição",
                    Detalhe = c.Detalhe
                })
                .ToList();

            return condicoesVersao;
        }
    }
}
