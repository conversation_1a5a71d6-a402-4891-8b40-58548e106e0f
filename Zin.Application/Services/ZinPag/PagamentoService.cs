using AutoMapper;
using System.Globalization;
using System.Text.RegularExpressions;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Handlers;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag;
public class PagamentoService(
    IAtivoRepository ativoRepository,
    IMapper mapper,
    IPagamentoRepository pagamentoRepository,
    IDocumentoRepository documentoRepository,
    IPessoaRepository pessoaRepository,
    IDocumentoPagamentoRepository documentoPagamentoRepository,
    ILiquidacaoPagamentoRepository liquidacaoPagamentoRepository) : IPagamentoService
{
    public async Task<int> CriarPagamentoAsync(CriaPagamentoDTO criaPagamentoDTO)
    {
        if (criaPagamentoDTO.IdPessoaBeneficiaria.HasValue)
        {
            // Lógica para criar pagamento para pessoa
            Pagamento pagamento = new Pagamento
            {
                DataCriacao = DateTime.UtcNow,
                StatusPagamento = StatusPagamento.Pendente,
                Cancelado = false,
                IdAgregador = criaPagamentoDTO.IdAgregador,
                IdPessoaBeneficiaria = criaPagamentoDTO.IdPessoaBeneficiaria.Value
            };

            if (criaPagamentoDTO.ItensVersoesIds != null && criaPagamentoDTO.ItensVersoesIds.Count != 0)
            {
                List<PagamentoItemVersao> pagamentoItemVersoes = [];

                foreach (var itemVersaoId in criaPagamentoDTO.ItensVersoesIds)
                {
                    var pagamentoItemVersao = new PagamentoItemVersao
                    {
                        Pagamento = pagamento,
                        IdItemVersao = itemVersaoId,
                    };
                    pagamentoItemVersoes.Add(pagamentoItemVersao);
                }

                pagamento.PagamentoItemVersoes = pagamentoItemVersoes;
            }

            return await pagamentoRepository.InserirAsync(pagamento);
        }
        else
        {
            throw new ArgumentNullException(nameof(criaPagamentoDTO.IdPessoaBeneficiaria), "IdPessoa não pode ser nulo. Deve ser fornecido um ID de pessoa para criar o pagamento.");
        }
    }

    

    public async Task<bool> CancelarPagamentoAsync(int pagamentoId)
    {
        var pagamento = await pagamentoRepository.BuscarPorIdAsync(pagamentoId);

        if (pagamento == null)
            return false;

        if (pagamento.Cancelado)
            return false;

        if (pagamento.StatusPagamento == StatusPagamento.Pago)
            return false;

        pagamento.Cancelado = true;
        pagamento.StatusPagamento = StatusPagamento.Cancelado;

        await pagamentoRepository.AtualizarAsync(pagamento);
        return true;
    }

    public async Task<PagamentoProcessamentoResponseDTO> ProcessarPagamentosImportadosAsync(List<PagamentoExcelDTO> pagamentosExcel)
    {
        var response = new PagamentoProcessamentoResponseDTO();

        foreach (var pagamentoExcel in pagamentosExcel)
        {
            var contextPagamento = new PagamentoProcessamentoContext { PagamentoExcel = pagamentoExcel };

            var etapas = new List<Func<PagamentoProcessamentoContext, Task>>
            {
                BuscarNotaFiscal,
                BuscarPessoaBeneficiaria,
                ProcessarPagamento,
                RegistrarResultado
            };

            foreach (var etapa in etapas)
            {
                await etapa(contextPagamento);
                if (contextPagamento.EncerrarFluxo) break;
            }

            if (contextPagamento.Resultado == null)
            {
                contextPagamento.Resultado = new DetalhePagamentoProcessadoDTO
                {
                    NumeroNF = pagamentoExcel.NumeroNF,
                    ValorPago = pagamentoExcel.ValorPago,
                    Erro = contextPagamento.Erro ?? "Erro desconhecido"
                };
            }

            var destino = string.IsNullOrWhiteSpace(contextPagamento.Resultado.Erro)
                ? response.PagamentosComSucesso
                : response.PagamentosComErro;

            destino.Add(contextPagamento.Resultado);
        }

        response.Mensagem = (response.PagamentosComSucesso.Any(), response.PagamentosComErro.Any()) switch
        {
            (true, false) => "Pagamentos processados com sucesso.",
            (true, true) => "Pagamentos processados parcialmente.",
            (false, true) => "Nenhum pagamento foi processado.",
            _ => "Nenhum pagamento foi identificado."
        };

        return response;
    }

    private async Task BuscarNotaFiscal(PagamentoProcessamentoContext contextPagamento)
    {
        var notaFiscal = (await documentoRepository.BuscarAsync(d =>
            d.Numero == contextPagamento.PagamentoExcel.NumeroNF &&
            d.TipoDocumento == TipoDocumento.NotaFiscalVenda)).FirstOrDefault();

        if (notaFiscal == null)
        {
            contextPagamento.EncerrarFluxo = true;
            contextPagamento.Erro = "Nota fiscal não encontrada.";
            return;
        }

        contextPagamento.DocumentoId = notaFiscal.Id;
    }

    private async Task BuscarPessoaBeneficiaria(PagamentoProcessamentoContext contextPagamento)
    {
        var documento = Regex.Replace(contextPagamento.PagamentoExcel.CnpjCpfFavorecido ?? "", @"\D", "");

        var pessoa = await pessoaRepository.BuscarPorDocumentoAsync(documento);
        contextPagamento.PessoaId = pessoa?.Id;

        if (contextPagamento.PessoaId == null)
        {
            contextPagamento.EncerrarFluxo = true;
            contextPagamento.Erro = "Favorecido não encontrado.";
        }
    }

    private async Task ProcessarPagamento(PagamentoProcessamentoContext contextPagamento)
    {
        if (!decimal.TryParse(contextPagamento.PagamentoExcel.ValorPago, NumberStyles.Any, CultureInfo.InvariantCulture, out var valorDestePagamento) ||
            !DateTime.TryParse(
                contextPagamento.PagamentoExcel.DataPagamento,
                CultureInfo.InvariantCulture,
                DateTimeStyles.AssumeLocal | DateTimeStyles.AdjustToUniversal,
                out var dataPagamento))
        {
            contextPagamento.EncerrarFluxo = true;
            contextPagamento.Erro = "Erro de conversão de valores.";
            return;
        }

        try
        {
            var notaFiscal = await documentoRepository.ObterPorIdAsync(contextPagamento.DocumentoId!.Value);
            var pagamento = await pagamentoRepository.ObterPorDocumentoIdAsync(contextPagamento.DocumentoId.Value);

            var valorJaPago = pagamento?.Valor ?? 0;

            if (valorJaPago + valorDestePagamento > notaFiscal.ValorTotal)
            {
                contextPagamento.EncerrarFluxo = true;
                string mensagemErro = $"O valor do pagamento ({valorDestePagamento}) para a nota fiscal {notaFiscal.Numero} excede o valor total da nota ({notaFiscal.ValorTotal}).";

                if (valorJaPago > 0)
                    mensagemErro += $" O valor já pago anteriormente é de {valorJaPago}.";

                contextPagamento.Erro = mensagemErro;
                return;
            }

            if (pagamento is null)
            {
                pagamento = new Pagamento
                {
                    IdPessoaBeneficiaria = contextPagamento.PessoaId!.Value,
                    Valor = valorDestePagamento,
                    DataCriacao = DateTime.UtcNow,
                    StatusPagamento = (valorDestePagamento == notaFiscal.ValorTotal) ? StatusPagamento.Pago : StatusPagamento.PagoParcialmente,
                    Cancelado = false
                };
                contextPagamento.PagamentoId = await pagamentoRepository.InserirAsync(pagamento);

                var relacao = new DocumentoPagamento
                {
                    IdPagamento = contextPagamento.PagamentoId!.Value,
                    IdDocumento = contextPagamento.DocumentoId!.Value
                };
                await documentoPagamentoRepository.InserirAsync(relacao);
            }
            else
            {
                pagamento.Valor += valorDestePagamento;
                pagamento.StatusPagamento = (pagamento.Valor == notaFiscal.ValorTotal) ? StatusPagamento.Pago : StatusPagamento.PagoParcialmente;
                pagamento.DataAtualizacao = DateTime.UtcNow;
                await pagamentoRepository.AtualizarAsync(pagamento);
                contextPagamento.PagamentoId = pagamento.Id;
            }

            var liquidacao = new LiquidacaoPagamento
            {
                IdPagamento = contextPagamento.PagamentoId.Value,
                Data = dataPagamento,
                ValorPago = valorDestePagamento,
                StatusPagamento = pagamento.StatusPagamento
            };

            await liquidacaoPagamentoRepository.InserirAsync(liquidacao);
        }
        catch (Exception ex)
        {
            contextPagamento.EncerrarFluxo = true;
            contextPagamento.Erro = "Erro ao processar pagamento. " + ex.Message;
        }
    }

    private Task RegistrarResultado(PagamentoProcessamentoContext contextPagamento)
    {
        contextPagamento.Resultado = new DetalhePagamentoProcessadoDTO
        {
            NumeroNF = contextPagamento.PagamentoExcel.NumeroNF,
            ValorPago = contextPagamento.PagamentoExcel.ValorPago,
            Erro = contextPagamento.Erro
        };
        return Task.CompletedTask;
    }
    public async Task<bool> ConfirmarPagamentoManualAsync(int pagamentoId)
    {
        var comando = new ConfirmarPagamentoManualCommand(
            pagamentoId,
            pagamentoRepository,
            liquidacaoPagamentoRepository
        );

        var handler = new ConfirmarPagamentoManualHandler();
        return await handler.HandleAsync(comando);
    }
}

public class PagamentoProcessamentoContext
{
    public PagamentoExcelDTO PagamentoExcel { get; init; } = default!;
    public int? DocumentoId { get; set; }
    public int? PessoaId { get; set; }
    public int? PagamentoId { get; set; }
    public bool EncerrarFluxo { get; set; }
    public string? Erro { get; set; }
    public DetalhePagamentoProcessadoDTO? Resultado { get; set; }
}

// Command
public record ConfirmarPagamentoManualCommand(
    int PagamentoId,
    IPagamentoRepository PagamentoRepository,
    ILiquidacaoPagamentoRepository LiquidacaoPagamentoRepository
);

