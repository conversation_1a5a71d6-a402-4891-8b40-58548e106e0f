using Zin.Application.DTOs.Importacao;
using Zin.Application.Interfaces;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Importacoes
{
    public class ImportacaoAgregadorService
    (
        IImportarClienteEmpresaService _clienteEmpresaService,
        IImportarAtivoVeiculoService _veiculoService,
        IImportarItensService _itemService,
        IImportarPessoaJuridicaService _pessoaJuridicaService,
        IImportarPessoaJuridicaService _fornecedorService,
        IMovimentacoesService _movimentacoesService,
        IImportarDocumentoService _documentoService,
        IAgregadorRepository _agregadorRepo
    ) : IImportacaoAgregadorService
    {
        public async Task<Agregador> IniciarProcessamentoAsync(ImportacaoAgregadorDTO dto)
        {
            var agregador = await _agregadorRepo.BuscarPorNumeroAsync(dto.Numero!);

            if (agregador == null)
                agregador = await CriarAgregadorAsync(dto);
            else
                agregador = await AtualizarAgregadorAsync(agregador, dto);

            return agregador;
        }

        private async Task<Agregador> CriarAgregadorAsync(ImportacaoAgregadorDTO dto)
        {
            var agregador = new Agregador(dto.TipoAgregador, dto.Numero);
            agregador = await SalvarAgregadorAsync(agregador, dto);

            return agregador;
        }

        private async Task<Agregador> AtualizarAgregadorAsync(Agregador agregador, ImportacaoAgregadorDTO dto)
        {
            return await SalvarAgregadorAsync(agregador, dto);
        }

        private async Task<Agregador> SalvarAgregadorAsync(Agregador agregador, ImportacaoAgregadorDTO dto)
        {
            await _clienteEmpresaService.AdicionarOuAtualizarClienteDoAgregadorAsync(agregador, dto.ClienteEmpresa);
            await _veiculoService.AdicionarOuAtualizarVeiculosDoAgregadorAsync(agregador, dto.Ativos.Veiculos);

            var oficinas = await _pessoaJuridicaService.ObterOuCriarPessoasJuridicasPorCnpjAsync(dto.Oficinas);
            var fornecedores = await _fornecedorService.ObterOuCriarPessoasJuridicasPorCnpjAsync(dto.Fornecedores);

            await _documentoService.PreencherDocumentosDoAgregadorAsync(agregador, fornecedores, dto.Documentos);
            await _itemService.AdicionarItensEVersoesNoAgregadorAsync(agregador, dto);

            if (agregador.EhNovo)
                await _agregadorRepo.InserirAsync(agregador);
            else
                await _agregadorRepo.AtualizarAsync(agregador);

            await _movimentacoesService.ObterOuAtualizarMovimentacoesAsync(agregador.Itens.ToList());

            await _agregadorRepo.AtualizarAsync(agregador);

            return agregador;
        }
    }
}
