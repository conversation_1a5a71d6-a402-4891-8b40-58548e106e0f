using Serilog;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Interfaces;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Domain.Entidades.Cadastros.Factory;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.ValueObject;

namespace Zin.Application.Services.Importacoes
{
    public class ImportarClienteEmpresaService
    (
        IImportacaoFactory _factory,
        IClienteEmpresaRepository _pessoaRepository
    ) : IImportarClienteEmpresaService
    {
        public async Task AdicionarOuAtualizarClienteDoAgregadorAsync(Agregador agregador, ImportarClienteEmpresaDTO dto)
        {
            var cliente = await _pessoaRepository.BuscarPorCnpjAsync(Cnpj.Criar(dto.Cnpj));

            if (cliente == null)
                cliente = await CriarClienteEmpresaAsync(dto);
            else
                cliente = await AtualizarClienteEmpresaAsync(cliente, dto);

            agregador.DefinirClienteEmpresa(cliente!);
        }

        public async Task<ClienteEmpresa?> CriarClienteEmpresaAsync(ImportarClienteEmpresaDTO dto)
        {
            var cnpj = Cnpj.Criar(dto.Cnpj!);

            Endereco endereco = await _factory.GerarEnderecoAsync(dto.Endereco);
         
            var cliente = PessoaFactory.ClienteEmpresa
            (
                cnpj,
        dto.NomeFantasia,
        dto.RazaoSocial,
            dto.Principal,
                endereco
            );

            await _pessoaRepository.InserirAsync(cliente);

            return cliente;
        }

        public async Task<ClienteEmpresa?> AtualizarClienteEmpresaAsync(ClienteEmpresa cliente, ImportarClienteEmpresaDTO dto)
        {
            cliente.DefinirNomeFantasia(dto.NomeFantasia);
            cliente.DefinirRazaoSocial(dto.RazaoSocial);
            cliente.DefinirMatriz(dto.Principal);
            cliente.DefinirCnpj(Cnpj.Criar(dto.Cnpj!));
            
            await _pessoaRepository.AtualizarAsync(cliente);

            return cliente;
        }
    }
}
