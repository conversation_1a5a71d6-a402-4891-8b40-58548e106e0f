using FluentValidation;
using Newtonsoft.Json;
using Serilog;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Zin.Application.Helpers;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Application.Shared.Retorno;
using Zin.Domain.Entidades.Importacoes;
using Zin.Domain.Repositorios.Importacoes;

namespace Zin.Application.Services.Importacoes
{
    public class ImportacaoService
    (
        IValidator<ImportacaoAgregadorDTO> validador,
        IImportacaoRepository _importacaoRepository
    ) : IImportacaoService
    {
        public async Task<ResultadoApp<ImportacaoCriada>> ImportarAgregadorAsync(ImportacaoAgregadorDTO dto)
        {
            Log.Logger.Information($"Iniciando importação de agregador nº {dto.Numero} do cliente {dto.IdCliente}");
            var validacaoImportacao = ImportacaoHelper.ValidarImportacao(dto);

            if (!validacaoImportacao.Sucesso)
                return ErroImportacao(dto, validacaoImportacao);

            try
            {
                var dtoJson = JsonConvert.SerializeObject(dto);
                var importacaoAgregador = ImportacaoAgregador.CriarAguardandoProcessamento(dtoJson, dto.IdCliente);
                var idImportacao = await _importacaoRepository.InserirAsync(importacaoAgregador);

                return SucessoImportacao(idImportacao, importacaoAgregador);
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, $"Erro ao importar agregador nº {dto.Numero} do cliente {dto.IdCliente}");
                throw new Exception($"Erro ao importar agregador nº {dto.Numero} do cliente {dto.IdCliente}: {ex.Message}", ex);
            }
        }

        private ResultadoApp<ImportacaoCriada> ErroImportacao(ImportacaoAgregadorDTO dto, ResultadoApp<Nada> validacaoImportacao)
        {
            Log.Logger.Error($"Importação inválida para o agregador nº {dto.Numero} do cliente {dto.IdCliente}");

            return ResultadoApp<ImportacaoCriada>.Falha(validacaoImportacao.Erros);
        }

        private ResultadoApp<ImportacaoCriada> SucessoImportacao(int idImportacao, ImportacaoAgregador importacaoAgregador)
        {
            Log.Logger.Information("Importação de agregador concluída com sucesso. ID: {Id}", idImportacao);

            return ResultadoApp<ImportacaoCriada>.OK(new ImportacaoCriada(idImportacao));
        }
    }
}
