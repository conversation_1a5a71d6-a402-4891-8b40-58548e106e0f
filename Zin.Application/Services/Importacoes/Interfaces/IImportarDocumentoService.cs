using Zin.Application.DTOs.Importacao;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;

namespace Zin.Application.Interfaces
{
    public interface IImportarDocumentoService
    {
        Task<Agregador> PreencherDocumentosDoAgregadorAsync
        (
            Agregador agregador,
            IEnumerable<PessoaJuridica> fornecedores,
            IEnumerable<ImportarDocumentoDTO> dtos
        );
    }
}
