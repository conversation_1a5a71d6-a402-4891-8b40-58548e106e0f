using Zin.Application.DTOs.Importacao;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;

namespace Zin.Application.Services.Importacoes.Interfaces
{
    public interface IImportarClienteEmpresaService
    {
        Task AdicionarOuAtualizarClienteDoAgregadorAsync(Agregador agregador, ImportarClienteEmpresaDTO dto);
        Task<ClienteEmpresa?> CriarClienteEmpresaAsync(ImportarClienteEmpresaDTO dto);
        Task<ClienteEmpresa?> AtualizarClienteEmpresaAsync(ClienteEmpresa cliente, ImportarClienteEmpresaDTO dto);
    }
}
