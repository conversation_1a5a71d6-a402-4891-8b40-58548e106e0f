using Zin.Application.DTOs.Configuracao;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Enums.Processos;

namespace Zin.Application.Services.Importacoes.Interfaces
{
    public interface IConfiguracaoService
    {
        Task<ConfiguracaoDto?> ObterConfiguracaPorIdAsync(Guid id);
        Task<IEnumerable<ConfiguracaoDto>> ListarConfiguracaoAsync();
        Task<ConfiguracaoDto> CriarConfiguracaoAsync(CriaConfiguracaoDto dto);
        Task AtualizarConfiguracaoAsync(Guid id, AtualizaConfiguracaoDto dto);
        Task RemoverConfiguracaoAsync(Guid id);

        Task<IEnumerable<Configuracao?>> BuscarPorTipoProcessamentoAsync(TipoProcessamento tipoProcessamento);

        Task<RegraDto?> ObterRegraPorIdAsync(Guid idRegra);       
        Task AtualizarRegraAsync(Guid id, AtualizaRegraDto dto);
        Task RemoverRegraAsync(Guid idRegra);
    }
}
