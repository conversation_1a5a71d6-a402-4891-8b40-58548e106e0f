using Zin.Application.DTOs.Pessoas;
using Zin.Domain.Entidades.Cadastros.Pessoas;

namespace Zin.Application.Services.Importacoes.Interfaces
{
    public interface IPessoaService
    {
        Task AtualizarPessoaAsync(int id, AtualizaPessoaDto pessoa);
        Task<PessoaDto?> BuscarPessoaPorIdAsync(int id);
        Task<int> CriaPessoaAsync(CriaPessoaDto criaPessoaDto);
        Task DeletarPessoaAsync(int id);
        Task<IEnumerable<PessoaDto>> ListarPessoasAsync();
    }
}
