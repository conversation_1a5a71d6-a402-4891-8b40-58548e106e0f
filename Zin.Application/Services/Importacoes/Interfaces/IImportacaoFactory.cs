using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Interfaces
{
    public interface IImportacaoFactory
    {
        Task<Domain.ValueObject.Endereco> GerarEnderecoAsync(ImportarEnderecoDTO dto);
        IReadOnlyList<Domain.ValueObject.DadosBancario> GerarDadosBancarios(IEnumerable<ImportarDadosBancariosZinDTO> dtos);
        IReadOnlyList<Domain.ValueObject.Contato> GerarContatos(IEnumerable<ImportarContatoDTO> dtos);
    }
}
