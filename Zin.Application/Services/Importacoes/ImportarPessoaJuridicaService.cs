using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Application.Services.Importacoes.Util;
using Zin.Domain.Entidades.Cadastros.Factory;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.ValueObject;

namespace Zin.Application.Services.Importacoes
{
    public class ImportarPessoaJuridicaService
    (
        Application.Interfaces.IImportacaoFactory _valueObjectsFactory, 
        IPessoaJuridicaRepository _pessoaRepository
    ) : IImportarPessoaJuridicaService
    {
        public async Task<IEnumerable<PessoaJuridica>> ObterOuCriarPessoasJuridicasPorCnpjAsync(IEnumerable<ImportarPessoaJuridicaDTO> dtos)
        {
            var cnpjs = dtos.Select(o => Cnpj.Criar(o.Cnpj)).ToList();
            var existentes = await _pessoaRepository.BuscarPorCnpjsAsync(cnpjs);

            var criados = await CriarPessoasJuridicasAsync(dtos, existentes.ToList());

            return existentes
               .Concat(criados)
               .Where(p => p is not null)
               .GroupBy(p => p!.Cnpj.NormalizarCNPJ())
               .Select(g => g.First())
               .ToList()!;
        }

        private async Task<IEnumerable<PessoaJuridica>> CriarPessoasJuridicasAsync
        (
            IEnumerable<ImportarPessoaJuridicaDTO> dtos, 
            IEnumerable<PessoaJuridica> existentes
        )
        {
            if (dtos is null)
                return new List<PessoaJuridica>();

            var cnpjsExistentes = existentes.ObterCnpjs();

            var novos = new List<PessoaJuridica>();

            foreach (var dto in dtos)
            {
                var cnpj = Cnpj.Criar(dto.Cnpj);

                if (cnpjsExistentes.Contains(cnpj))
                    continue;

                Endereco endereco = await _valueObjectsFactory.GerarEnderecoAsync(dto.Endereco);
            
                var contatosPreenchidos = dto.Contatos.Where(c => c.Preenchido);
                var contatos = _valueObjectsFactory.GerarContatos(contatosPreenchidos ?? Enumerable.Empty<ImportarContatoDTO>());

                var dadosBancarios = _valueObjectsFactory.GerarDadosBancarios(dto.DadosBancarios);

                var fornecedor = PessoaFactory.PessoaJuridica(
                    cnpj,
                    dto.NomeFantasia,
                    dto.RazaoSocial,
                    endereco,
                    dadosBancarios,
                    contatos
                );

                await _pessoaRepository.InserirAsync(fornecedor);

                novos.Add(fornecedor);

                cnpjsExistentes.Add(cnpj);
            }

            return novos;
        }
    }
}
