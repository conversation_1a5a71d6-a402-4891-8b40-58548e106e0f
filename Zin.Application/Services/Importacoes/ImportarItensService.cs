using AutoMapper;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Application.Services.Importacoes.Util;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Factories.Interfaces;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Domain.ValueObject;

namespace Zin.Application.Services.Importacoes
{
    public class ImportarItensService
    (
        IMapper _mapper,
        IPessoaJuridicaRepository _pessoaJuridicaRepo,
        IVeiculoRepository _veiculoRepo,
        IDocumentoRepository _documentoRepo,
        IItemVersaoService _itemVersaoService,     
        IItemVersaoFactory _factoryItemVersao
    ) : IImportarItensService
    {
        public async Task<Agregador> AdicionarItensEVersoesNoAgregadorAsync
        (
            Agregador agregador,
            ImportacaoAgregadorDTO dto
        )
        { 
            if (agregador is null) throw new ArgumentNullException(nameof(agregador));
            if (dto is null) throw new ArgumentNullException(nameof(dto));

            var dicionarioItensAgregador = HelperImportacao.ObterDicionarioItensAgregador(agregador);
            var itensAgrupados = HelperImportacao.AgruparItensPorCodigoDescricao(dto.Itens);

            foreach (var grupo in itensAgrupados)
            {
                var grupoItemOrdenado = grupo
                    .OrderBy(i => i.DataAutorizacao)
                    .ThenBy(i => OrdemMovimento(i.TipoMovimentoItem))
                    .ToList();

                var primeiraVersao = false;

                // criar item novo (1ª versão) com o 1º DTO do grupo
                if (!dicionarioItensAgregador.TryGetValue(grupo.Key, out var item))
                {
                    item = await CriarItemComPrimeiraVersaoAsync(agregador, grupoItemOrdenado.First());
                    agregador.AdicionarItem(item);
                    dicionarioItensAgregador[grupo.Key] = item;
                    primeiraVersao = true;
                }

                for (var i = primeiraVersao ? 1 : 0; i < grupoItemOrdenado.Count; i++)
                    await TentarAdicionarVersaoNoAgregadorAsync(agregador, item, grupoItemOrdenado[i]);
            }

            return agregador;
        }

        private async Task<Item> CriarItemComPrimeiraVersaoAsync(Agregador agregador, ImportarItemDTO dto)
        {
            var item = new Item
                (
                    dto.Codigo,
                    dto.Descricao,
                    dto.Quantidade,
                    dto.ValorUnitario,
                    dto.ValorTotal
                )
                { 
                    Agregador = agregador
                };


            var itemVersao = await CriarItemVersaoAsync(agregador, dto, item, null);

            item.AdicionarVersao(itemVersao);
            return item;
        }

        private async Task TentarAdicionarVersaoNoAgregadorAsync(Agregador agregador, Item item, ImportarItemDTO dto)
        {
            var res = _itemVersaoService.DeveCriarNovaVersao(item.Versoes, dto);
            if (!res.DeveCriarNovaVersao) return;

            var versao = await CriarItemVersaoAsync(agregador, dto, item, res.VersaoAnterior);
            item.AdicionarVersao(versao);
        }

        private async Task<ItemVersao> CriarItemVersaoAsync
        (
            Agregador agregador,
            ImportarItemDTO dto,
            Item item,
            ItemVersao? versaoAnterior
        )
        {
            var fornecedor = await _pessoaJuridicaRepo.BuscarPorCnpjAsync(Cnpj.Criar(dto.CnpjFornecedor))
                ?? throw new KeyNotFoundException($"Fornecedor com CNPJ {dto.CnpjFornecedor} não encontrado.");

            var oficina = await _pessoaJuridicaRepo.BuscarPorCnpjAsync(Cnpj.Criar(dto.CnpjOficina))
                ?? throw new KeyNotFoundException($"Oficina com CNPJ {dto.CnpjOficina} não encontrada.");

            var veiculo = await _veiculoRepo.BuscarVeiculoPorPlacaAsync(Placa.Criar(dto.VeiculoPlaca))
                ?? throw new KeyNotFoundException($"Veículo com placa {dto.VeiculoPlaca} não encontrado.");

            var documentos = new List<Documento>();
            foreach (var docDto in dto.DocumentosRelacionados)
            {
                var documento = await _documentoRepo.BuscarPorIdAgregadorENumero(agregador.Id, docDto.NumeroNota)
                    ?? throw new KeyNotFoundException($"Documento com Número {docDto.NumeroNota} não encontrado.");

                documentos.Add(documento);
            }

            var itemVersao = _factoryItemVersao.Criar
            (
                item,
                versaoAnterior,
                fornecedor,
                oficina,
                veiculo,
                dto.Quantidade,
                dto.ValorUnitario,
                dto.ValorTotal,
                dto.TipoMovimentoItem,
                dto.TipoItem,
                dto.DataCriacao,
                dto.DataAutorizacao,
                dto.DataMovimento,
                dto.DataEntrega,
                documentos
            );

            return itemVersao;
        }

        private int OrdemMovimento(TipoMovimentoItem tipo) =>
            tipo switch
            {
                TipoMovimentoItem.Autorizacao => 0,
                TipoMovimentoItem.Exclusao => 1,
                TipoMovimentoItem.Desconhecido => 2,
                TipoMovimentoItem.Outro => 3,
                _ => 99
            };
    }
}
