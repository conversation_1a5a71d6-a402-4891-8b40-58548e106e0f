using AutoMapper;
using Zin.Application.DTOs.Documentos;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Interfaces;
using Zin.Application.Services.Importacoes.Util;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Importacoes
{
    public class ImportarDocumentoService
    (
        IMapper _mapper,
        IDocumentoService _service,
        IDocumentoRepository _documentoRepo
    ) : IImportarDocumentoService
    {
        public async Task<Agregador> PreencherDocumentosDoAgregadorAsync
        (
            Agregador agregador,
            IEnumerable<PessoaJuridica> fornecedores,
            IEnumerable<ImportarDocumentoDTO> dtos
        )
        {
            if (dtos == null || !dtos.Any())
                return agregador;

            var notasfscaisDetalhado = MapearNotasFiscaisDetalhado(agregador, fornecedores, dtos);

            foreach (var dto in notasfscaisDetalhado)
            {
                var documento = await _documentoRepo.BuscarPorIdAgregadorENumero(agregador.Id, dto.NumeroDocumento);

                if (documento != null)
                    continue;

                documento = await CriarDocumentoAsync(agregador, dto);
            }

            return agregador;
        }

        private IEnumerable<ImportarNotaFiscalDetalhado> MapearNotasFiscaisDetalhado
        (
            Agregador agregador,
            IEnumerable<PessoaJuridica> fornecedores,
            IEnumerable<ImportarDocumentoDTO> dtos
        )
        {
            var fornecedoresPorCnpj = HelperImportacao.ObterDicionarioPessoaJuridica(fornecedores);
            var notasFiscaisResultado = new List<ImportarNotaFiscalDetalhado>();

            foreach (var dto in dtos)
            {
                var notaFiscal = _mapper.Map<ImportarNotaFiscalDetalhado>(dto);
                notaFiscal.DefinirFornecedor(fornecedoresPorCnpj[dto.CnpjFornecedor]);
                notasFiscaisResultado.Add(notaFiscal);
            }

            return 
                notasFiscaisResultado
                    .GroupBy(n => 
                        HelperImportacao.GerarChaveNotaFiscal(n.CnpjFornecedor,n.NumeroDocumento,n.Serie))
                        .Select(g => g.First()
                    ).ToList();
        }

        private async Task<Documento> CriarDocumentoAsync(Agregador agregador, ImportarNotaFiscalDetalhado notaFiscal)
        {
            var criaNotaFiscalDTO = GerarDocumentoCriaNotaFiscalDTO(agregador, notaFiscal);
            var idDocumento = await _service.CriarNotaFiscalAsync(criaNotaFiscalDTO);
            var documento = await _documentoRepo.BuscarPorIdAsync(idDocumento);

            return documento;
        }

        private CriaNotaFiscalDTO GerarDocumentoCriaNotaFiscalDTO(Agregador agregador, ImportarNotaFiscalDetalhado dto)
        {
            return new CriaNotaFiscalDTO
            (
                dto.TipoDocumento,
                agregador,
                dto.Fornecedor as PessoaJuridica,
                agregador.ClienteEmpresa,
                dto.NumeroDocumento,          
                dto.Serie,
                dto.DataEmissao, 
                dto.ValorTotal,
                dto.Arquivo != null ? dto.Arquivo.Tamanho : 0, 
                dto.Arquivo != null ? dto.Arquivo.Nome : string.Empty, 
                dto.Arquivo != null ? dto.Arquivo.MimeType : string.Empty,
                dto.Arquivo != null ? dto.Arquivo.Base64 : string.Empty
            );
        }
    }
}
