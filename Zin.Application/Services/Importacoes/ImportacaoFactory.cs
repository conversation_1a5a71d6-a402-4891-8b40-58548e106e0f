using Serilog;
using Zin.Application.DTOs.Importacao;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.ValueObject;
using Contato = Zin.Domain.ValueObject.Contato;
using DadosBancario = Zin.Domain.ValueObject.DadosBancario;
using Endereco = Zin.Domain.ValueObject.Endereco;

namespace Zin.Application.Services.Importacoes
{
    public class ImportacaoFactory(ICidadeRepository cidadeRepository) : Application.Interfaces.IImportacaoFactory
    {
        public async Task<Endereco> GerarEnderecoAsync(ImportarEnderecoDTO dto)
        {
            if (dto is null) return null;

            try
            {
                if (string.IsNullOrWhiteSpace(dto.Logradouro)) throw new ArgumentException("Logradouro é obrigatório.", nameof(dto));
                if (string.IsNullOrWhiteSpace(dto.Numero)) throw new ArgumentException("Número é obrigatório.", nameof(dto));
                if (string.IsNullOrWhiteSpace(dto.<PERSON>rro)) throw new ArgumentException("Bairro é obrigatório.", nameof(dto));

                var cidade = await cidadeRepository.BuscarPorCodigoIBGE(dto.CodIbgeCidade)
                    ?? throw new InvalidOperationException($"Cidade não encontrada para o IBGE {dto.CodIbgeCidade}.");

                var cep = Cep.Criar(dto.Cep);

                return new Endereco(
                    dto.Logradouro,
                    dto.Numero,
                    dto.Complemento,
                    dto.Bairro,
                    cidade.Id!,
                    cidade.IdEstado!,
                    cep
                );
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Erro ao gerar Endereço.");
                return null;
            }
        }

        public IReadOnlyList<DadosBancario> GerarDadosBancarios(IEnumerable<ImportarDadosBancariosZinDTO> dtos)
        {
            if (dtos is null) return null;

            try
            {
                return dtos
                    .Where(dto => !string.IsNullOrEmpty(dto.BancoCpfCnpjTitular))
                    .Select(dto => new DadosBancario(
                        dto.BancoTitular,
                        dto.BancoCpfCnpjTitular,
                        dto.BancoNome,
                        dto.BancoConta,
                        dto.BancoAgencia
                    ))
                    .ToList();
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Erro gerar dados bancários.");
                return new List<DadosBancario>();
            }
        }

        public IReadOnlyList<Contato> GerarContatos(IEnumerable<ImportarContatoDTO> dtos)
        {
            try
            {
                if (dtos is null)
                    throw new ArgumentNullException(nameof(dtos));

                return dtos.Select(dto => new Contato(
                dto.Nome,
                dto.Valor,
                dto.MeioContato,
                dto.RecebeNotificacao,
                dto.Observacao
                ))
                .ToList();

            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Erro ao gerar contatos.");
                return new List<Contato>();
            }
        }
    }
}
