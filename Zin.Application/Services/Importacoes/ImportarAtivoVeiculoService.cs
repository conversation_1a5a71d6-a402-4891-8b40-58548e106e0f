using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Domain.ValueObject;

namespace Zin.Application.Services.Importacoes
{
    public class ImportarAtivoVeiculoService(IVeiculoRepository _veiculoRepository) : IImportarAtivoVeiculoService
    {
        public async Task<Agregador> AdicionarOuAtualizarVeiculosDoAgregadorAsync(Agregador agregador, IList<ImportarAtivoVeiculoDTO> dtos)
        {
            if (dtos is null || dtos.Count == 0)
                return agregador;

            var placas = dtos
                .Select(d => Placa.Criar(d.Placa))
                .Distinct() 
                .ToList();

            var existentes = await _veiculoRepository.BuscarVeiculosPorPlacasAsync(placas);

            var placasExistentes = new HashSet<Placa>(
                existentes.Select(v => Placa.Criar(v.Placa))
            );

            var novos = await CriarNovosVeiculosAsync(dtos, placasExistentes);

            var todos = existentes.Concat(novos);

            foreach (var veiculo in todos)
            {
                var jaVinculado = agregador.Ativos
                    .OfType<Veiculo>()
                    .Any(v => v.Placa.Equals(veiculo.Placa));

                if (!jaVinculado)
                    agregador.Ativos.Add(veiculo);
            }

            return agregador;
        }

        private async Task<List<Veiculo>> CriarNovosVeiculosAsync(IEnumerable<ImportarAtivoVeiculoDTO> dtos, HashSet<Placa> placasJaExistentes)
        {
            var dtosNovos = dtos
                 .Select(d => new { Dto = d, Placa = Placa.Criar(d.Placa) })
                 .Where(x => !placasJaExistentes.Contains(x.Placa))
                 .GroupBy(x => x.Placa) // protege contra duplicado na lista
                 .Select(g => g.First().Dto)
                 .ToList();

            var novos = new List<Veiculo>(dtosNovos.Count);

            foreach (var dto in dtosNovos)
            {
                var placa = Placa.Criar(dto.Placa);

                var veiculo = new Veiculo(placa)
                {
                    Chassi = dto.Chassi,
                    Cor = dto.Cor,
                    Modelo = dto.Modelo,
                    AnoFabricacao = dto.AnoFabricacao,
                    AnoModelo = dto.AnoModelo
                };

                await _veiculoRepository.InserirAsync(veiculo);

                placasJaExistentes.Add(placa);
                novos.Add(veiculo);
            }

            return novos;
        }
    }
}