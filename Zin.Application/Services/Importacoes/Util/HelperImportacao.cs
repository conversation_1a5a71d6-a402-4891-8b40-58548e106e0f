using Zin.Application.DTOs.Importacao;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.ValueObject;

namespace Zin.Application.Services.Importacoes.Util
{
    public static class HelperImportacao
    {
        public static Dictionary<string, PessoaJuridica> ObterDicionarioPessoaJuridica(IEnumerable<PessoaJuridica> pessoas)
        {
            return pessoas
                .GroupBy(p => p.Cnpj)
                .ToDictionary(g => g.Key, g => g.First());
        }

        public static HashSet<Cnpj> ObterCnpjs(this IEnumerable<PessoaJuridica>? pessoas) =>
            new HashSet<Cnpj>(
                (pessoas ?? Enumerable.Empty<PessoaJuridica>())
                    .Select(o => Cnpj.Criar(o.Cnpj))
            );

        public static string NormalizarCNPJ(this string cnpj) => Cnpj.Criar(cnpj).ToString();
        public static string NormalizarPlaca(this string placa) => Placa.Criar(placa).ToString();

        public static Dictionary<string, Veiculo> ObterDicionarioVeiculos(IEnumerable<Ativo> veiculos)
        {
            return veiculos
                .OfType<Veiculo>()
                .GroupBy(v => v.Placa)
                .ToDictionary(g => g.Key, g => g.First());
        }

        public static Dictionary<string, Documento> ObterDicionarioNotasFiscaisDosDocumentos(this IEnumerable<Documento> documentos) =>
            documentos
                .GroupBy(d => GerarChaveNotaFiscal((d.Emitente as PessoaJuridica).Cnpj, d.Numero, d.Serie))
                .ToDictionary(g => g.Key, g => g.First());

        public static Dictionary<string, ImportarNotaFiscalItemDTO> ObterDicionarioNotasFiscaisDosDTOs(this IEnumerable<ImportarNotaFiscalItemDTO> documentos) =>
            documentos
                .GroupBy(d => GerarChaveNotaFiscal(d.CnpjFornecedor, d.NumeroNota, d.Serie))
                .ToDictionary(g => g.Key, g => g.First());

        public static string GerarChaveNotaFiscal(string cnpj, string? numero, string serie) => $"{cnpj.NormalizarCNPJ()}|{serie}|{numero}".ToUpper();

        public static Dictionary<string, Item> ObterDicionarioItensAgregador(Agregador agregador) =>
        agregador.Itens.ToDictionary(
            i => GerarChaveAgrupamento(i.Codigo, i.Descricao), i => i);

        public static IEnumerable<IGrouping<string, ImportarItemDTO>> AgruparItensPorCodigoDescricao(IEnumerable<ImportarItemDTO> dtos) =>
            dtos.GroupBy(d => GerarChaveAgrupamento(d.Codigo, d.Descricao));

        public static string GerarChaveAgrupamento(string codigo, string descricao)
        {
            if (string.IsNullOrWhiteSpace(codigo) || string.IsNullOrWhiteSpace(descricao))
                throw new ArgumentException("Código e Descrição são obrigatórios para agrupar itens.");

            return $"{codigo?.Trim()}|{descricao?.Trim()}".ToUpperInvariant();
        }
    }
}
