using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Ressarcimentos
{
    public class CriaRessarcimentoDto
    {
        public int IdPessoa { get; set; }
        public int IdAgregador { get; set; }
        public decimal Valor { get; set; }
        public StatusRessarcimento StatusRessarcimento { get; set; }
        public DateTime DataPrevisao { get; set; } = DateTime.UtcNow;
        public decimal ValorTotal { get; set; }
        public FormasPagamentoRessarcimento formasPagamentoRessarcimento { get; set; }

        public List<int> ItensVersoesIds { get; set; } = [];
    }
}
