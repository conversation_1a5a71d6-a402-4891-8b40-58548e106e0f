using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Ressarcimentos
{
    public class FiltrosDTO
    {
        public string? NumeroAgregador { get; set; } 
        public string? Fornecedor { get; set; } 
        public DateTime? DataAutorizacaoInicial { get; set; } 
        public DateTime? DataAutorizacaoFinal { get; set; } 
        public DateTime? DataRessarcimentoInicial { get; set; } 
        public DateTime? DataRessarcimentoFinal { get; set; } 
        public string? NumeroDocumento { get; set; } 
        public StatusVeiculo? StatusVeiculo { get; set; }
        public string? Placa { get; set; } 
        public bool? Divergencia { get; set; } 
        public bool? Condicao { get; set; } 
    }
}
