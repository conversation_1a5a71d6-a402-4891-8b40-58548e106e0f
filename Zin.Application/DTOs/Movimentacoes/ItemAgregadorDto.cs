namespace Zin.Application.DTOs.Movimentacoes
{
    public class ItemAgregadorDto
    {
        public int IdItem { get; set; }
        public string Codigo { get; set; } = string.Empty;
        public string? Descricao { get; set; }
        public decimal ValorAtualizado { get; set; }
        public bool Divergencias { get; set; }
        public List<VersaoAgregadorDto> Versoes { get; set; } = [];
        public List<CondicaoAgregadorDto> Condicoes { get; set; } = [];
    }
}
