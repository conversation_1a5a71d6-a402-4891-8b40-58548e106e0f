using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Movimentacoes
{
    public class MovimentacaoPagamentoDto
    {
        public int Id { get; set; }
        public DateTime DataCriacao { get; set; }
        public decimal ValorTotal { get; set; }
        public decimal ValorAPagar { get; set; }
        public DateTime? DataProgramada { get; set; }
        public StatusPagamento Situacao { get; set; }
        public List<MovimentacaoLiquidacaoDto> Liquidacoes { get; set; } = new List<MovimentacaoLiquidacaoDto>();
    }
}
