using Zin.Application.DTOs.Comum;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Application.DTOs.Movimentacoes
{
    public class MovimentacaoDto
    {
        public int IdMovimentacao { get; set; }
        public int IdAgregador { get; set; }
        public string? NumeroAgregador { get; set; }
        public MovimentacaoFornecedorDto? Fornecedor { get; set; }
        public string? DataAutorizacao { get; set; }
        public int? DocumentoId { get; set; }
        public string? DocumentoNumero { get; set; }
        public decimal ValorPagar { get; set; }
        public decimal ValorPago { get; set; }
        public decimal ValorRessarcir { get; set; }
        public decimal ValorRessarcido { get; set; }
        public EnumDto<StatusProcessamento>? Situacao { get; set; }
        public string? AtivoCodigo { get; set; }
        public EnumDto<StatusVeiculo>? AtivoStatus { get; set; }
        public List<DivergenciaDto>? Divergencias { get; set; }
        public List<CondicaoDto>? Condicoes { get; set; }
    }
}
