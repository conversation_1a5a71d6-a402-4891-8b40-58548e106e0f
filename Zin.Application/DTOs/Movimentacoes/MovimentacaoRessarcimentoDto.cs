using Zin.Domain.Enums;
using System.Collections.Generic;

namespace Zin.Application.DTOs.Movimentacoes
{
    public class MovimentacaoRessarcimentoDto
    {
        public int Id { get; set; }
        public DateTime DataCriacao { get; set; }
        public decimal ValorTotal { get; set; }
        public decimal ValorARessarcir { get; set; }
        public DateTime? DataProgramada { get; set; }
        public StatusRessarcimento Situacao { get; set; }
        public List<MovimentacaoLiquidacaoRessarcimentoDto> Liquidacoes { get; set; } = new List<MovimentacaoLiquidacaoRessarcimentoDto>();
    }
}
