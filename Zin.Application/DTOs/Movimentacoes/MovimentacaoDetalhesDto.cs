namespace Zin.Application.DTOs.Movimentacoes
{
    public class MovimentacaoDetalhesDto
    {
        public int IdMovimentacao { get; set; }
        public string? NumeroAgregador { get; set; }
        public decimal ValorTotalAcumulado { get; set; }
        public decimal ValorTotalPago { get; set; }
        public decimal ValorTotalAPagar { get; set; }
        public decimal ValorPassivelRessarcimento { get; set; }
        public List<MovimentacaoAtivoDto>? Ativos { get; set; }
        public MovimentacaoFornecedorDto? Fornecedor { get; set; }
        public MovimentacaoNotaFiscalDto? NotaFiscal { get; set; }
        public List<MovimentacaoItemDto>? Itens { get; set; }
        public MovimentacaoDadosGeraisAgregador? DadosGeraisAgregador { get; set; }
        public List<MovimentacaoPagamentoDto> Pagamentos { get; set; } = new();
    }
}
