namespace Zin.Application.DTOs.Movimentacoes
{
    public class ItemVersaoDto
    {
        public int Id { get; set; }
        public TipoMovimentoDto? TipoMovimento { get; set; }
        public string? DataAutorizacao { get; set; }
        public string? DataMovimento { get; set; }
        public int? DocumentoId { get; set; }
        public MovimentacaoFornecedorDto? Fornecedor { get; set; }
        public int Quantidade { get; set; }
        public decimal ValorUnitario { get; set; }
        public decimal ValorTotal { get; set; }
        public string? DataEntrega { get; set; }
    }
}
