using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Pagamentos
{
    public class CriaPagamentoDTO
    {
        public int? IdPessoaBeneficiaria { get; set; }
        public int IdAgregador { get; set; }
        public StatusPagamento StatusPagamento { get; set; }
        public DateTime? DataPrevisao { get; set; }
        public decimal ValorTotal { get; set; }
        public FormaPagamento FormaPagamento { get; set; }

        public List<int> ItensVersoesIds { get; set; } = [];
    }
}
