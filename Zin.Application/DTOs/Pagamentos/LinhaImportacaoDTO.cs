using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Pagamentos;

public class LinhaImportacaoDTO
{
    public int Id { get; set; }
    public int NumeroLinha { get; set; }
    
    // Chaves de identificação
    public string? CnpjPagador { get; set; }
    public string? CnpjCpfFavorecido { get; set; }
    public string? NumeroNF { get; set; }
    
    // Datas
    public DateTime? DataProgramacao { get; set; }
    public DateTime? DataLiquidacao { get; set; }
    public decimal ValorPago { get; set; }
    
    // Resultado do processamento
    public OperacaoLinhaPagamento OperacaoAplicada { get; set; }
    public StatusLinhaPagamento Status { get; set; }
    public string Mensagem { get; set; } = string.Empty;
    public int? IdPagamento { get; set; }
    public bool PagamentoEncontrado { get; set; }
    public bool PagamentoCriado { get; set; }
}

public class LinhaImportacaoListagemResponseDTO
{
    public List<LinhaImportacaoDTO> <PERSON><PERSON> { get; set; } = new();
    public int TotalRegistros { get; set; }
    public int PaginaAtual { get; set; }
    public int TamanhoPagina { get; set; }
    public int TotalPaginas { get; set; }
    
    // Informações do lote
    public LoteImportacaoDetalheDTO LoteInfo { get; set; } = new();
}
