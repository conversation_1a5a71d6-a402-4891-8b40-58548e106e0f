using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Pagamentos;

public class LoteImportacaoDTO
{
    public int Id { get; set; }
    public string NomeArquivo { get; set; } = string.Empty;
    public DateTime DataProcessamento { get; set; }
    public string UsuarioProcessamento { get; set; } = string.Empty;
    public StatusLoteImportacao Status { get; set; }
    
    // Métricas do lote
    public int TotalLinhasLidas { get; set; }
    public int TotalProgramados { get; set; }
    public int TotalLiquidados { get; set; }
    public int TotalCriados { get; set; }
    public int TotalIgnorados { get; set; }
    public int TotalDuplicados { get; set; }
    public int TotalErros { get; set; }
    
    // Propriedades calculadas
    public int TotalProcessados => TotalProgramados + TotalLiquidados + TotalCriados;
    public int TotalComProblemas => TotalIgnorados + TotalDuplicados + TotalErros;
    public bool TemErros => TotalErros > 0;
    public bool TemIgnorados => TotalIgnorados > 0;
}

public class LoteImportacaoListagemResponseDTO
{
    public List<LoteImportacaoDTO> Lotes { get; set; } = new();
    public int TotalRegistros { get; set; }
    public int PaginaAtual { get; set; }
    public int TamanhoPagina { get; set; }
    public int TotalPaginas { get; set; }
}

public class LoteImportacaoDetalheDTO : LoteImportacaoDTO
{
    public string TipoArquivo { get; set; } = string.Empty;
    public long TamanhoArquivo { get; set; }
    public DateTime? DataInicio { get; set; }
    public DateTime? DataFim { get; set; }
    public TimeSpan? TempoProcessamento => DataFim.HasValue && DataInicio.HasValue 
        ? DataFim.Value - DataInicio.Value 
        : null;
}


