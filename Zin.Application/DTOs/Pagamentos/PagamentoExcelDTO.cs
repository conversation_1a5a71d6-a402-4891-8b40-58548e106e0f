namespace Zin.Application.DTOs.Pagamentos;

public class PagamentoExcelDTO
{
    public string? Sinistro { get; set; }
    public string? Placa { get; set; }
    public string? CnpjPagador { get; set; }
    public string? NomePagador { get; set; }
    public string? CnpjCpfFavorecido { get; set; }
    public string? Favorecido { get; set; }
    public string? NumeroNF { get; set; }
    public string? DataEmissaoNF { get; set; }
    public string? DataAutorizacaoPagamento { get; set; }
    public string? DataPagamento { get; set; }
    public string? ValorPago { get; set; }
    public string? TipoDocumento { get; set; }
    public string? StatusPagamento { get; set; }
    public string? FormaPagamento { get; set; }
    public string? BancoAgConta { get; set; }
    public string? ChavePix { get; set; }
}