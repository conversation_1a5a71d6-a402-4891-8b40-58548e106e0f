using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Pagamentos;

public class PagamentoListagemDTO
{
    public int Id { get; set; }
    public int IdPessoaBeneficiaria { get; set; }
    public string? NomeBeneficiario { get; set; }
    public string? CnpjCpfBeneficiario { get; set; }
    public int? IdAgregador { get; set; }
    public string? NumeroAgregador { get; set; }
    public StatusPagamento StatusPagamento { get; set; }
    public DateTime DataCriacao { get; set; }
    public DateTime? DataAtualizacao { get; set; }
    public DateTime? DataPrevisao { get; set; }
    public decimal Valor { get; set; }
    public FormaPagamento FormaPagamento { get; set; }
    public string? Descricao { get; set; }
    public bool Cancelado { get; set; }
    
    // Informações de liquidação
    public decimal ValorPago { get; set; }
    public decimal ValorPendente { get; set; }
    public DateTime? DataUltimaLiquidacao { get; set; }
    public int QuantidadeLiquidacoes { get; set; }
}

public class PagamentoListagemResponseDTO
{
    public List<PagamentoListagemDTO> Pagamentos { get; set; } = new();
    public int TotalRegistros { get; set; }
    public int PaginaAtual { get; set; }
    public int TamanhoPagina { get; set; }
    public int TotalPaginas { get; set; }
    
    // Resumo estatístico
    public PagamentoResumoDTO Resumo { get; set; } = new();
}

public class PagamentoResumoDTO
{
    public decimal ValorTotalPendente { get; set; }
    public decimal ValorTotalPago { get; set; }
    public decimal ValorTotalCancelado { get; set; }
    public int QuantidadePendente { get; set; }
    public int QuantidadePago { get; set; }
    public int QuantidadeCancelado { get; set; }
}
