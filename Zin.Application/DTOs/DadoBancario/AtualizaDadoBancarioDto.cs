namespace Zin.Application.DTOs.DadoBancario
{
    public class AtualizaDadoBancarioDto
    {
        public int IdBanco { get; set; }

        public long Agencia { get; set; }

        public byte? AgenciaDv { get; set; }

        public string Conta { get; set; }

        public byte? ContaDv { get; set; }

        public int TipoConta { get; set; }

        public required string Titular { get; set; }

        public required string CpfCnpjTitular { get; set; }

        public string? Pix { get; set; }
    }
}
