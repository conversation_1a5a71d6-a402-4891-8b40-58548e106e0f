using Zin.Application.DTOs.Documentos;
using Zin.Application.DTOs.Empresas;
using Zin.Application.DTOs.Itens;
using Zin.Application.DTOs.Veiculos;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Agregadores
{
    public class CriaAgregadorDto
    {
        public int? TipoAgregador { get; set; }
        public required string Numero { get; set; }
        public required CriaClienteEmpresaDto ClienteEmpresa { get; set; }
        public List<CriaVeiculoDto> AtivoVeiculo { get; set; } = [];
        public required List<CriaItemDto> Itens { get; set; } = [];
        public required List<CriaNotaFiscalDTO> DocumentosNota { get; set; } = [];
        public TipoAtivo TipoAtivo { get; set; }
    }
}