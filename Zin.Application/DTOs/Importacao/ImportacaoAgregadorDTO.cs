using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Importacao
{
    public class ImportacaoAgregadorDTO
    {
        public TipoAgregador TipoAgregador { get; set; }
        public string Numero { get; set; }
        public string IdCliente { get; set; }

        public ImportarClienteEmpresaDTO? ClienteEmpresa { get; set; }

        public ImportarAtivosDTO? Ativos { get; set; }
        public IList<ImportarPessoaJuridicaDTO>? Fornecedores { get; set; }
        public IList<ImportarPessoaJuridicaDTO>? Oficinas { get; set; }
        public IList<ImportarItemDTO>? Itens { get; set; }
        public IList<ImportarDocumentoDTO>? Documentos { get; set; }
    }
}
