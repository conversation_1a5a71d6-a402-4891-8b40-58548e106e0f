using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Importacao
{
    public class ImportarItemDTO
    {
        public string? Codigo { get; set; }
        public string? Descricao { get; set; }
        public int Quantidade { get; set; } // Também vai na versão
        public decimal ValorUnitario { get; set; }
        public decimal ValorTotal { get; set; }

        #region ItemVersao
        public int IdItemPedidoFornecedor { get; set; }
        public int IdFornecedor { get; set; }
        public string? CnpjFornecedor { get; set; }
        public string? CnpjOficina { get; set; }

        public TipoMovimentoItem TipoMovimentoItem { get; set; }
        public TipoItemVersao TipoItem { get; set; }

        public DateTime DataCriacao { get; set; }

        public DateTime DataAutorizacao { get; set; }
        public DateTime DataMovimento { get; set; }
      
        public DateTime? DataEntrega { get; set; }

        public IList<ImportarNotaFiscalItemDTO>? DocumentosRelacionados { get; set; }
        public string? VeiculoPlaca { get; set; }
        #endregion
    }
}
