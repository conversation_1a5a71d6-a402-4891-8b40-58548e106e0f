using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Importacao
{
    public class ImportarContatoDTO
    {
        public MeioDeContato MeioContato { get; set; }
        public string? Nome { get; set; }
        public string Valor { get; set; }
        public string? Observacao { get; set; }
        public bool RecebeNotificacao { get; set; }

        public bool Preenchido =>
            !string.IsNullOrEmpty(Nome)
            && !string.IsNullOrEmpty(Valor);
    }
}
