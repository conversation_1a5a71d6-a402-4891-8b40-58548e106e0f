
using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Itens
{
    public class CriaItemDto
    {
        public TipoMovimentoItem TipoMovimentoItem { get; set; }
        public required string Codigo { get; set; }
        public string? Descricao { get; set; }
        public DateTime DataCriacao { get; set; }
        public DateTime DataAutorizacao { get; set; }
        public DateTime DataMovimento { get; set; }
        public string? CnpjFornecedor { get; set; }
        public string? RazaoSocialFornecedor { get; set; }
        public string? NomeFantasiaFornecedor { get; set; }
        public int Quantidade { get; set; }
        public decimal ValorUnitario { get; set; }
        public decimal ValorTotal { get; set; }
        public DateTime? DataEntrega { get; set; }
    }
}
