using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Application.DTOs.Configuracao
{
    public class CriaConfiguracaoDto
    {
        public string Nome { get; set; } = string.Empty;
        public TipoProcessamento TipoProcessamento { get; set; }
        public TipoConfiguracao TipoConfiguracao { get; set; }
        public bool Ativo { get; set; }
        public DateTime CriadoEm { get; set; }
        public string CriadoPor { get; set; } = string.Empty;
        public List<CriaRegraDto>? Regras { get; set; }
    }
}
