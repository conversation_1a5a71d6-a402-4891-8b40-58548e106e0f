using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Pessoas
{
    public class PessoaDto
    {
        public int Id { get; set; }
        public TipoPessoa TipoPessoa { get; set; }

        // Campos de PessoaFisica
        public string? Nome { get; set; }
        public string? Sobrenome { get; set; }
        public string? Cpf { get; set; }

        // Campos de PessoaJuridica
        public string? RazaoSocial { get; set; }
        public string? NomeFantasia { get; set; }
        public string? Cnpj { get; set; }
        public PessoaEnderecoDto[] Enderecos { get; set; }
        public PessoaContatoDto[] Contatos { get; set; }
        public PessoaDadoBancarioDto[] DadosBancarios { get; set; }
    }
}
