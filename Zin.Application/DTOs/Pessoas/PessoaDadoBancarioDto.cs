using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Pessoas
{
    public class PessoaDadoBancarioDto
    {
        public int id_dados_bancarios { get; set; }
        public int IdBanco { get; set; }
        public string NomeBanco { get; set; }
        public string Agencia { get; set; }
        public string AgenciaDv { get; set; }
        public string Conta { get; set; }
        public string ContaDv { get; set; }
        public TipoConta TipoConta { get; set; }
        public required string Titular { get; set; }
        public required string CpfCnpjTitular { get; set; }
        public string? Pix { get; set; }
        public bool Principal { get; set; }
    }
}
