using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.ValueObject;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Specification para verificar se a data de autorização de um item recebido é nova
    /// Caso seja, deve ser criada uma nova versão do item.
    /// </summary>
    internal class DataAutorizacaoNovaSpecification : IItemVersaoSpecification
    {
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> todasVersoes, ImportarItemDTO dto)
        {
            ItemVersaoSpecificationHelper.GarantirIntegridadeDasVersoes(todasVersoes, dto);

            bool deveCriarNovaVersao = false;
            ItemVersao? versaoAnterior = null;
            var cnpj = Cnpj.Criar(dto.CnpjFornecedor);

            var ultimaVersaoFornecedor = ItemVersaoSpecificationHelper.ObterUltimaVersaoPorFornecedor(
                todasVersoes,
                cnpj
            );

            if (ultimaVersaoFornecedor != null
                && ultimaVersaoFornecedor.DataHoraAutorizacao != dto.DataAutorizacao)
            {
                deveCriarNovaVersao = true;
                versaoAnterior = ultimaVersaoFornecedor;
            }

            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(deveCriarNovaVersao, versaoAnterior);
        }
    }
}
