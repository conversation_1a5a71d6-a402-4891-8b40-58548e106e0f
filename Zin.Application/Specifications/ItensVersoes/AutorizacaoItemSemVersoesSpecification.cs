using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.ValueObject;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Specification para verificar se um item não possui versões anteriores
    /// caso não haja versões, deve ser criada uma nova versão do item.
    /// Se houver versões, mas nenhuma com a mesma data de autorização e fornecedor,
    /// também deve ser criada uma nova versão.
    /// </summary>
    public class AutorizacaoItemSemVersoesSpecification : IItemVersaoSpecification
    {
        /// <summary>
        /// Verifica se deve ser criada uma nova versão do item
        /// </summary>
        /// <param name="versoes"><PERSON><PERSON> as versões deum item especifico</param>
        /// <param name="dto"></param>
        /// <returns></returns>
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> versoes, ImportarItemDTO dto)
        {
            ItemVersaoSpecificationHelper.GarantirIntegridadeDasVersoes(versoes, dto);

            // 1) Só se aplica a Autorização
            if (dto is null || dto.TipoMovimentoItem != TipoMovimentoItem.Autorizacao)
                return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(false, null);

            // 2) Sem versões? cria
            if (versoes is null || versoes.Count == 0)
                return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(true, null);

            // 3) Não existe versão para (data, fornecedor)? cria
            var cnpjFornecedor = Cnpj.Criar(dto.CnpjFornecedor); 
            var candidatas = ItemVersaoSpecificationHelper
                .ObterTodasVersoesDoFornecedorEAutorizacao(versoes, dto.DataAutorizacao, cnpjFornecedor);

            var deveCriar = !(candidatas?.Any() ?? false);
            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(deveCriar, null);
        }
    }
}
