using AutoMapper;
using Zin.Application.DTOs.Importacao;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Mappings
{
    public class ItemProfile : Profile
    {
        public ItemProfile()
        {
            CreateMap<ImportacaoAgregadorDTO, Agregador>();
            CreateMap<ImportacaoAgregadorDTO, Agregador>().ReverseMap();
            CreateMap<ImportarItemDTO, Item>();
            CreateMap<ImportarItemDTO, ImportarItemDTO>();
            CreateMap<ImportarDocumentoDTO, Documento>();
            CreateMap<ImportarDocumentoDTO, Documento>().ReverseMap();
        }
    }
}
