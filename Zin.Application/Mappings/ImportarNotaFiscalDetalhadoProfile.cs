using AutoMapper;
using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Mappings
{
    // ImportarNotaFiscalDetalhado : ImportarDocumentoDTO
    // Criar Profile ImportarNotaFiscalDetalhado
    public class ImportarNotaFiscalDetalhadoProfile: Profile
    {
        public ImportarNotaFiscalDetalhadoProfile()
        {
            CreateMap<ImportarDocumentoDTO, ImportarNotaFiscalDetalhado>()
                .AddTransform<DateTime>(dt =>
                    dt.Kind == DateTimeKind.Unspecified
                        ? DateTime.SpecifyKind(dt, DateTimeKind.Utc)
                        : dt.Kind == DateTimeKind.Local
                            ? dt.ToUniversalTime()
                            : dt)
                .ForMember(dest => dest.Fornecedor, opt => opt.Ignore());
        }
    }   
}
