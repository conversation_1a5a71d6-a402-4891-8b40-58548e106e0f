using AutoMapper;
using Zin.Application.DTOs.Agregadores;
using Zin.Application.DTOs.Comum;
using Zin.Application.DTOs.Documentos;
using Zin.Application.DTOs.Movimentacoes;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Enums;

namespace Zin.Application.Mappings
{
    public class ZinPagMappingProfile : Profile
    {
        public ZinPagMappingProfile()
        {
            CreateMap<Agregador, AgregadorDto>().ReverseMap();

            CreateMap<LiquidacaoRessarcimento, MovimentacaoLiquidacaoRessarcimentoDto>()
                .ForMember(dest => dest.DataRessarcimento, opt => opt.MapFrom(src => src.Data))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.Valor))
                .ForMember(dest => dest.TemComprovante, opt => opt.MapFrom(src => src.Ressarcimento.DocumentoRessarcimentos.Any()))
                .ForMember(dest => dest.DocumentoId, opt => opt.MapFrom(src => src.Ressarcimento.DocumentoRessarcimentos.Select(dr => dr.IdDocumento).FirstOrDefault()));

            CreateMap<Ressarcimento, MovimentacaoRessarcimentoDto>()
                .ForMember(dest => dest.DataCriacao, opt => opt.MapFrom(src => src.DataSolicitacao))
                .ForMember(dest => dest.ValorTotal, opt => opt.MapFrom(src => src.Valor))
                .ForMember(dest => dest.ValorARessarcir, opt => opt.MapFrom(src => src.Valor - src.LiquidacoesRessarcimentos.Sum(lr => lr.Valor)))
                .ForMember(dest => dest.DataProgramada, opt => opt.MapFrom(src => src.DataPagamento))
                .ForMember(dest => dest.Situacao, opt => opt.MapFrom(src => src.StatusRessarcimento))
                .ForMember(dest => dest.Liquidacoes, opt => opt.MapFrom(src => src.LiquidacoesRessarcimentos));

            CreateMap<Movimentacao, ObterMovimentacaoDetalhesDto>()
                .ForMember(dest => dest.IdMovimentacao, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.IdAgregador, opt => opt.MapFrom(src => src.Agregador.Id))
                .ForMember(dest => dest.NumeroSinistro, opt => opt.MapFrom(src => src.Agregador.Numero))
                .ForMember(dest => dest.Pagamentos, opt => opt.MapFrom(src => src.MovimentacoesItensVersoes.SelectMany(iv => iv.ItemVersao!.Pagamentos.Select(piv => piv.Pagamento)).Distinct()))
                .ForMember(dest => dest.Ressarcimentos, opt => opt.MapFrom(src => src.MovimentacoesItensVersoes.SelectMany(iv => iv.ItemVersao!.Ressarcimentos.Select(riv => riv.Ressarcimento)).Distinct()));

            CreateMap<Pessoa, FornecedorDto>()
                .ForMember(dest => dest.DadosBancarios, opt => opt.MapFrom(src => src.DadosBancarios.FirstOrDefault()));

            CreateMap<PessoaJuridica, FornecedorDto>()
                .IncludeBase<Pessoa, FornecedorDto>()
                .ForMember(dest => dest.NomeFantasia, opt => opt.MapFrom(src => src.NomeFantasia))
                .ForMember(dest => dest.RazaoSocial, opt => opt.MapFrom(src => src.RazaoSocial))
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => src.Cnpj));

            CreateMap<PessoaFisica, FornecedorDto>()
                .IncludeBase<Pessoa, FornecedorDto>()
                .ForMember(dest => dest.NomeFantasia, opt => opt.MapFrom(src => src.Nome))
                .ForMember(dest => dest.RazaoSocial, opt => opt.MapFrom(src => src.Nome))
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => src.Cpf));

            CreateMap<PessoaJuridica, OficinaDto>()
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NomeFantasia))
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => src.Cnpj));

            CreateMap<Veiculo, AtivoDto>()
                .ForMember(dest => dest.Ano, opt => opt.MapFrom(src => src.AnoFabricacao))
                .ForMember(dest => dest.SituacaoReparo, opt => opt.MapFrom(src => src.StatusVeiculo.ToString()));

            CreateMap<DadoBancario, DadosBancariosDto>();

            CreateMap<Documento, DocumentoDto>()
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => new EnumDto<TipoDocumento>(src.TipoDocumento)))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.ValorTotal))
                .ForMember(dest => dest.Serie, opt => opt.MapFrom(src => ""))
                                .ForMember(dest => dest.Chave, opt => opt.MapFrom(src => ""));

            CreateMap<LiquidacaoPagamento, MovimentacaoLiquidacaoDto>()
                .ForMember(dest => dest.DataPagamento, opt => opt.MapFrom(src => src.Data))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.ValorPago))
                .ForMember(dest => dest.TemComprovante, opt => opt.MapFrom(src => src.Pagamento.DocumentoPagamentos.Any()))
                .ForMember(dest => dest.DocumentoId, opt => opt.MapFrom(src => src.Pagamento.DocumentoPagamentos.Select(dp => dp.IdDocumento).FirstOrDefault()));

            CreateMap<Pagamento, MovimentacaoPagamentoDto>()
                .ForMember(dest => dest.DataCriacao, opt => opt.MapFrom(src => src.DataCriacao))
                .ForMember(dest => dest.ValorTotal, opt => opt.MapFrom(src => src.Valor))
                .ForMember(dest => dest.ValorAPagar, opt => opt.MapFrom(src => src.Valor - src.LiquidacoesPagamentos.Sum(l => l.ValorPago)))
                .ForMember(dest => dest.DataProgramada, opt => opt.MapFrom(src => src.DataPrevisao))
                .ForMember(dest => dest.Situacao, opt => opt.MapFrom(src => src.StatusPagamento))
                .ForMember(dest => dest.Liquidacoes, opt => opt.MapFrom(src => src.LiquidacoesPagamentos));

            CreateMap<Movimentacao, MovimentacaoDetalhesDto>()
                .ForMember(dest => dest.Itens, opt => opt.MapFrom(src => src.MovimentacoesItensVersoes.Select(miv => miv.ItemVersao)))
                .ForMember(dest => dest.Pagamentos, opt => opt.MapFrom(src => src.MovimentacoesItensVersoes.SelectMany(iv => iv.ItemVersao!.Pagamentos.Select(piv => piv.Pagamento)).Distinct()));

            CreateMap<Movimentacao, MovimentacaoDto>()
                .ForMember(dest => dest.IdAgregador, opt => opt.MapFrom(src => src.IdAgregador))
                .ForMember(dest => dest.NumeroAgregador, opt => opt.MapFrom(src => src.Agregador.Numero))
                .ForMember(dest => dest.DataAutorizacao, opt => opt.MapFrom(src => src.DataHoraAutorizacaoItem.ToString()))
                .ForMember(dest => dest.AtivoCodigo, opt => opt.MapFrom(src => ((Veiculo)src.Agregador.Ativos.FirstOrDefault()).Placa))
                .ForMember(dest => dest.AtivoStatus, opt => opt.MapFrom(src => ((Veiculo)src.Agregador.Ativos.FirstOrDefault()).StatusVeiculo))
                .ForMember(dest => dest.ValorPagar, opt => opt.MapFrom(src => src.ValorTotalAPagar))
                .ForMember(dest => dest.ValorPago, opt => opt.MapFrom(src => src.ValorTotalPago))
                .ForMember(dest => dest.ValorRessarcir, opt => opt.MapFrom(src => src.ValorTotalARessarcir))
                .ForMember(dest => dest.ValorRessarcido, opt => opt.MapFrom(src => src.ValorTotalRessarcido))
                .ForMember(dest => dest.Fornecedor, opt => opt.MapFrom(src => 
                    src.MovimentacoesItensVersoes.FirstOrDefault() != null 
                    ? src.MovimentacoesItensVersoes.First().ItemVersao!.PessoaFornecedora 
                    : null));

            CreateMap<Pessoa, MovimentacaoFornecedorDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));

            CreateMap<PessoaJuridica, MovimentacaoFornecedorDto>()
                    .IncludeBase<Pessoa, MovimentacaoFornecedorDto>()
                .ForMember(dest => dest.NomeFantasia, opt => opt.MapFrom(src => src.NomeFantasia))
                .ForMember(dest => dest.RazaoSocial, opt => opt.MapFrom(src => src.RazaoSocial))
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => src.Cnpj));
        }
    }
}
