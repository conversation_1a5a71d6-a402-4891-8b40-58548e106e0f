using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Application.DTOs.Agregadores;
using Zin.Application.DTOs.Importacao;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums;

namespace Zin.Application.Mappings
{
    public class ImportacaoMappingProfile : Profile
    {
        public ImportacaoMappingProfile()
        {
            CreateMap<ImportacaoAgregadorDTO, Agregador>()
                .ForMember(dest => dest.IdClienteEmpresa, opt => opt.Ignore())
                .ForMember(dest => dest.ClienteEmpresa, opt => opt.Ignore())
                .ForMember(dest => dest.Ativos, opt => opt.Ignore())
                .ForMember(dest => dest.Itens, opt => opt.Ignore())
                .ForMember(dest => dest.Movimentacoes, opt => opt.Ignore())
                .ForMember(dest => dest.TipoAgregador, opt => opt.MapFrom(src => (TipoAgregador)src.TipoAgregador!));
        }
    }
}
