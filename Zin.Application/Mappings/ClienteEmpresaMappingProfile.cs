using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Application.DTOs.Agregadores;
using Zin.Application.DTOs.Empresas;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;

namespace Zin.Application.Mappings
{
    public class ClienteEmpresaMappingProfile : Profile
    {
        public ClienteEmpresaMappingProfile()
        {
            // Mapeamento de criação de cliente empresa
            CreateMap<CriaClienteEmpresaDto, ClienteEmpresa>();

        }
    }
}
