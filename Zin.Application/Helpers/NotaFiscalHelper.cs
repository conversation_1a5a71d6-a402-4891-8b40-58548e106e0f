using Zin.Application.DTOs.Documentos;

namespace Zin.Application.Helpers
{
    public static class NotaFiscalHelper
    {
        public static Stream ParaStream(this CriaNotaFiscalDTO dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Base64))
                throw new InvalidOperationException("O campo Base64 da nota fiscal está vazio.");

            // Remove prefixo "data:...;base64," se existir
            var parts = dto.Base64.Split(',');
            var pureBase64 = parts.Length > 1 ? parts[1] : parts[0];

            var bytes = Convert.FromBase64String(pureBase64);

            return new MemoryStream(bytes); 
        }

        public static void SalvarArquivo(this CriaNotaFiscalDTO dto, string path)
        {
            using var stream = dto.ParaStream();
            using var fileStream = new FileStream(path, FileMode.Create, FileAccess.Write);
            stream.CopyTo(fileStream);
        }
    }
}
