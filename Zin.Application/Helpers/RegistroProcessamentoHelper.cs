using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Helpers
{
    public static class RegistroProcessamentoHelper
    {
        public static async Task RegistrarAsync(
            ItemVersao itemVersao,
            TipoProcessamento tipoProcessamento,
            StatusProcessamento statusProcessamento,
            string? detalhe,
            List<Divergencia>? divergencias,
            List<Condicao>? condicoes,
            IRegistroProcessamentoRepository? registroProcessamentoRepositorio,
            IItemVersaoRepository itemVersaoRepositorio
        )
        {
            StatusAtualizador.SetStatusProcessamentoItemVersao(itemVersao, tipoProcessamento, statusProcessamento);
            await itemVersaoRepositorio.AtualizarAsync(itemVersao);

            if (registroProcessamentoRepositorio == null) return;

            var registroExistente = await registroProcessamentoRepositorio.BuscarPorItemVersaoTipoAsync(itemVersao.Id, tipoProcessamento);

            if (registroExistente == null)
            {
                var registro = new RegistroProcessamentoItemVersao
                {
                    IdItemVersao = itemVersao.Id,
                    Tipo = tipoProcessamento,
                    Status = statusProcessamento,
                    Detalhe = detalhe,
                    DataAtualizacao = DateTime.UtcNow,
                    Divergencias = divergencias ?? [],
                    Condicoes = condicoes ?? [],
                };
                await registroProcessamentoRepositorio.InserirAsync(registro);
            }
            else if (
                registroExistente.Status != statusProcessamento ||
                registroExistente.Detalhe != detalhe ||
                registroExistente.Divergencias != divergencias ||
                registroExistente.Condicoes != condicoes ||
                registroExistente.Tipo != tipoProcessamento
            )
            {
                registroExistente.Status = statusProcessamento;
                registroExistente.Detalhe = detalhe;
                registroExistente.DataAtualizacao = DateTime.UtcNow;
                registroExistente.Divergencias = divergencias ?? [];
                registroExistente.Condicoes = condicoes ?? [];
                registroExistente.Tipo = tipoProcessamento;
                await registroProcessamentoRepositorio.AtualizarAsync(registroExistente);
            }
        }
    }
}