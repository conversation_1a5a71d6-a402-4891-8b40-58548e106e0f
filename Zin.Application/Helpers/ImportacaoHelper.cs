using FluentValidation;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador;
using Zin.Application.Shared.Retorno;

namespace Zin.Application.Helpers
{
    using global::Zin.Application.DTOs.Documentos;
    using System;
    using System.IO;

    namespace Zin.Application.Helpers
    {
        public static class ImportacaoHelper
        {
            private static readonly Lazy<IValidator<ImportacaoAgregadorDTO>> _validador =
                new(() => new ValidadorCriaImportacaoAgregador());

            public static ResultadoApp<Nada> ValidarImportacao(ImportacaoAgregadorDTO dto)
            {
                if (dto == null)
                    return ResultadoApp<Nada>.Falha(new ErroApp(TipoErroApp.Validacao, "DTO_NULO", "O DTO não pode ser nulo."));

                var resultado = _validador.Value.Validate(dto);

                if (resultado.IsValid)
                    return ResultadoApp<Nada>.OK(Nada.Valor);

                var erros = resultado
                                            .Errors
                                            .Select(e => new ErroApp(TipoErroApp.Validacao, e<PERSON>, e.ErrorMessage))
                                            .ToList();

                return ResultadoApp<Nada>.Falha(erros);
            }
        }
    }
}
