using Zin.Domain.Enums.Processos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Helpers
{
    public static class StatusAtualizador
    {
        private static readonly TipoProcessamento[] _tiposProcessamento = new[]
        {
            TipoProcessamento.ItemDuplicado,
            TipoProcessamento.PagamentoDuplicado,
            TipoProcessamento.Pagamento,
            TipoProcessamento.Ressarcimento
        };

        public static async Task AtualizarStatusGeralAgregadorSeConcluidoAsync(int idAgregador, IAgregadorRepository agregadorRepositorio, IItemRepository itemRepositorio, IItemVersaoRepository itemVersaoRepositorio)
        {
            var itens = await itemRepositorio.BuscarPorAgregadorIdAsync(idAgregador);
            var itensVersao = await itemVersaoRepositorio.BuscarPorAgregadorIdAsync(idAgregador);

            bool todosProcessados = true;
            bool algumDivergente = false;

            foreach (var item in itens)
            {
                if (_tiposProcessamento.Any(tipo => GetStatusProcessamentoItem(item, tipo) == StatusProcessamento.Divergente))
                {
                    algumDivergente = true;
                }
                if (_tiposProcessamento.Any(tipo => GetStatusProcessamentoItem(item, tipo) != StatusProcessamento.Processado &&
                                                    GetStatusProcessamentoItem(item, tipo) != StatusProcessamento.Divergente))
                {
                    todosProcessados = false;
                }
            }

            foreach (var itemVersao in itensVersao)
            {
                if (_tiposProcessamento.Any(tipo => GetStatusProcessamentoItemVersao(itemVersao, tipo) == StatusProcessamento.Divergente))
                {
                    algumDivergente = true;
                }
                if (_tiposProcessamento.Any(tipo => GetStatusProcessamentoItemVersao(itemVersao, tipo) != StatusProcessamento.Processado &&
                                                    GetStatusProcessamentoItemVersao(itemVersao, tipo) != StatusProcessamento.Divergente))
                {
                    todosProcessados = false;
                }
            }

            if (!todosProcessados)
                return;

            if (algumDivergente)
                await agregadorRepositorio.AtualizarStatusAsync(idAgregador, StatusProcessamento.Divergente);
            else
                await agregadorRepositorio.AtualizarStatusAsync(idAgregador, StatusProcessamento.Processado);
        }

        public static async Task AtualizarStatusGeralItemSeConcluidoAsync(int itemId, IItemRepository itemRepositorio, IItemVersaoRepository itemVersaoRepositorio)
        {
            var item = await itemRepositorio.BuscarPorIdAsync(itemId);
            if (item == null) return;

            var versoes = await itemVersaoRepositorio.BuscarPorItemIdAsync(item.Id);

            bool todasProcessadas = true;
            bool algumaDivergente = false;

            foreach (var tipo in _tiposProcessamento)
            {
                foreach (var versao in versoes)
                {
                    var status = GetStatusProcessamentoItemVersao(versao, tipo);
                    if (status != StatusProcessamento.Processado && status != StatusProcessamento.AProcessar)
                        algumaDivergente = true;

                    if (status == StatusProcessamento.AProcessar)
                        todasProcessadas = false;
                }
            }

            if (!todasProcessadas)
                return;

            var statusFinal = algumaDivergente ? StatusProcessamento.Divergente : StatusProcessamento.Processado;

            if (item.StatusProcessamento != statusFinal)
            {
                item.StatusProcessamento = statusFinal;
                await itemRepositorio.AtualizarAsync(item);
            }
        }

        public static StatusProcessamento GetStatusProcessamentoItem(Item item, TipoProcessamento tipo)
        {
            return tipo switch
            {
                TipoProcessamento.ItemDuplicado => item.StatusProcessamentoItemDuplicado,
                TipoProcessamento.PagamentoDuplicado => item.StatusProcessamentoPagamentoDuplicado,
                TipoProcessamento.Pagamento => item.StatusProcessamentoPagamento,
                TipoProcessamento.Ressarcimento => item.StatusProcessamentoRessarcimento,
                _ => StatusProcessamento.AProcessar
            };
        }
        public static StatusProcessamento GetStatusProcessamentoItemVersao(ItemVersao itemVersao, TipoProcessamento tipo)
        {
            return tipo switch
            {
                TipoProcessamento.ItemDuplicado => itemVersao.StatusProcessamentoItemDuplicado,
                TipoProcessamento.PagamentoDuplicado => itemVersao.StatusProcessamentoPagamentoDuplicado,
                TipoProcessamento.Pagamento => itemVersao.StatusProcessamentoPagamento,
                TipoProcessamento.Ressarcimento => itemVersao.StatusProcessamentoRessarcimento,
                _ => StatusProcessamento.AProcessar
            };
        }
        public static void SetStatusProcessamentoItemVersao(ItemVersao itemVersao, TipoProcessamento tipo, StatusProcessamento status)
        {
            switch (tipo)
            {
                case TipoProcessamento.ItemDuplicado: itemVersao.StatusProcessamentoItemDuplicado = status; break;
                case TipoProcessamento.PagamentoDuplicado: itemVersao.StatusProcessamentoPagamentoDuplicado = status; break;
                case TipoProcessamento.Pagamento: itemVersao.StatusProcessamentoPagamento = status; break;
                case TipoProcessamento.Ressarcimento: itemVersao.StatusProcessamentoRessarcimento = status; break;
            }
        }
    }
}