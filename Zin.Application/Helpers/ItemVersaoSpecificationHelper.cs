using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.ValueObject;

namespace Zin.Application.Helpers
{
    public static class ItemVersaoSpecificationHelper
    {
        public static string MensagemErroExclusaoSemAutorizacao(ImportarItemDTO itemDto)
        {
            return $"Não é possível criar uma versão de exclusão do item {itemDto.Codigo} " +
                   $"sem uma versão de autorização anterior para o fornecedor {itemDto.CnpjFornecedor} " +
                   $"com a data de autorização {itemDto.DataAutorizacao:dd/MM/yyyy}.";
        }

        public static IEnumerable<ItemVersao> ObterTodasVersoesDoFornecedorEAutorizacao
        (
             IEnumerable<ItemVersao>? versoes,
             DateTime dataAutorizacao,
             Cnpj cnpjFornecedor
        )
        {
            if (versoes is null || !versoes.Any()) return Enumerable.Empty<ItemVersao>();
            return versoes.Where(v => MesmoFornecedorEAutorizacao(v, dataAutorizacao.ToUniversalTime(), cnpjFornecedor)).ToList();
        }

        public static ItemVersao? ObterUltimaVersaoMesmaAutorizacao
        (
          IEnumerable<ItemVersao>? todasVersoes,
          DateTime dataAutorizacao,
          Cnpj cnpjFornecedor
        )
        {
            if (todasVersoes is null) return null;

            return todasVersoes
                .Where(v => MesmoFornecedorEAutorizacao(v, dataAutorizacao, cnpjFornecedor))
                .OrderByDescending(v => v.DataCriacao)
                .FirstOrDefault();
        }

        public static ItemVersao? ObterUltimaVersaoPorFornecedor(
            ICollection<ItemVersao> todasVersoes, 
            string cnpjFornecedor)
        {
            return todasVersoes
                .Where(v => (v.PessoaFornecedora as PessoaJuridica)!.Cnpj == cnpjFornecedor)
                .OrderByDescending(v => v.DataCriacao)
                .FirstOrDefault();
        }

        public static DeveCriarNovaVersaoResponse GeraDeveCriarNovaVersaoResponse(bool deveCriar, ItemVersao? versaoAnterior)
        {
            // Implementação real permanece a do seu projeto.
            return new DeveCriarNovaVersaoResponse(deveCriar, versaoAnterior);
        }

        private static bool MesmoFornecedorEAutorizacao(ItemVersao versao, DateTime dataAutorizacao, Cnpj cnpjFornecedor)
        {
            return
                versao.DataHoraAutorizacao?.ToUniversalTime() == dataAutorizacao.ToUniversalTime()
                && versao.PessoaFornecedora is PessoaJuridica pj
                && cnpjFornecedor.Equals(pj.Cnpj);
        }

        public static void GarantirIntegridadeDasVersoes(IEnumerable<ItemVersao> versoes, ImportarItemDTO dto)
        {
            if (dto is null) throw new ArgumentNullException(nameof(dto));

            if (string.IsNullOrWhiteSpace(dto.Codigo)) throw new ArgumentNullException(nameof(dto.Codigo), "Código do DTO é obrigatório.");
            if (string.IsNullOrWhiteSpace(dto.Descricao)) throw new ArgumentNullException(nameof(dto.Descricao), "Descrição do DTO é obrigatória.");
            if (string.IsNullOrWhiteSpace(dto.CnpjFornecedor)) throw new ArgumentNullException(nameof(dto.CnpjFornecedor), "CNPJ do fornecedor no DTO é obrigatório.");

            if (versoes != null && versoes.Any())
            {
                foreach (var versao in versoes)
                {
                    if (versao is null) throw new ArgumentException("Há uma versão nula na lista.", nameof(versoes));
                    if (versao.Item is null) throw new ArgumentException("Versão sem Item.", nameof(versoes));
                    if (string.IsNullOrWhiteSpace(versao.Item.Codigo)) throw new ArgumentException("Item sem Código.", nameof(versoes));
                    if (string.IsNullOrWhiteSpace(versao.Item.Descricao)) throw new ArgumentException("Item sem Descrição.", nameof(versoes));
                    if (versao.PessoaFornecedora is null) throw new ArgumentException("Versão sem Fornecedor associado.", nameof(versoes));
                }

                // Coerência com o DTO (mesmo item)
                var itemDivergente = versoes.Any(v => !v.Item!.Igual(dto.Codigo, dto.Descricao));

                if (itemDivergente)
                    throw new ArgumentException("As versões não pertencem ao mesmo item informado no DTO.", nameof(versoes));
            }
        }
    }
}
