using FluentValidation;
using System.Text.RegularExpressions;
using Zin.Application.DTOs.Importacao;
using Zin.Domain.Enums;

namespace Zin.Application.Helpers.Validador
{
    public class ValidadorContato : AbstractValidator<ImportarContatoDTO>
    {
        // helpers
        private static readonly Regex OnlyDigits = new Regex(@"\D", RegexOptions.Compiled);

        public ValidadorContato()
        {
            RuleFor(x => x.Nome)
                .NotEmpty().WithMessage("Informe o nome do contato para notificações.");

            // Meio de contato precisa ser válido
            RuleFor(x => x.<PERSON>o<PERSON>ontato)
                .IsInEnum().WithMessage("Meio de contato inválido.")
                .NotEqual(MeioDeContato.Desconhecido).WithMessage("Informe o meio de contato.");

            // Valor é obrigatório para qualquer meio
            RuleFor(x => x.Valor)
                .NotEmpty().WithMessage("Valor do contato é obrigatório.");

            // Regras específicas por meio
            When(x => x.MeioContato == MeioDeContato.Email, () =>
            {
                RuleFor(x => x.Valor)
                    .EmailAddress().WithMessage("E-mail inválido.");
            });

            When(x => x.MeioContato == MeioDeContato.Telefone, () =>
            {
                RuleFor(x => x.Valor)
                    .Must(ValorEhTelefoneFixoOuMovel)
                    .WithMessage("Telefone {PropertyValue} inválido. Use 10 ou 11 dígitos (com DDD) ou 8/9 dígitos (sem DDD).");
            });

            When(x => x.MeioContato == MeioDeContato.WhatsApp, () =>
            {
                RuleFor(x => x.Valor)
                    .Must(EhWhatsapp)
                    .WithMessage("WhatsApp {PropertyValue} inválido. Use 11 dígitos (DDD + 9XXXXXXXX) e inicie com 9.");
            });
        }

        // -------- helpers --------

        // Aceita: 
        // - sem DDD: 8 (fixo) ou 9 (celular) dígitos
        // - com DDD: 10 (fixo) ou 11 (celular) dígitos
        private static bool ValorEhTelefoneFixoOuMovel(string valor)
        {
            if (string.IsNullOrWhiteSpace(valor)) return false;
            var d = OnlyDigits.Replace(valor, "");

            if (d.Length == 8)
                return PrimeiroEntre('2', '3', '4', '5', d[0]);   // fixo local

            if (d.Length == 9)
                return d[0] == '9';   // móvel local (com 9)

            if (d.Length == 10)
            {
                // fixo com DDD (3º dígito entre 2 e 5)
                if (PrimeiroEntre('2', '3', '4', '5', d[2])) return true;

                // celular antigo com DDD (3º dígito entre 6 e 9)
                if (d[2] is >= '6' and <= '9') return true;

                return false;
            }

            if (d.Length == 11)
                return d[2] == '9';   // móvel atual com DDD (9XXXX-XXXX)

            return false;
        }

        private static bool EhWhatsapp(string valor)
        {
            if (string.IsNullOrWhiteSpace(valor)) return false;
            var d = OnlyDigits.Replace(valor, "");

            // Celular com DDD (padrão atual)
            if (d.Length == 11 && d[2] == '9') return true;

            // Celular sem o 9 (opcional, se você já adotou)
            if (d.Length == 10 && d[2] is >= '6' and <= '9') return true;

            // FIXO com DDD (permitir WhatsApp Business em número fixo)
            if (d.Length == 10 && d[2] is >= '2' and <= '5') return true;

            return false;
        }

        private static bool PrimeiroEntre(char a, char b, char c, char d, char ch)
        {
            return ch == a || ch == b || ch == c || ch == d;
        }
    }
}
