using FluentValidation;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador.Extension;

namespace Zin.Application.Helpers.Validador
{
    public class ValidadorVeiculo : AbstractValidator<ImportarAtivoVeiculoDTO>
    {
        /* Criar Validador Veículo */
        public ValidadorVeiculo()
        {
            RuleFor(v => v.Placa)
                .Placa();
            //RuleFor(v => v.<PERSON><PERSON>)
            //    .Cha<PERSON>();
            RuleFor(v => v.Modelo)
                .NotEmpty().WithMessage(ValidadorMensagem.ModeloObrigatorio);
            RuleFor(v => v.<PERSON>a)
                .NotEmpty().WithMessage(ValidadorMensagem.MarcaObrigatoria);
            RuleFor(v => v.AnoModelo)
                .NotEmpty().WithMessage(ValidadorMensagem.AnoModeloObrigatorio);
            RuleFor(v => v.CnpjOficina)
                .Cnpj();
        }
    }
}
