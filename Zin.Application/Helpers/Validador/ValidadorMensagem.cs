
namespace Zin.Application.Helpers.Validador
{
    public static class ValidadorMensagem
    {
        public static readonly string CnpjObrigatorio = "CNPJ obrigatório.";
        public static readonly string CnpjInvalido = "CNPJ inválido.";

        public static readonly string RazaoSocialObrigatoria = "Razão Social obrigatória.";
        public static readonly string RazaoSocialMinimo3Caracteres = "Razão Social deve ter pelo menos 3 caracteres.";

        public static readonly string EmailInvalido = "Email inválido.";

        public static readonly string RazaoSocialDeTerNoMaximo100Caracteres = "Razão Social deve ter no máximo 100 caracteres.";
        public static readonly string NomeFantasiaObrigatorio = "Nome Fantasia obrigatório.";
    
        public static readonly string EnderecoObrigatorio = "Endereço obrigatório.";
        public static readonly string CepInvalido = "CEP {PropertyValue} inválido.";

        public static readonly string PlacaObrigatoria = "Placa obrigatória.";
        public static readonly string PlacaInvalida = "Placa inválida.";

        public static readonly string ChassiObrigatorio = "Chassi obrigatório.";
        public static readonly string MarcaObrigatoria = "Marca obrigatória.";

        public static readonly string ModeloObrigatorio = "Modelo obrigatório.";

        public static readonly string AnoModeloObrigatorio = "Ano Modelo obrigatório.";

        public static readonly string TitularObrigatorio = "Titular obrigatório.";
    }
}
