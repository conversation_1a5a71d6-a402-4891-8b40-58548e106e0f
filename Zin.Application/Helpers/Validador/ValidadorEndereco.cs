using FluentValidation;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador.Extension;

namespace Zin.Application.Helpers.Validador
{
    public class ValidadorEndereco : AbstractValidator<ImportarEnderecoDTO>
    {
        public ValidadorEndereco()
        {
            RuleFor(x => x.<PERSON>)
                .NotEmpty().WithMessage("Logradouro obrigatório.")
                .MaximumLength(100);

            RuleFor(x => x.Numero)
                .NotEmpty().WithMessage("Número obrigatório.")
                .MaximumLength(10);

            RuleFor(x => x.CodIbgeCidade)
                .NotEmpty().WithMessage("Código Ibge da Cidade obrigatório.")
                .MaximumLength(50);

            RuleFor(x => x.Cep)
                .Cep();
        }
    }
}
