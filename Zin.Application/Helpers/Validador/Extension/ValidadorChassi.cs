using FluentValidation;
using System.Text.RegularExpressions;

namespace Zin.Application.Helpers.Validador.Extension
{
    public static class ValidadorChassi
    {
        private static readonly Regex VinRegex = new Regex("^[A-HJ-NPR-Z0-9]{17}$", RegexOptions.Compiled);

        public static IRuleBuilderOptions<T, string> Chassi<T>(this IRuleBuilder<T, string> ruleBuilder)
        {
            return ruleBuilder
                .NotEmpty().WithMessage(ValidadorMensagem.ChassiObrigatorio);
                //.Must(c => VinRegex.IsMatch(c))
                //.WithMessage("Chassi inválido. Deve ter 17 caracteres alfanuméricos (exceto I, O e Q).");
        }
    }
}
