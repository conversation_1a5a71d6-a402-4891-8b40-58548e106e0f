using FluentValidation;
using System.Text.RegularExpressions;

namespace Zin.Application.Helpers.Validador.Extension
{
    public static class ValidadorPlaca
    { 
        public static IRuleBuilderOptions<T, string?> Placa<T>(
            this IRuleBuilder<T, string?> ruleBuilder,
            bool obrigatorio = true)
        {
            var rb = ruleBuilder;
            if (obrigatorio)
                rb = rb.NotEmpty().WithMessage(ValidadorMensagem.PlacaObrigatoria);

            return rb.Must(Domain.ValueObject.Placa.EhValida)
                     .WithMessage(ValidadorMensagem.PlacaInvalida);
        }
    }
}
