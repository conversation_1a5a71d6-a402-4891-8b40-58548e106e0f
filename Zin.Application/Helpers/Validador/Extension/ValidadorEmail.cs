
using FluentValidation;

namespace Zin.Application.Helpers.Validador.Extension
{
    public static class ValidadorEmail
    {
        public static IRuleBuilderOptions<T, string?> Email<T>(this IRuleBuilder<T, string?> ruleBuilder)
        {
            return ruleBuilder
                .NotEmpty().WithMessage(ValidadorMensagem.EmailInvalido)   // mensagem genérica
                .EmailAddress().WithMessage(ValidadorMensagem.EmailInvalido);
        }
    }
}
