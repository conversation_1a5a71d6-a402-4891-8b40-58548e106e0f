using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace Prismatec.Integracao.Domain.Shared.Extensions
{
    public static class ValidatorExtensions
    {
        public static bool CepIsValid(string cep, out string erro)
        {
            erro = string.Empty;

            if (string.IsNullOrWhiteSpace(cep))
            {
                erro = "O CEP não pode estar em branco.";
                return false;
            }

            cep = cep.Trim();

            // Aceita com ou sem hífen (00000-000 ou 00000000)
            var regex = new System.Text.RegularExpressions.Regex(@"^\d{5}-?\d{3}$");
            if (!regex.IsMatch(cep))
            {
                erro = "Formato de CEP inválido. Use 00000-000 ou 00000000.";
                return false;
            }

            return true;
        }

        public static bool CepIsValid(string cep)
        {
            cep = cep.Trim();
            // Aceita com ou sem hífen (00000-000 ou 00000000)
            var regex = new System.Text.RegularExpressions.Regex(@"^\d{5}-?\d{3}$");
            return !string.IsNullOrWhiteSpace(cep) && regex.IsMatch(cep);

        }

        public static bool CNPJIsValid(string cnpj)
        {
            if (string.IsNullOrWhiteSpace(cnpj))
                return false;

            // Remove caracteres não numéricos
            cnpj = new string(cnpj.Where(char.IsDigit).ToArray());

            if (cnpj.Length != 14)
                return false;

            // Verifica se todos os dígitos são iguais (inválido)
            if (cnpj.Distinct().Count() == 1)
                return false;

            var multiplicador1 = new int[12] { 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            var multiplicador2 = new int[13] { 6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };

            var tempCnpj = cnpj.Substring(0, 12);
            int soma = 0;

            for (int i = 0; i < 12; i++)
                soma += int.Parse(tempCnpj[i].ToString()) * multiplicador1[i];

            int resto = soma % 11;
            var digito1 = resto < 2 ? 0 : 11 - resto;

            tempCnpj += digito1;
            soma = 0;

            for (int i = 0; i < 13; i++)
                soma += int.Parse(tempCnpj[i].ToString()) * multiplicador2[i];

            resto = soma % 11;
            var digito2 = resto < 2 ? 0 : 11 - resto;

            return cnpj.EndsWith($"{digito1}{digito2}");
        }
        private static readonly Regex TelefoneRegex = new Regex(@"^\(?\d{2}\)?[\s-]?\d{4,5}[\s-]?\d{4}$");
        public static bool TelefoneIsValid(string telefone)
        {
            if (string.IsNullOrWhiteSpace(telefone))
                return false;
            telefone = telefone.Trim();

            return TelefoneRegex.IsMatch(telefone);
        }
        //UF
        private static readonly Dictionary<string, string> Estados = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "AC", "Acre" },
            { "AL", "Alagoas" },
            { "AP", "Amapá" },
            { "AM", "Amazonas" },
            { "BA", "Bahia" },
            { "CE", "Ceará" },
            { "DF", "Distrito Federal" },
            { "ES", "Espírito Santo" },
            { "GO", "Goiás" },
            { "MA", "Maranhão" },
            { "MT", "Mato Grosso" },
            { "MS", "Mato Grosso do Sul" },
            { "MG", "Minas Gerais" },
            { "PA", "Pará" },
            { "PB", "Paraíba" },
            { "PR", "Paraná" },
            { "PE", "Pernambuco" },
            { "PI", "Piauí" },
            { "RJ", "Rio de Janeiro" },
            { "RN", "Rio Grande do Norte" },
            { "RS", "Rio Grande do Sul" },
            { "RO", "Rondônia" },
            { "RR", "Roraima" },
            { "SC", "Santa Catarina" },
            { "SP", "São Paulo" },
            { "SE", "Sergipe" },
            { "TO", "Tocantins" }
        };

        public static bool UFIsValid(string ufOuNome)
        {
            if (string.IsNullOrWhiteSpace(ufOuNome))
                return false;

            var input = ufOuNome.Trim().ToUpperInvariant();

            return Estados.ContainsKey(input) || Estados.Values.Any(nome => string.Equals(nome, ufOuNome, StringComparison.OrdinalIgnoreCase));
        }
        //placas
        private static readonly Regex PlacaAntigaRegex = new Regex(@"^[A-Z]{3}-\d{4}$", RegexOptions.IgnoreCase);
        private static readonly Regex PlacaMercosulRegex = new Regex(@"^[A-Z]{3}\d[A-Z]\d{2}$", RegexOptions.IgnoreCase);

        public static bool PlacaIsValid(string placa)
        {
            if (string.IsNullOrWhiteSpace(placa))
                return false;

            placa = placa.Trim().ToUpper();

            return PlacaAntigaRegex.IsMatch(placa) || PlacaMercosulRegex.IsMatch(placa);
        }
        //ExtensoesFotos
        private static readonly string[] ExtensoesFotosVeiculoPermitidas = new[] { ".jpg", ".jpeg", ".png" };
        public static bool IsValidExtensoesFotosVeiculo(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return false;
            return ExtensoesFotosVeiculoPermitidas.Contains(Path.GetExtension(fileName).ToLower());
        }
    }
}
