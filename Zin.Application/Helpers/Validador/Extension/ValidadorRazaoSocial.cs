using FluentValidation;

namespace Zin.Application.Helpers.Validador.Extension
{
    public static class ValidadorRazaoSocial 
    {
        public static IRuleBuilder<T, string> RazaoSocial<T>(this IRuleBuilder<T, string> ruleBuilder)
        {
            return ruleBuilder
                .NotEmpty()
                    .WithMessage(ValidadorMensagem.RazaoSocialObrigatoria)

                .MinimumLength(3)
                    .WithMessage(ValidadorMensagem.RazaoSocialMinimo3Caracteres)

                .MaximumLength(100)
                    .WithMessage(ValidadorMensagem.RazaoSocialDeTerNoMaximo100Caracteres);
        }
    }
}
