using FluentValidation;
using Zin.Helpers;

namespace Zin.Application.Helpers.Validador.Extension
{
    public static class ValidadorCEP
    {
        public static IRuleBuilderOptions<T, string?> Cep<T>(
            this IRuleBuilder<T, string?> ruleBuilder,
            bool obrigatorio = true)
        {
            var rb = ruleBuilder;

            if (obrigatorio)
                rb = rb.NotEmpty().WithMessage(ValidadorMensagem.CepInvalido);

            return rb.Must(Domain.ValueObject.Cep.EhValido)
                .WithMessage(ValidadorMensagem.CepInvalido);
        }
    }
}
