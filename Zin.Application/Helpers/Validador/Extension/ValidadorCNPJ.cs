using FluentValidation;

namespace Zin.Application.Helpers.Validador.Extension
{
    public static class ValidadorCNPJ
    {
        public static IRuleBuilderOptions<T, string?> Cnpj<T>(this IRuleBuilder<T, string?> ruleBuilder)
        {
            return ruleBuilder
                .NotEmpty().WithMessage(ValidadorMensagem.CnpjObrigatorio)
                .Must(Domain.ValueObject.Cnpj.EhValido).WithMessage(ValidadorMensagem.CnpjInvalido);
        }
    }
}
