using FluentValidation;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador.Extension;

namespace Zin.Application.Helpers.Validador
{
    public class ValidadorFornecedor : AbstractValidator<ImportarPessoaJuridicaDTO>
    {
        public ValidadorFornecedor()
        {
            RuleFor(x => x.RazaoSocial)
                .RazaoSocial();

            RuleFor(x => x.NomeFantasia)
                .NomeFantasia();

            RuleFor(x => x.Cnpj)
                .Cnpj();

            // TODO: (TITI) TRANCAR IMPORTAÇÃO POR CAUSA DE CONTATO OU ENDEREÇO -> ZIN = SISTEMA PAGAMENTO
            //RuleFor(x => x.Endereco)
            //    .NotNull().WithMessage(ValidadorMensagem.EnderecoObrigatorio)
            //    .SetValidator(new ValidadorEndereco());

            //RuleFor(x => x.Contatos)
            //    .Must(c => c != null && c.Count > 0)
            //    .ForEach(contato => contato.SetValidator(new ValidadorContato()));

            RuleFor(x => x.DadosBancarios)
                .Must(d => d != null && d.Count > 0).WithMessage("Deve haver ao menos um dado bancário.")
                .ForEach(dado => dado.SetValidator(new ValidadorDadosBancarios()));
        }
    }
}
