using FluentValidation;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador.Extension;

namespace Zin.Application.Helpers.Validador
{
    public class ValidadorClienteEmpresa : AbstractValidator<ImportarClienteEmpresaDTO>
    {
        public ValidadorClienteEmpresa()
        {
            RuleFor(x => x.Cnpj)
                .Cnpj();

            RuleFor(x => x.RazaoSocial)
                .RazaoSocial();

            RuleFor(x => x.NomeFantasia)
                .NomeFantasia();

            RuleFor(x => x.Endereco)
                .SetValidator(new ValidadorEndereco());
        }
    }
}
