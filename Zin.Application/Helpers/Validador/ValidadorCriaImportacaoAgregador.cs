using FluentValidation;
using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Helpers.Validador
{
    public sealed class ValidadorCriaImportacaoAgregador : AbstractValidator<ImportacaoAgregadorDTO>
    {
        public ValidadorCriaImportacaoAgregador()
        {
            RuleFor(x => x.TipoAgregador)
                .NotNull().WithMessage("Tipo Agregador obrigatório.")
                .IsInEnum();

            RuleFor(x => x.IdCliente)
                .NotEmpty().WithMessage("Id Cliente obrigatório.")
                .Must(id => Guid.TryParse(id, out _))
                .WithMessage("Id Cliente deve ser um GUID válido.");

            RuleFor(x => x.Numero)
                .MaximumLength(100)
                .When(x => !string.IsNullOrWhiteSpace(x.Numero))
                .WithMessage("Numero deve ter no máximo 100 caracteres.");

            When(x => x.ClienteEmpresa is not null, () =>
            {
                RuleFor(x => x.ClienteEmpresa!)
                    .SetValidator(new ValidadorClienteEmpresa());
            });

            RuleFor(x => x.Ativos)
                .Must(list => list?.Veiculos.Count > 0)
                .WithMessage("Ativos: deve haver ao menos um item quando a coleção for informada.")
                .Must(list => list == null || list.Veiculos.All(a => a != null))
                .WithMessage("Ativos: não pode conter itens nulos.");
            //.ForEach(a =>
            //{
            //    a.RuleFor(x => x.Veiculos)
            //        .Must(v => v == null || v.Count > 0)
            //        .WithMessage("Veiculos: deve haver ao menos um item quando a coleção for informada.")
            //        .ForEach(v => v.SetValidator(new ValidadorVeiculo()));
            //});

            RuleFor(x => x.Fornecedores)
                .Must(list => list?.Count > 0)
                .WithMessage("Fornecedor obrigatório.")
                .ForEach(f => f.SetValidator(new ValidadorFornecedor()));

            RuleFor(x => x.Oficinas)
                .Must(list => list?.Count > 0)
                .WithMessage("Oficina obrigatória.")
                .ForEach(o => o.SetValidator(new ValidadorOficina()));

            RuleFor(x => x.Itens)
                .Must(list => list?.Count > 0)
                .WithMessage("Item obrigatório.")
                .ForEach(i => i.SetValidator(new ValidadorImportarItens()));
        }
    }
}
