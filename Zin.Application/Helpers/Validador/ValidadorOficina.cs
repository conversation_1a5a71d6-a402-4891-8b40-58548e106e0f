using FluentValidation;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador.Extension;

namespace Zin.Application.Helpers.Validador
{
    public class ValidadorOficina : AbstractValidator<ImportarPessoaJuridicaDTO>
    {
        public ValidadorOficina()
        {
            RuleFor(x => x.RazaoSocial)
                .RazaoSocial();

            RuleFor(x => x.NomeFantasia)
                .NomeFantasia();

            RuleFor(x => x.Cnpj)
                .Cnpj();

            // TODO: (TITI) TRANCAR IMPORTAÇÃO POR CAUSA DE ENDEREÇO -> ZIN = SISTEMA PAGAMENTO
            //RuleFor(x => x.Endereco)
            //    .NotNull().WithMessage(ValidadorMensagem.EnderecoObrigatorio)
            //    .SetValidator(new ValidadorEndereco());
        }
    }
}
