using FluentValidation;
using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Helpers.Validador
{
    public class ValidadorDadosBancarios : AbstractValidator<ImportarDadosBancariosZinDTO>
    {
        public ValidadorDadosBancarios()
        {
            RuleFor(x => x.BancoCodigo)
                .NotEmpty().WithMessage("Código banco obrigatório.");

            RuleFor(x => x.BancoNome)
                .NotEmpty().WithMessage("Nome banco obrigatório.");

            RuleFor(x => x.BancoAgencia)
                .NotEmpty().WithMessage("Agência banco obrigatória.");

            RuleFor(x => x.BancoConta)
                .NotEmpty().WithMessage("Conta banco obrigatória.");

            //RuleFor(x => x.BancoCpfCnpjTitular)
            //    .NotEmpty().WithMessage(ValidadorMensagem.TitularObrigatorio);
        }
    }
}
