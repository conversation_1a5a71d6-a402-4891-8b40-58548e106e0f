using FluentValidation;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers.Validador.Extension;

namespace Zin.Application.Helpers.Validador
{
    public sealed class ValidadorImportarItens : AbstractValidator<ImportarItemDTO>
    {
        public ValidadorImportarItens()
        {
            RuleFor(x => x.TipoMovimentoItem).IsInEnum();
            RuleFor(x => x.TipoItem).IsInEnum();

            RuleFor(x => x.IdItemPedidoFornecedor)
                .GreaterThan(0).WithMessage("IdItemPedidoFornecedor deve ser maior que zero.");

            RuleFor(x => x.IdFornecedor)
                .GreaterThan(0).WithMessage("IdFornecedor deve ser maior que zero.");

            RuleFor(x => x.Codigo)
               .NotEmpty()
               .WithMessage("Codigo Peça obrigatório.");

            RuleFor(x => x.Descricao)
                .NotEmpty()
                .WithMessage("Descrição Peça obrigatória.");

            RuleFor(x => x.CnpjFornecedor)
                .Cnpj();

            RuleFor(x => x.CnpjOficina)
                .Cnpj();

            RuleFor(x => x.VeiculoPlaca)
                .Placa();

            RuleFor(x => x.DataCriacao)
                .Must(d => d > DateTime.MinValue).WithMessage("DataCriacao é obrigatória.");

            RuleFor(x => x.DataAutorizacao)
                .Must(d => d > DateTime.MinValue).WithMessage("DataAutorizacao é obrigatória.")
                .GreaterThanOrEqualTo(x => x.DataCriacao).WithMessage("DataAutorizacao deve ser ≥ DataCriacao.");

            RuleFor(x => x.DataMovimento)
                .Must(d => d > DateTime.MinValue).WithMessage("DataMovimento é obrigatória.")
                .GreaterThanOrEqualTo(x => x.DataAutorizacao).WithMessage("DataMovimento deve ser ≥ DataAutorizacao.");

            RuleFor(x => x.Quantidade)
                .GreaterThan(0).WithMessage("Quantidade deve ser maior que zero.");

            RuleFor(x => x.ValorUnitario)
                .GreaterThanOrEqualTo(0m).WithMessage("ValorUnitario não pode ser negativo.");

            RuleFor(x => x.ValorTotal)
                .GreaterThanOrEqualTo(0m).WithMessage("ValorTotal não pode ser negativo.");

            RuleFor(x => x)
                .Must(ValorTotalConsistente)
                .WithMessage("ValorTotal deve ser igual a Quantidade × ValorUnitario (arredondado a 2 casas).");

            RuleFor(x => x.DocumentosRelacionados)
                .Must(list => list == null || list.All(d => d != null))
                .WithMessage("DocumentosRelacionados não pode conter itens nulos.");

            // Se você tiver um ImportarDocumentoDTOValidator, pode habilitar:
            // RuleForEach(x => x.DocumentosRelacionados).SetValidator(new ImportarDocumentoDTOValidator());
        }

        private static bool ValorTotalConsistente(ImportarItemDTO dto)
        {
            var esperado = decimal.Round(dto.Quantidade * dto.ValorUnitario, 2, MidpointRounding.AwayFromZero);
            var total = decimal.Round(dto.ValorTotal, 2, MidpointRounding.AwayFromZero);
            return total == esperado;
        }
    }
}
