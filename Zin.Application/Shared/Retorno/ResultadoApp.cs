namespace Zin.Application.Shared.Retorno
{
    public sealed class ResultadoApp<T>
    {
        private ResultadoApp(bool sucesso, T? conteudo, List<ErroApp>? erros)
        {
            Sucesso = sucesso;
            Conteudo = conteudo;
            Erros = erros;
        }

        public bool Sucesso { get; }
        public T? Conteudo { get; }
        public IReadOnlyList<ErroApp>? Erros { get; }

        public static ResultadoApp<T> OK(T conteudo) 
            => new(true, conteudo, null);

        public static ResultadoApp<T> Falha(ErroApp erro) 
            => new(false, default, new List<ErroApp> { erro });

        public static ResultadoApp<T> Falha(IEnumerable<ErroApp>? erros) 
            => new(false, default, erros.ToList());
    }
}
