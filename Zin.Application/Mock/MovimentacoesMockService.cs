using Zin.Application.DTOs.Movimentacoes;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;

namespace Zin.Application.Mock
{
    public class MovimentacoesMockService 
    {
        public Task<ObterMovimentacaoDetalhesDto?> ObterDetalhesMovimentacaoAsync(int idMovimentacao)
        {
            var detalhe = MovimentacoesMockDb
                .MovimentacoesDetalhes
                .FirstOrDefault(m => m.IdMovimentacao == idMovimentacao);

            return Task.FromResult(detalhe);
        }

        public Task<List<MovimentacaoDto>> ListarMovimentacoesAsync()
        {
            return Task.FromResult(MovimentacoesMockDb.Movimentacoes);
        }

        public Task<List<ItemAgregadorDto>> ObterItensOutrasMovimentacoesAsync(int idAgregador, int movId)
        {
            return Task.FromResult(new List<ItemAgregadorDto>());
        }

        public Task<List<ItemAgregadorDto>> ObterItensMesmaMovimentacaoAsync(int idAgregador, int movId)
        {
            return Task.FromResult(new List<ItemAgregadorDto>());
        }
    }
}
