using Zin.Application.DTOs.Movimentacoes;
using Zin.Application.DTOs.Paineis;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Extension;
using Zin.Domain.Enums.Processos;

namespace Zin.Application.Mock
{
    // Classe estática para centralizar a lista de movimentações em memória
    public static class MovimentacoesMockDb
    {
        public static List<MovimentacaoDto> Movimentacoes { get; } = new();
        public static List<ObterMovimentacaoDetalhesDto> MovimentacoesDetalhes { get; } = new();

        //private static readonly PainelPrincipalDto _painelPrincipalDto;
        //public static PainelPrincipalDto PainelPrincipalDto => _painelPrincipalDto;

        //static MovimentacoesMockDb()
        //{
        //    _movimentacoes = MontarListaMovimentacoes();
        //    _movimentacoesDetalhes = MontarListaDeDetalhesDeMovimentacoes();
        //    _painelPrincipalDto = MontarPainelPrincipal();
        //}

        //private static List<MovimentacaoDto> MontarListaMovimentacoes()
        //{
        //    return
        //    [
        //        #region Sinistro 12.22.100300.03
                
        //        new()
        //        {
        //            IdMovimentacao = 9,
        //            IdAgregador = 1001,
        //            NumeroAgregador = "12.22.100300.03",
        //            Fornecedor = BuscaFornecedor(2002),
        //            DataAutorizacao = "2024-06-03",
        //            DocumentoId = 3003,
        //            DocumentoNumero = "NF-3003",
        //            Valor = 6707.81m,
        //            Situacao = new SituacaoDto<StatusProcessamento> { Valor = StatusProcessamento.Divergente },
        //            AtivoCodigo = "GHI-0003",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosSuspensosFaltaPeca },
        //            Divergencias = [ new DivergenciaDto { Valor = Divergencia.PossivelItemDuplicado }],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },

        //        #endregion

        //        #region Sinistro 13.23.100400.04

        //        new()
        //        {
        //            IdMovimentacao = 10,
        //            IdAgregador = 1002,
        //            NumeroAgregador = "13.23.100400.04",
        //            Fornecedor = BuscaFornecedor(2003),
        //            DataAutorizacao = "2024-06-04",
        //            DocumentoId = null,
        //            DocumentoNumero = null,
        //            Valor = 6707.81m,
        //            Situacao = new SituacaoDto<StatusProcessamento> { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "JKL4M26",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosSuspensosFaltaPeca },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },

        //        #endregion

        //        #region Sinistro 11.21.100200.09
                
        //        new()
        //        {
        //            IdMovimentacao = 3,
        //            IdAgregador = 1003,
        //            NumeroAgregador = "11.21.100200.09",
        //            Fornecedor = BuscaFornecedor(2000),
        //            DataAutorizacao = "2024-06-14",
        //            DocumentoId = null,
        //            DocumentoNumero = null,
        //            Valor = 9843.75m,
        //            Situacao = new SituacaoDto<StatusProcessamento> { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "ABC-0014",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosSuspensosFaltaPeca },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },
        //        new()
        //        {
        //            IdMovimentacao = 5,
        //            IdAgregador = 1003,
        //            NumeroAgregador = "11.21.100200.09",
        //            Fornecedor = BuscaFornecedor(2001),
        //            DataAutorizacao = "2024-06-09",
        //            DocumentoId = 3009,
        //            DocumentoNumero = "NF-3009",
        //            Valor = 7031.25m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "DEF2G14",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.EmReparos },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },
        //        new()
        //        {
        //            IdMovimentacao = 6,
        //            IdAgregador = 1003,
        //            NumeroAgregador = "11.21.100200.09",
        //            Fornecedor = BuscaFornecedor(2001),
        //            DataAutorizacao = "2024-06-10",
        //            DocumentoId = null,
        //            DocumentoNumero = null,
        //            Valor = 7312.50m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "DEF2G15",
        //            AtivoStatus = new SituacaoDto < StatusVeiculo > { Valor = StatusVeiculo.ReparosSuspensosFaltaPeca },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },

        //        #endregion
                
        //        #region Sinistro 11.21.100200.11
        //        new()
        //        {
        //            IdMovimentacao = 7,
        //            IdAgregador = 1004,
        //            NumeroAgregador = "11.21.100200.11",
        //            Fornecedor = BuscaFornecedor(2001),
        //            DataAutorizacao = "2024-06-11",
        //            DocumentoId = 3011,
        //            DocumentoNumero = "NF-3011",
        //            Valor = 7593.75m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "DEF2G16",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.EmReparos },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },
        //        new()
        //        {
        //            IdMovimentacao = 8,
        //            IdAgregador = 1004,
        //            NumeroAgregador = "11.21.100200.11",
        //            Fornecedor = BuscaFornecedor(2001),
        //            DataAutorizacao = "2024-06-12",
        //            DocumentoId = 3012,
        //            DocumentoNumero = "NF-3012",
        //            Valor = 7875.00m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "DEF2G17",
        //            AtivoStatus = new SituacaoDto < StatusVeiculo > { Valor = StatusVeiculo.ReparosSuspensosFaltaPeca },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },
        //        #endregion

        //        #region Sinistro 10.20.100100.13

        //        new()
        //        {
        //            IdMovimentacao = 1,
        //            IdAgregador = 1005,
        //            NumeroAgregador = "10.20.100100.13",
        //            Fornecedor = BuscaFornecedor(2000),
        //            DataAutorizacao = "2024-06-01",
        //            DocumentoId = 3001,
        //            DocumentoNumero = "NF-3001",
        //            Valor = 6707.81m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "ABC-0001",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.EmReparos },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },
        //        new()
        //        {
        //            IdMovimentacao = 2,
        //            IdAgregador = 1005,
        //            NumeroAgregador = "10.20.100100.13",
        //            Fornecedor = BuscaFornecedor(2000),
        //            DataAutorizacao = "2024-06-13",
        //            DocumentoId = 3013,
        //            DocumentoNumero = "NF-3013",
        //            Valor = 8437.50m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "ABC-0013",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.EmReparos },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },
        //        new()
        //        {
        //            IdMovimentacao = 4,
        //            IdAgregador = 1005,
        //            NumeroAgregador = "10.20.100100.13",
        //            Fornecedor = BuscaFornecedor(2001),
        //            DocumentoId = null,
        //            DocumentoNumero = null,
        //            Valor = 6707.81m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "DEF2G13",
        //            AtivoStatus = new SituacaoDto < StatusVeiculo > { Valor = StatusVeiculo.ReparosSuspensosFaltaPeca },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },

        //        #endregion

        //        #region Sinistro 13.23.100400.15
                
        //        new()
        //        {
        //            IdMovimentacao = 11,
        //            IdAgregador = 1006,
        //            NumeroAgregador = "13.23.100400.15",
        //            Fornecedor = BuscaFornecedor(2003),
        //            DataAutorizacao = "2024-06-15",
        //            DocumentoId = 3015,
        //            DocumentoNumero = "NF-3015",
        //            Valor = 10125.00m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "JKL4M27",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosConcluidosAguardandoSegurado },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.MenosDe35DiasEntrega },
        //            ]
        //        },

        //        #endregion

        //        #region Sinistro 13.23.100400.16

        //        new()
        //        {
        //            IdMovimentacao = 12,
        //            IdAgregador = 1007,
        //            NumeroAgregador = "13.23.100400.16",
        //            Fornecedor = BuscaFornecedor(2003),
        //            DataAutorizacao = "2024-06-16",
        //            DocumentoId = 3016,
        //            DocumentoNumero = "NF-3016",
        //            Valor = 10406.25m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "JKL4M28",
        //            AtivoStatus = new SituacaoDto < StatusVeiculo > { Valor = StatusVeiculo.ReparosSuspensosFaltaPeca },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },

        //        #endregion

        //        #region Sinistro 13.23.100400.25

        //        new()
        //        {
        //            IdMovimentacao = 13,
        //            IdAgregador = 1008,
        //            NumeroAgregador = "13.23.100400.25",
        //            Fornecedor = BuscaFornecedor(2003),
        //            DataAutorizacao = "2024-06-17",
        //            DocumentoId = 3017,
        //            DocumentoNumero = "NF-3017",
        //            Valor = 10687.50m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "JKL4M29",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosConcluidosAguardandoSegurado },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },

        //        #endregion

        //        #region Sinistro 14.24.100500.55

        //        new()
        //        {
        //            IdMovimentacao = 16,
        //            IdAgregador = 1009,
        //            NumeroAgregador = "14.24.100500.55",
        //            Fornecedor = BuscaFornecedor(2004),
        //            DataAutorizacao = "2024-06-07",
        //            DocumentoId = 3007,
        //            DocumentoNumero = "NF-3007",
        //            Valor = 9843.75m,
        //            Situacao = new SituacaoDto < StatusProcessamento > { Valor = StatusProcessamento.AProcessar },
        //            AtivoCodigo = "MNO-0007",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosSuspensosFaltaPeca },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.NotaFiscalNaoAnexada },
        //                new CondicaoDto{ Valor = Condicao.VeiculoNaoEntregue }
        //            ]
        //        },
        //        new()
        //        {
        //            IdMovimentacao = 17,
        //            IdAgregador = 1009,
        //            NumeroAgregador = "14.24.100500.55",
        //            Fornecedor = BuscaFornecedor(2004),
        //            DataAutorizacao = "2024-06-08",
        //            DocumentoId = 3008,
        //            DocumentoNumero = "NF-3008",
        //            Valor = 11250.00m,
        //            Situacao = new SituacaoDto<StatusProcessamento> { Valor = StatusProcessamento.PossivelPagamento },
        //            AtivoCodigo = "MNO-0008",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosPendentesRodandoComSegurado },
        //            Divergencias = [],
        //            Condicoes =
        //            [
        //                new CondicaoDto{ Valor = Condicao.MenosDe35DiasEntrega },
        //            ]
        //        },

        //        #endregion

        //        #region Sinistro 14.24.100500.65

        //        new()
        //        {
        //            IdMovimentacao = 14,
        //            IdAgregador = 1010,
        //            NumeroAgregador = "14.24.100500.65",
        //            Fornecedor = BuscaFornecedor(2004),
        //            DataAutorizacao = "2024-06-05",
        //            DocumentoId = 0,
        //            DocumentoNumero = string.Empty,
        //            Valor = 6707.81m,
        //            Situacao = new SituacaoDto<StatusProcessamento> { Valor = StatusProcessamento.PagamentoEfetuado },
        //            AtivoCodigo = "MNO-0005",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosConcluidosAguardandoSegurado },
        //            Divergencias = [],
        //            Condicoes =  []
        //        },

        //        #endregion

        //        #region Sinistro 14.24.100500.70

        //        new()
        //        {
        //            IdMovimentacao = 15,
        //            IdAgregador = 1011,
        //            NumeroAgregador = "14.24.100500.70",
        //            Fornecedor = BuscaFornecedor(2004),
        //            DataAutorizacao = "2024-06-06",
        //            DocumentoId = 3006,
        //            DocumentoNumero = "NF-3006",
        //            Valor = 8437.50m,
        //            Situacao = new SituacaoDto<StatusProcessamento> { Valor = StatusProcessamento.PagamentoEfetuado },
        //            AtivoCodigo = "MNO-0006",
        //            AtivoStatus = new SituacaoDto<StatusVeiculo> { Valor = StatusVeiculo.ReparosConcluidosAguardandoSegurado },
        //            Divergencias = [],
        //            Condicoes = []
        //        },

        //        #endregion
        //    ];
        //}

        //private static List<MovimentacaoDetalhesDto> MontarListaDeDetalhesDeMovimentacoes()
        //{
        //    var detalhes = new List<MovimentacaoDetalhesDto>();

        //    foreach (var movimentacao in _movimentacoes)
        //    {
        //        var itens = MontaItensPorMovimentacaoFixo(movimentacao.IdMovimentacao, movimentacao.Fornecedor.Id);
        //        var valotTotalAcumulado = SomaValorTotalAcumulado(itens);
        //        var valorPassivelRessarcimento = SomaVersoesPorTipoMovimento(itens, TipoMovimentoItem.Exclusao);

        //        detalhes.Add(new MovimentacaoDetalhesDto
        //        {
        //            IdMovimentacao = movimentacao.IdMovimentacao,
        //            NumeroAgregador = movimentacao.NumeroAgregador,
        //            ValorTotalAcumulado = valotTotalAcumulado,
        //            ValorTotalPago = movimentacao.Situacao!.Valor == StatusProcessamento.PagamentoEfetuado ? valotTotalAcumulado : 0m,
        //            ValorTotalAPagar = movimentacao.Situacao!.Valor == StatusProcessamento.PossivelPagamento ? valotTotalAcumulado : 0m,
        //            ValorPassivelRessarcimento = valorPassivelRessarcimento,
        //            Fornecedor = new MovimentacaoFornecedorDto
        //            {
        //                Cnpj = movimentacao.Fornecedor.Cnpj,
        //                Id = movimentacao.Fornecedor.Id,
        //                RazaoSocial = movimentacao.Fornecedor.RazaoSocial,
        //                NomeFantasia = movimentacao.Fornecedor.NomeFantasia
        //            },
        //            Ativos =
        //            [
        //               new MovimentacaoAtivoDto
        //               {
        //                   Codigo = movimentacao.AtivoCodigo,
        //                   Status = movimentacao.AtivoStatus
        //               }
        //            ],
        //            Itens = itens,
        //            NotaFiscal = new MovimentacaoNotaFiscalDto
        //            {
        //                Id = movimentacao.DocumentoId,
        //                Fornecedor = movimentacao.Fornecedor,
        //                Numero = movimentacao.DocumentoNumero,
        //                Serie = "1",
        //                Valor = movimentacao.Valor
        //            }
        //        });
        //    }

        //    foreach (var detalhe in detalhes)
        //    {
        //        detalhe.DadosGeraisAgregador = new MovimentacaoDadosGeraisAgregador
        //        {
        //            Itens = [..
        //                detalhes
        //                .Where(md => md.IdMovimentacao == detalhe.IdMovimentacao)
        //                .SelectMany(md => md.Itens ?? [])],
        //        };
        //    }

        //    return detalhes;
        //}

        //private static decimal SomaValorTotalAcumulado(List<MovimentacaoItemDto> itens)
        //{
        //    var somaTodasVersoesAutorizadas = SomaVersoesPorTipoMovimento(itens, TipoMovimentoItem.Autorizacao);

        //    var somaTodasVersoesExcluidas = SomaVersoesPorTipoMovimento(itens, TipoMovimentoItem.Exclusao);

        //    return somaTodasVersoesAutorizadas - somaTodasVersoesExcluidas;
        //}

        //private static decimal SomaVersoesPorTipoMovimento(List<MovimentacaoItemDto> itens, TipoMovimentoItem tipoMovimento)
        //{
        //    return itens
        //        .SelectMany(i => i.Versoes ?? [])
        //        .Where(v => v.TipoMovimento?.Valor == tipoMovimento)
        //        .Sum(v => v.ValorTotal);
        //}

        //public static PainelPrincipalDto MontarPainelPrincipal()
        //{
        //    // Pseudocódigo:
        //    // 1. Calcular o valor total autorizado (soma de todos os valores das movimentações)
        //    // 2. Calcular o total de notas pagas (Situação == "Pago")
        //    // 3. Calcular o total a pagar (Situação == "A Pagar")
        //    // 4. Calcular o total de ressarcimentos (Situação == "Ressarcido" ou similar, se houver)
        //    // 5. Calcular o total de exclusões (Situação == "Excluído" ou similar, se houver)
        //    // 6. Contar o número de notas em cada categoria
        //    // 7. Calcular progresso (exemplo: percentual de notas pagas sobre total)
        //    // 8. Montar o objeto PainelPrincipalDto e retornar

        //    var totalAutorizado = _movimentacoes.Sum(m => m.Valor);

        //    var pagas = _movimentacoes.Where(m => m.Situacao != null && m.Situacao.Valor == StatusProcessamento.PagamentoEfetuado).ToList();
        //    var totalPagas = pagas.Sum(m => m.Valor);
        //    var qtdPagas = pagas.Count;

        //    var aPagar = _movimentacoes.Where(m => m.Situacao != null && m.Situacao.Valor == StatusProcessamento.PossivelPagamento).ToList();
        //    var totalAPagar = aPagar.Sum(m => m.Valor);
        //    var qtdAPagar = aPagar.Count;

        //    // Ressarcimentos e Exclusões não existem explicitamente, então retornam zerados
        //    var totalRessarcimentos = 0m;
        //    var qtdRessarcimentos = 0;
        //    var totalExclusoes = 0m;
        //    var qtdExclusoes = 0;

        //    // Progresso: % de notas pagas sobre total
        //    double progressoPagas = _movimentacoes.Count > 0 ? (double)qtdPagas / _movimentacoes.Count : 0.0;

        //    return new PainelPrincipalDto
        //    {
        //        AutorizadoTotal = new PainelPrincipalValorDto
        //        {
        //            Valor = totalAutorizado
        //        },
        //        Exclusoes = new PainelPrincipalExclusoesDto
        //        {
        //            Valor = totalExclusoes,
        //            Notas = qtdExclusoes,
        //            Progresso = 0.0
        //        },
        //        NfsPagas = new PainelPrincipalNfsPagasDto
        //        {
        //            Valor = totalPagas,
        //            Notas = qtdPagas,
        //            Progresso = progressoPagas,
        //            TotalAPagar = new PainelPrincipalTotalAPagarDto
        //            {
        //                Valor = totalAPagar
        //            },
        //            Incluidas = new PainelPrincipalIncluidasDto
        //            {
        //                Valor = totalPagas + totalAPagar,
        //            },
        //            PendenteExclusao = new PainelPrincipalPendentesExclusaoDto
        //            {
        //                Valor = 0m,
        //            }
        //        },
        //        Ressarcimentos = new PainelPrincipalRessarcimentosDto
        //        {
        //            Valor = totalRessarcimentos,
        //            Notas = qtdRessarcimentos,
        //            Progresso = 0.0,
        //            Realizados = new PainelPrincipalRealizadosDto
        //            {
        //                Valor = 0m,
        //            },
        //            Pendentes = new PainelPrincipalPendentesDto
        //            {
        //                Valor = 0m,
        //            },
        //            NfsPendentesExclusao = new PainelPrincipalNfsPendentesExclusaoDto
        //            {
        //                Valor = 0m,
        //            }
        //        }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensPorMovimentacaoFixo(int idMovimentacao, int idFornecedor)
        //{
        //    return idMovimentacao switch
        //    {
        //        1 => MontaItensMov1(idFornecedor),
        //        2 => MontaItensMov2(idFornecedor),
        //        3 => MontaItensMov3(idFornecedor),
        //        4 => MontaItensMov4(idFornecedor),
        //        5 => MontaItensMov5(idFornecedor),
        //        6 => MontaItensMov6(idFornecedor),
        //        7 => MontaItensMov7(idFornecedor),
        //        8 => MontaItensMov8(idFornecedor),
        //        9 => MontaItensMov9(idFornecedor),
        //        10 => MontaItensMov10(idFornecedor),
        //        11 => MontaItensMov11(idFornecedor),
        //        12 => MontaItensMov12(idFornecedor),
        //        13 => MontaItensMov13(idFornecedor),
        //        14 => MontaItensMov14(idFornecedor),
        //        15 => MontaItensMov15(idFornecedor),
        //        16 => MontaItensMov16(idFornecedor),
        //        17 => MontaItensMov17(idFornecedor),
        //        _ => []
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov1(int fornecedorId)
        //{
        //    // Valor: 6707.81 (baixo) - 5 itens  
        //    return
        //    [
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-01", DataMovimento = "2024-06-01", DocumentoId = 3001, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-01", DataMovimento = "2024-06-01", DocumentoId = 3001, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-01", DataMovimento = "2024-06-01", DocumentoId = 3001, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 900.00m, ValorTotal = 900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-01", DataMovimento = "2024-06-01", DocumentoId = 3001, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1107.81m, ValorTotal = 1107.81m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-01", DataMovimento = "2024-06-01", DocumentoId = 3001, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    ];
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov2(int fornecedorId)
        //{
        //    // Valor: 8437.50 (médio) - 6 itens
        //    return
        //    [
        //        new() { Id = 1, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-13", DataMovimento = "2024-06-13", DocumentoId = 3013, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-13", DataMovimento = "2024-06-13", DocumentoId = 3013, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-13", DataMovimento = "2024-06-13", DocumentoId = 3013, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-13", DataMovimento = "2024-06-13", DocumentoId = 3013, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 900.00m, ValorTotal = 900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-13", DataMovimento = "2024-06-13", DocumentoId = 3013, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 937.50m, ValorTotal = 937.50m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-13", DataMovimento = "2024-06-13", DocumentoId = 3013, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1900.00m, ValorTotal = 1900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    ];
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov3(int fornecedorId)
        //{
        //    // Valor: 9843.75 (médio) - 7 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-14", DataMovimento = "2024-06-14", DocumentoId = 3014, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-14", DataMovimento = "2024-06-14", DocumentoId = 3014, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-14", DataMovimento = "2024-06-14", DocumentoId = 3014, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-14", DataMovimento = "2024-06-14", DocumentoId = 3014, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-14", DataMovimento = "2024-06-14", DocumentoId = 3014, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 943.75m, ValorTotal = 943.75m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-14", DataMovimento = "2024-06-14", DocumentoId = 3014, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 1000.00m, ValorTotal = 1000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 7, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-14", DataMovimento = "2024-06-14", DocumentoId = 3014, Fornecedor = BuscaFornecedor(2000), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov4(int fornecedorId)
        //{
        //    // Valor: 6707.81 (baixo) - 4 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-02", DataMovimento = "2024-06-02", DocumentoId = 3002, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1700.00m, ValorTotal = 1700.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-02", DataMovimento = "2024-06-02", DocumentoId = 3002, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-02", DataMovimento = "2024-06-02", DocumentoId = 3002, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1007.81m, ValorTotal = 1007.81m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-02", DataMovimento = "2024-06-02", DocumentoId = 3002, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov5(int fornecedorId)
        //{
        //    // Valor: 7031.25 (baixo) - 5 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-09", DataMovimento = "2024-06-09", DocumentoId = 3009, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-09", DataMovimento = "2024-06-09", DocumentoId = 3009, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-09", DataMovimento = "2024-06-09", DocumentoId = 3009, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 900.00m, ValorTotal = 900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-09", DataMovimento = "2024-06-09", DocumentoId = 3009, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1431.25m, ValorTotal = 1431.25m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-09", DataMovimento = "2024-06-09", DocumentoId = 3009, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov6(int fornecedorId)
        //{
        //    // Valor: 7312.50 (baixo) - 5 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-10", DataMovimento = "2024-06-10", DocumentoId = 3010, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-10", DataMovimento = "2024-06-10", DocumentoId = 3010, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-10", DataMovimento = "2024-06-10", DocumentoId = 3010, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 900.00m, ValorTotal = 900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-10", DataMovimento = "2024-06-10", DocumentoId = 3010, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1712.50m, ValorTotal = 1712.50m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-10", DataMovimento = "2024-06-10", DocumentoId = 3010, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov7(int fornecedorId)
        //{
        //    // Valor: 7593.75 (médio) - 6 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-11", DataMovimento = "2024-06-11", DocumentoId = 3011, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-11", DataMovimento = "2024-06-11", DocumentoId = 3011, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-11", DataMovimento = "2024-06-11", DocumentoId = 3011, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-11", DataMovimento = "2024-06-11", DocumentoId = 3011, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-11", DataMovimento = "2024-06-11", DocumentoId = 3011, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 693.75m, ValorTotal = 693.75m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-11", DataMovimento = "2024-06-11", DocumentoId = 3011, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1000.00m, ValorTotal = 1000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov8(int fornecedorId)
        //{
        //    // Valor: 7875.00 (médio) - 6 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-12", DataMovimento = "2024-06-12", DocumentoId = 3012, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-12", DataMovimento = "2024-06-12", DocumentoId = 3012, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-12", DataMovimento = "2024-06-12", DocumentoId = 3012, Fornecedor = BuscaFornecedor(2001) , Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-12", DataMovimento = "2024-06-12", DocumentoId = 3012, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-12", DataMovimento = "2024-06-12", DocumentoId = 3012, Fornecedor = BuscaFornecedor(2001)  , Quantidade = 1, ValorUnitario = 975.00m, ValorTotal = 975.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-12", DataMovimento = "2024-06-12", DocumentoId = 3012, Fornecedor = BuscaFornecedor(2001), Quantidade = 1, ValorUnitario = 1000.00m, ValorTotal = 1000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov9(int fornecedorId)
        //{
        //    // Valor: 6707.81 (baixo) - 4 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-03", DataMovimento = "2024-06-03", DocumentoId = 3003, Fornecedor = BuscaFornecedor(2002), Quantidade = 1, ValorUnitario = 1700.00m, ValorTotal = 1700.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-03", DataMovimento = "2024-06-03", DocumentoId = 3003, Fornecedor = BuscaFornecedor(2002), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-03", DataMovimento = "2024-06-03", DocumentoId = 3003, Fornecedor = BuscaFornecedor(2002), Quantidade = 1, ValorUnitario = 1007.81m, ValorTotal = 1007.81m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-03", DataMovimento = "2024-06-03", DocumentoId = 3003, Fornecedor = BuscaFornecedor(2002), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov10(int fornecedorId)
        //{
        //    // Valor: 6707.81 (baixo) - 4 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-04", DataMovimento = "2024-06-04", DocumentoId = 3004, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1700.00m, ValorTotal = 1700.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-04", DataMovimento = "2024-06-04", DocumentoId = 3004, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-04", DataMovimento = "2024-06-04", DocumentoId = 3004, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1007.81m, ValorTotal = 1007.81m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-04", DataMovimento = "2024-06-04", DocumentoId = 3004, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov11(int fornecedorId)
        //{
        //    // Valor: 10125.00 (alto) - 7 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-15", DataMovimento = "2024-06-15", DocumentoId = 3015, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-15", DataMovimento = "2024-06-15", DocumentoId = 3015, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-15", DataMovimento = "2024-06-15", DocumentoId = 3015, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-15", DataMovimento = "2024-06-15", DocumentoId = 3015, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-15", DataMovimento = "2024-06-15", DocumentoId = 3015, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1125.00m, ValorTotal = 1125.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-15", DataMovimento = "2024-06-15", DocumentoId = 3015, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1000.00m, ValorTotal = 1000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 7, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }   , DataAutorizacao = "2024-06-15", DataMovimento = "2024-06-15", DocumentoId = 3015, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1625.00m, ValorTotal = 1625.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov12(int fornecedorId)
        //{
        //    // Valor: 10406.25 (alto) - 7 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-16", DataMovimento = "2024-06-16", DocumentoId = 3016, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-16", DataMovimento = "2024-06-16", DocumentoId = 3016, Fornecedor = BuscaFornecedor(2003) , Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-16", DataMovimento = "2024-06-16", DocumentoId = 3016, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-16", DataMovimento = "2024-06-16", DocumentoId = 3016, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-16", DataMovimento = "2024-06-16", DocumentoId = 3016, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1281.25m, ValorTotal = 1281.25m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-16", DataMovimento = "2024-06-16", DocumentoId = 3016, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1125.00m, ValorTotal = 1125.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 7, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-16", DataMovimento = "2024-06-16", DocumentoId = 3016, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov13(int fornecedorId)
        //{
        //    // Valor: 10687.50 (alto) - 7 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-17", DataMovimento = "2024-06-17", DocumentoId = 3017, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-17", DataMovimento = "2024-06-17", DocumentoId = 3017, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-17", DataMovimento = "2024-06-17", DocumentoId = 3017, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-17", DataMovimento = "2024-06-17", DocumentoId = 3017, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-17", DataMovimento = "2024-06-17", DocumentoId = 3017, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1556.25m, ValorTotal = 1556.25m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-17", DataMovimento = "2024-06-17", DocumentoId = 3017, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1131.25m, ValorTotal = 1131.25m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 7, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-17", DataMovimento = "2024-06-17", DocumentoId = 3017, Fornecedor = BuscaFornecedor(2003), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov14(int fornecedorId)
        //{
        //    // Valor: 6707.81 (baixo) - 4 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-05", DataMovimento = "2024-06-05", DocumentoId = 0, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1700.00m, ValorTotal = 1700.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-05", DataMovimento = "2024-06-05", DocumentoId = 0, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-05", DataMovimento = "2024-06-05", DocumentoId = 0, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1007.81m, ValorTotal = 1007.81m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-05", DataMovimento = "2024-06-05", DocumentoId = 0, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov15(int fornecedorId)
        //{
        //    // Valor: 8437.50 (médio) - 6 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-06", DataMovimento = "2024-06-06", DocumentoId = 3006, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-06", DataMovimento = "2024-06-06", DocumentoId = 3006, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-06", DataMovimento = "2024-06-06", DocumentoId = 3006, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "GRADE-SUP", Descricao = "Grade Superior", 
        //            Versoes = 
        //            [
        //                new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-06", DataMovimento = "2024-06-06", DocumentoId = 3006, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 900.00m, ValorTotal = 900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] },
        //                new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Exclusao }, DataAutorizacao = "2024-06-06", DataMovimento = "2024-06-08", DocumentoId = null, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 900.00m, ValorTotal = 900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }
        //            ] },
        //        new() { Id = 5, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-06", DataMovimento = "2024-06-06", DocumentoId = 3006, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 937.50m, ValorTotal = 937.50m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-INF", Descricao = "Grade Inferior", 
        //            Versoes = 
        //            [
        //                new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-06", DataMovimento = "2024-06-06", DocumentoId = 3006, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1900.00m, ValorTotal = 1900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] },
        //                new ItemVersaoDto { Id = 2, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Exclusao }, DataAutorizacao = "2024-06-06", DataMovimento = "2024-06-08", DocumentoId = null, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1900.00m, ValorTotal = 1900.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }
        //            ] },
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov16(int fornecedorId)
        //{
        //    // Valor: 9843.75 (médio) - 7 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-07", DataMovimento = "2024-06-07", DocumentoId = 3007, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-07", DataMovimento = "2024-06-07", DocumentoId = 3007, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1200.00m, ValorTotal = 1200.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-07", DataMovimento = "2024-06-07", DocumentoId = 3007, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-07", DataMovimento = "2024-06-07", DocumentoId = 3007, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-07", DataMovimento = "2024-06-07", DocumentoId = 3007, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 943.75m, ValorTotal = 943.75m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-07", DataMovimento = "2024-06-07", DocumentoId = 3007, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1000.00m, ValorTotal = 1000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 7, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-07", DataMovimento = "2024-06-07", DocumentoId = 3007, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static List<MovimentacaoItemDto> MontaItensMov17(int fornecedorId)
        //{
        //    // Valor: 11250.00 (alto) - 7 itens
        //    return new List<MovimentacaoItemDto>
        //    {
        //        new() { Id = 1, Codigo = "FAROL-ESQ", Descricao = "Farol Esquerdo", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-08", DataMovimento = "2024-06-08", DocumentoId = 3008, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 2, Codigo = "FAROL-DIR", Descricao = "Farol Direito", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-08", DataMovimento = "2024-06-08", DocumentoId = 3008, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 3, Codigo = "CAPO", Descricao = "Capô", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-08", DataMovimento = "2024-06-08", DocumentoId = 3008, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 4, Codigo = "PARACHOQUE-D", Descricao = "Para-choque Dianteiro", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-08", DataMovimento = "2024-06-08", DocumentoId = 3008, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1500.00m, ValorTotal = 1500.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 5, Codigo = "VIGA-PARACHOQUE", Descricao = "Viga do Para-choque", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-08", DataMovimento = "2024-06-08", DocumentoId = 3008, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1750.00m, ValorTotal = 1750.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 6, Codigo = "GRADE-SUP", Descricao = "Grade Superior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-08", DataMovimento = "2024-06-08", DocumentoId = 3008, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 1000.00m, ValorTotal = 1000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] },
        //        new() { Id = 7, Codigo = "GRADE-INF", Descricao = "Grade Inferior", Versoes = [new ItemVersaoDto { Id = 1, TipoMovimento = new TipoMovimentoDto { Valor = TipoMovimentoItem.Autorizacao }, DataAutorizacao = "2024-06-08", DataMovimento = "2024-06-08", DocumentoId = 3008, Fornecedor = BuscaFornecedor(2004), Quantidade = 1, ValorUnitario = 2000.00m, ValorTotal = 2000.00m, DataEntrega = null, Divergencias = [], Condicoes = [] }] }
        //    };
        //}

        //private static MovimentacaoFornecedorDto BuscaFornecedor(int fornecedorId)
        //{
        //    // Lista fixa dos 5 fornecedores presentes no mock
        //    var fornecedores = new List<MovimentacaoFornecedorDto>
        //    {
        //        new() { Id = 2000, RazaoSocial = "Indústria de Peças LTDA", NomeFantasia = "Indústria Peças", Cnpj = "12.345.678/0001-00" },
        //        new() { Id = 2001, RazaoSocial = "Comercial de Peças S.A.", NomeFantasia = "Comercial Peças", Cnpj = "23.456.789/0001-11" },
        //        new() { Id = 2002, RazaoSocial = "Fornecedora Brasil LTDA", NomeFantasia = "Brasil Fornece", Cnpj = "34.567.890/0001-22" },
        //        new() { Id = 2003, RazaoSocial = "Distribuidora Nacional de Peças ME", NomeFantasia = "Distribuidora Nacional", Cnpj = "45.678.901/0001-33" },
        //        new() { Id = 2004, RazaoSocial = "Fornecedora Global Importação e Exportação", NomeFantasia = "Fornecedora Global", Cnpj = "56.789.012/0001-44" }
        //    };

        //    return fornecedores.FirstOrDefault(f => f.Id == fornecedorId)!;
        //}
    }
}