using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Importacoes
{
    [Table("importacoes", Schema = "importacoes")]
    public class ImportacaoAgregador
    {
        [Key]
        [Column("id_importacao")]
        public int Id { get; set; }

        [Column("dados_agregador")]
        public string DadosAgregador { get; set; }

        [Column("status")]
        public StatusImportacao Status { get; private set; }

        [Column("mensagem_erro")]
        public string? MensagemErro { get; private set; }

        [Column("data_importacao")]
        public DateTime DataImportacao { get; private set; }

        [Column("data_processamento")]
        public DateTime? DataProcessamento { get; private set; }

        [Column("id_agregador")]
        public int IdAgregador { get; private set; }

        [Column("id_cliente")]
        public string IdCliente { get; private set; }

        public void FinalizarProcessamento(Agregador agregador)
        {
            IdAgregador = agregador.Id;
            Status = StatusImportacao.Processado;
            DataProcessamento = DateTime.UtcNow;
            MensagemErro = null;
        }

        public void Erro(string mensagemErro)
        {
            Status = StatusImportacao.Erro;
            DataProcessamento = DateTime.UtcNow;
            MensagemErro = mensagemErro;
        }

        public static ImportacaoAgregador CriarAguardandoProcessamento(string dadosAgregador, string idCliente)
            => new()
            {
                DadosAgregador = dadosAgregador,
                Status = StatusImportacao.AguardandoProcessamento,
                DataImportacao = DateTime.UtcNow,
                IdCliente = idCliente
            };
    }
}
