using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.Cadastros.Enderecos
{
    [Table("estados", Schema = "cadastros")]
    public class Estado
    {
        [Key]
        [Column("id_estado")]
        public int Id { get; set; }

        [Column("nome")]
        public required string Nome { get; set; }

        [Column("sigla")]
        public required string Sigla { get; set; }

        public ICollection<Cidade> Cidades { get; set; } = [];
    }

}
