using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.Enderecos
{
    [Table("enderecos", Schema = "cadastros")]
    public class Endereco
    {
        public Endereco()
        {
            
        }

        public Endereco(
            string bairro,
            int idCidade,
            int idEstado,
            string logradouro,
            string numero,
            string? complemento,
            string cep,
            TipoEndereco tipoEndereco
        )
        {
            Bairro = bairro;
            IdCidade = idCidade;
            IdEstado = idEstado;
            Logradouro = logradouro;
            // TODO: (Filipe) revisar Numero do endereço como string ou como int?
            // Numero = numero;
            Numero = numero; // Remover essa linha após definir o tipo do Numero            
            Complemento = complemento;
            Cep = cep;
            TipoEndereco = tipoEndereco;
        }

        [Key]
        [Column("id_endereco")]
        public int Id { get; set; }

        [Column("bairro")]
        public required string Bairro { get; set; }

        [Column("id_cidade")]
        public int IdCidade { get; set; }

        [ForeignKey(nameof(IdCidade))]
        public Cidade? Cidade { get; set; }

        [Column("id_estado")]
        public int IdEstado { get; set; }

        [ForeignKey(nameof(IdEstado))]
        public Estado? Estado { get; set; }

        [Column("logradouro")]
        public required string Logradouro { get; set; }

        [Column("numero")]
        public required string Numero { get; set; }

        [Column("complemento")]
        public string? Complemento { get; set; }

        [Column("cep")]
        public required string Cep { get; set; }

        [Column("tipo_endereco")]
        public TipoEndereco TipoEndereco { get; set; }
    }
}
