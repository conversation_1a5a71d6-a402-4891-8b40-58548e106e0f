using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.Cadastros.Enderecos
{
    [Table("cidades", Schema = "cadastros")]
    public class Cidade
    {
        [Key]
        [Column("id_cidade")]
        public int Id { get; set; }

        [Column("nome")]
        public required string Nome { get; set; }

        [Column("cod_ibge")]
        public required string CodIbge { get; set; }

        [Column("id_estado")]
        public int IdEstado { get; set; }

        [ForeignKey(nameof(IdEstado))]
        public Estado? Estado { get; set; }

    }

}
