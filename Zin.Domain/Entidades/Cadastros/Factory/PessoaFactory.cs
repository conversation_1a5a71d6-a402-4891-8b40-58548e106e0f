using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Enums;
using Zin.Domain.ValueObject;

namespace Zin.Domain.Entidades.Cadastros.Factory
{
    public static class PessoaFactory
    {
        public static ClienteEmpresa ClienteEmpresa
        (
            Cnpj cnpj,
            string nomeFantasia,
            string razaoSocial,
            bool matriz,
            Endereco endereco,
            IEnumerable<object> contatos = null
        )
        {
            if (string.IsNullOrWhiteSpace(nomeFantasia))
                throw new ArgumentException("Nome fantasia obrigatório.", nameof(nomeFantasia));

            if (string.IsNullOrWhiteSpace(razaoSocial))
                throw new ArgumentException("Razão social obrigatória.", nameof(razaoSocial));

            var cliente = new ClienteEmpresa(cnpj, matriz, nomeFantasia, razaoSocial);

            if (endereco != null)
            {
                var novoEndereco = new Enderecos.Endereco
                {
                    Cep = endereco.Cep.Digitos,
                    Logradouro = endereco.Logradouro,
                    Numero = endereco.Numero, 
                    Complemento = endereco.Complemento,
                    Bairro = endereco.Bairro,
                    IdCidade = endereco.IdCidade,
                    IdEstado = endereco.IdEstado,
                    TipoEndereco = TipoEndereco.Comercial
                };

                var novaPessoaEndereco = new PessoaEndereco
                {
                    Endereco = novoEndereco,
                    Principal = true
                };

                cliente.PessoasEnderecos.Add(novaPessoaEndereco);
            }

            return cliente;
        }

        public static PessoaJuridica PessoaJuridica
        (
            Cnpj cnpj,
            string nomeFantasia,
            string razaoSocial,
            Endereco endereco,
            IEnumerable<DadosBancario>? dadosBancarios = null,
            IEnumerable<Contato>? contatos = null
        )
        {
            if (string.IsNullOrWhiteSpace(nomeFantasia))
                throw new ArgumentException("Nome fantasia obrigatório.", nameof(nomeFantasia));

            if (string.IsNullOrWhiteSpace(razaoSocial))
                throw new ArgumentException("Razão social obrigatória.", nameof(razaoSocial));

            var pessoa = new PessoaJuridica(cnpj, razaoSocial, nomeFantasia);

            if (endereco != null)
            {
                var novoEndereco = new Enderecos.Endereco
                {
                    Cep = endereco.Cep,
                    Logradouro = endereco.Logradouro,
                    Numero = endereco.Numero,
                    Complemento = endereco.Complemento,
                    Bairro = endereco.Bairro,
                    IdCidade = endereco.IdCidade,
                    IdEstado = endereco.IdEstado,
                    TipoEndereco = TipoEndereco.Comercial
                };

                var novaPessoaEndereco = new PessoaEndereco
                {
                    Endereco = novoEndereco,
                    Principal = true
                };

                pessoa.PessoasEnderecos.Add(novaPessoaEndereco);
            }

            if (contatos != null)
            {
                foreach (var contato in contatos)
                {
                    var novoContato = new Contatos.Contato
                    {
                        NomeResponsavel = contato.Nome,
                        Valor = contato.Valor,
                        MeioDeContato = contato.MeioDeContato
                    };

                    var novaPessoaContato = new PessoaContato
                    {
                        Contato = novoContato,
                        Principal = true
                    };

                    pessoa.PessoasContatos.Add(novaPessoaContato);
                }
            }

            if (dadosBancarios != null)
            {
                foreach (var dadosBancario in dadosBancarios)
                {
                    var novoDadoBancario = new DadoBancario
                    {
                        Banco = new Banco { Codigo = "", Nome = "" }, //dadosBancario.BancoNome,
                        Conta = dadosBancario.BancoConta,
                        CpfCnpjTitular = dadosBancario.BancoCpfCnpjTitular,
                        Titular = dadosBancario.BancoTitular,
                        Agencia = dadosBancario.BancoAgencia,
                        TipoConta = TipoConta.Desconhecido
                    };

                    pessoa.DadosBancarios.Add(new PessoaDadoBancario
                    {
                        DadoBancario = novoDadoBancario
                    });
                }
            }

            return pessoa;
        }
    }
}
