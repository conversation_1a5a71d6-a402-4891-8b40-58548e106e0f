using Zin.Domain.ValueObject;

namespace Zin.Domain.Entidades.Cadastros.Factory.DTO
{
    public sealed record PessoaJuridicaDTO
    {
        public Cnpj Cnpj { get; }
        public string RazaoSocial { get; }
        public string? NomeFantasia { get; }
        public bool Matriz { get; }

        public PessoaJuridicaDTO
        (
            string cnpj, 
            string razaoSocial, 
            string? nomeFantasia, 
            bool matriz
        )
        {
            Cnpj = Cnpj.Criar(cnpj);
            RazaoSocial = razaoSocial;
            NomeFantasia = nomeFantasia;
            Matriz = matriz;
        }
    }
}
