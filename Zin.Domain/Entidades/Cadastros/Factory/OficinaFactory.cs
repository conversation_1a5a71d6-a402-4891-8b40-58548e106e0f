using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.ValueObject;

namespace Zin.Domain.Entidades.Cadastros.Factory.Oficina
{
    public class ImportarOficinaDTO
    {
        public string? RazaoSocial { get; set; }
        public string? NomeFantasia { get; set; }
        public string? Cnpj { get; set; }
        public string? Email { get; set; }

    }
    public static class OficinaFactory
    {
        /// <summary>
        /// Obtém oficinas existentes pelo CNPJ, ou cria novas se não existirem.
        /// </summary>
        /// <param name="dto">DTO contendo as oficinas a serem processadas.</param>
        /// <param name="pessoaRepositorio">Repositório de pessoas para busca/inserção.</param>
        /// <returns>Lista de pessoas (oficinas) existentes ou criadas.</returns>
        public static async Task<List<Pessoa>> ObterOuCriarOficinasAsync(
            List<ImportarOficinaDTO> dtos,
            IPessoaRepository pessoaRepositorio)
        {
            if (dtos == null || !dtos.Any())
                throw new ArgumentException("Oficinas não pode ser nula.", nameof(dtos));

            var listaOficinas = new List<Pessoa>();

            foreach (var dto in dtos)
            {
                if (string.IsNullOrWhiteSpace(dto.Cnpj))
                    continue;

                var oficinaExistente = await pessoaRepositorio.BuscarAsync(
                    x => x.GetType() == typeof(PessoaJuridica) && ((PessoaJuridica)x).Cnpj == dto.Cnpj);

                if (oficinaExistente.Any())
                {
                    listaOficinas.Add(oficinaExistente.First());
                }
                else
                {
                    var cnpj = Cnpj.Criar(dto.Cnpj);
                    var novaPessoaJuridica = new PessoaJuridica(cnpj, dto.RazaoSocial, dto.NomeFantasia);
                    await pessoaRepositorio.InserirAsync(novaPessoaJuridica);

                    listaOficinas.Add(novaPessoaJuridica);
                }
            }

            return listaOficinas;
        }
    }
}