using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.Cadastros.DadosBancarios
{
    [Table("bancos", Schema = "cadastros")]
    public class Banco
    {
        [Key]
        [Column("id_banco")]
        public int Id { get; set; }

        [Column("codigo")]
        [StringLength(10)]
        public required string Codigo { get; set; }

        [Column("nome")]
        [StringLength(100)]
        public required string Nome { get; set; }

    }
}
