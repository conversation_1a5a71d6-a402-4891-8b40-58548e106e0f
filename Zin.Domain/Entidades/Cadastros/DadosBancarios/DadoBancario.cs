using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Numerics;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.DadosBancarios
{
    [Table("dados_bancarios", Schema = "cadastros")]
    public class DadoBancario
    {
        [Key]
        [Column("id_dado_bancario")]
        public int Id { get; set; }

        [Column("id_banco")]
        public int IdBanco { get; set; }

        [Column("agencia")]
        public string? Agencia { get; set; }

        [Column("agencia_dv")]
        public string? AgenciaDV { get; set; }

        [Column("conta")]
        public string Conta { get; set; }

        [Column("conta_dv")]
        public string? ContaDv { get; set; }

        [Column("tipo_conta")]
        public TipoConta TipoConta { get; set; }

        [Column("titular")]
        [StringLength(100)]
        public required string Titular { get; set; }

        [Column("cpf_cnpj_titular")]
        [StringLength(14, MinimumLength = 11)]
        public required string CpfCnpjTitular { get; set; }

        [Column("pix")]
        [StringLength(100)]
        public string? Pix { get; set; }

        [Column("principal")]
        public bool Principal { get; set; }

        [ForeignKey(nameof(IdBanco))]
        public Banco? Banco { get; set; }
    }
}