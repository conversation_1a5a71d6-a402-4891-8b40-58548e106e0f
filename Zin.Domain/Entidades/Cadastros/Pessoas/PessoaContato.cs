using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Contatos;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("pessoas_contatos", Schema = "cadastros")]
    public class PessoaContato
    {
        [Column("id_pessoa")]
        public int IdPessoa { get; set; }

        [ForeignKey(nameof(IdPessoa))]
        public Pessoa? Pessoa { get; set; }

        [Column("id_contato")]
        public int IdContato { get; set; }

        [ForeignKey(nameof(IdContato))]
        public Contato? Contato { get; set; }

        [Column("principal")]
        public bool Principal { get; set; }
    }
}
