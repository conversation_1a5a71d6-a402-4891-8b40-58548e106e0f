using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Enderecos;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("pessoas_enderecos", Schema = "cadastros")]
    public class PessoaEndereco
    {
        [Column("id_pessoa")]
        public int IdPessoa { get; set; }

        [ForeignKey(nameof(IdPessoa))]
        public Pessoa? Pessoa { get; set; }

        [Column("id_endereco")]
        public int IdEndereco { get; set; }

        [ForeignKey(nameof(IdEndereco))]
        public Endereco? Endereco { get; set; }

        [Column("principal")]
        public bool Principal { get; set; }
    }
}
