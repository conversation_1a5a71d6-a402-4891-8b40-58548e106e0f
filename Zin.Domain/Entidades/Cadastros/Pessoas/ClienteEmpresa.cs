using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.ValueObject;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("cliente_empresas", Schema = "cadastros")]
    public class ClienteEmpresa : PessoaJuridica
    {
        public ClienteEmpresa(string cnpj, bool matriz, string nomeFantasia, string razaoSocial) : base(cnpj, razaoSocial, nomeFantasia)
        {
            DefinirMatriz(matriz);
        }

        [Column("matriz")]
        public bool Matriz { get; private set; }

        public ICollection<Agregador> Agregadores { get; set; } = [];

        public void DefinirMatriz(bool matriz)
        {
            Matriz = matriz;
        }
    }
}
