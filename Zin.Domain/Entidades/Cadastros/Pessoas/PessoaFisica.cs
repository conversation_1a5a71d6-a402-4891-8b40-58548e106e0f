using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("pessoas_fisicas", Schema = "cadastros")]
    public class PessoaFisica : Pessoa
    {
        [Column("nome")]
        [StringLength(100)]
        public required string Nome { get; set; }

        [Column("sobrenome")]
        [StringLength(100)]
        public required string Sobrenome { get; set; }

        [Column("cpf")]
        [StringLength(11, MinimumLength = 11)]
        public required string Cpf { get; set; }
    }

}
