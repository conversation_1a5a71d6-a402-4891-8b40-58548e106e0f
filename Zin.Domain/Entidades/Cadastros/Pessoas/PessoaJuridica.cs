using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.ValueObject;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("pessoas_juridicas", Schema = "cadastros")]
    public class PessoaJuridica : Pessoa
    {
        public PessoaJuridica(string cnpj, string razaoSocial, string nomeFantasia)
        {
            if (cnpj == null)
                throw new ArgumentNullException(nameof(cnpj), "CNPJ Obrigatório.");

            if (string.IsNullOrWhiteSpace(razaoSocial))
                throw new ArgumentException("Razão Obrigatória.", nameof(razaoSocial));

            if (string.IsNullOrWhiteSpace(nomeFantasia))    
                throw new ArgumentException("Nome Fantasia Obrigatório.", nameof(nomeFantasia));

            DefinirCnpj(cnpj);
            TipoPessoa = Enums.TipoPessoa.Juridica;
            DefinirRazaoSocial(razaoSocial);
            DefinirNomeFantasia(nomeFantasia);
        }

        [Column("razao_social")]
        [StringLength(150)]
        public string RazaoSocial { get; private set; }

        [Column("nome_fantasia")]
        [StringLength(100)]
        public string NomeFantasia { get; private set; }

        [Column("cnpj")]
        [StringLength(14, MinimumLength = 14)]
        public string Cnpj { get; private set; }

        public void DefinirCnpj(string cnpj)
        {
            if (string.IsNullOrWhiteSpace(cnpj))
                throw new ArgumentNullException(nameof(cnpj), "CNPJ Obrigatório.");

            Cnpj = cnpj;
        }

        public void DefinirRazaoSocial(string razaoSocial)
        {
            if (string.IsNullOrWhiteSpace(razaoSocial))
                throw new ArgumentException("Razão Obrigatória.", nameof(razaoSocial));

            RazaoSocial = razaoSocial;
        }

        public void DefinirNomeFantasia(string nomeFantasia)
        {
            if (string.IsNullOrWhiteSpace(nomeFantasia))
                throw new ArgumentException("Nome Fantasia Obrigatório.", nameof(nomeFantasia));

            NomeFantasia = nomeFantasia;
        }
    }
}