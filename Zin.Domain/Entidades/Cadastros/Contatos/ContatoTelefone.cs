using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.Contatos
{
    // Essa classe foi descontinuada, pois o Telefone agora esta diretamente na entidade Contato
    // Deve ser utilizada o MeioDeContato na entidade Contato para definir o tipo do contato (Telefone, Email, etc)
    //[Table("contato_telefones", Schema = "cadastros")]
    //public class ContatoTelefone
    //{
    //    [Key]
    //    [Column("id_contato_telefone")]
    //    public int Id { get; set; }

    //    [Column("id_contato")]
    //    public int IdContato { get; set; }

    //    [ForeignKey(nameof(IdContato))]
    //    public Contato? Contato { get; set; }

    //    [Column("tipo")]
    //    public TipoTelefone Tipo { get; set; }

    //    [Column("numero")]
    //    public long Numero { get; set; }

    //    [Column("ramal")]
    //    public int Ramal { get; set; }
    //}
}