using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.Contatos
{
    // Essa classe foi descontinuada, pois o TipoContato foi substituido por MeioDeContato na entidade Contato
    //[Table("contato_tipos", Schema = "cadastros")]
    //[PrimaryKey(nameof(IdContato), nameof(TipoContato))]
    //public class ContatoTipo
    //{
    //    [Column("id_contato")]
    //    public int IdContato { get; set; }

    //    [ForeignKey(nameof(IdContato))]
    //    public Contato? Contato { get; set; }

    //    // Essa propriedade não sera usada pois mudamos o TipoContato para MeioDeContato
    //    // Porém, mantivemos esse comentado para referencia futura, caso seja necessario
    //    //[Column("tipo_contato")]
    //    //public TipoContato TipoContato { get; set; }
    //}
}
