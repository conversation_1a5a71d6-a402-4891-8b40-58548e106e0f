using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.Contatos
{
    [Table("contatos", Schema = "cadastros")]
    public class Contato
    {
        [Key]
        [Column("id_contato")]
        public int Id { get; set; }

        [Column("meio_de_contato")]
        public MeioDeContato MeioDeContato { get; set; }

        [Column("nome_responsavel")]
        public required string NomeResponsavel { get; set; }

        [Column("valor")]
        public string? Valor { get; set; }

        [Column("ativo")]
        public bool Ativo { get; set; }

        [Column("observacao")]
        public string? Observacao { get; set; } 

        [Column("recebe_notificacao")]
        public bool RecebeNotificacao { get; set; }
    }
}