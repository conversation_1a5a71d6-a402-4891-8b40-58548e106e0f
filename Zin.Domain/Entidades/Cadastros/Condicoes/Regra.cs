using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.Cadastros.Condicoes
{
    [Table("regras", Schema = "zinpag")]
    public class Regra
    {
        [Key]
        [Column("id_regra")]
        public Guid Id { get; set; }

        [Column("id_configuracao")]
        public Guid IdConfiguracao { get; set; }

        [Column("nome")]
        public required string Nome { get; set; }

        [Column("valor")]
        public required string Valor { get; set; }

        [Column("tipagem")]
        public required string Tipagem { get; set; }

        [Column("operador")]
        public required OperadorComparacao Operador { get; set; }

        [Column("ativo")]
        public required bool Ativo { get; set; }

        [ForeignKey(nameof(IdConfiguracao))]
        public required Configuracao Configuracao { get; set; }
    }
}
