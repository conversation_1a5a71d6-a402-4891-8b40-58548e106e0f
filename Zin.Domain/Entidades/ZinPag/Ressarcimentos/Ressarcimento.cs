using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Ressarcimentos
{
    [Table("ressarcimentos", Schema = "zinpag")]
    public class Ressarcimento
    {
        //1. <PERSON>ve primária
        [Key]
        [Column("id_ressarcimento")]
        public int Id { get; set; }

        //2. <PERSON>ves estrangeiras
        [Column("id_cliente_empresa")]
        public int IdClienteEmpresa { get; set; }

        [Column("id_fornecedor")]
        public int IdFornecedor { get; set; } 

        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        [Column("id_ativo")]
        public int IdAtivo { get; set; }

        [Column("id_agregador_ativo")]
        public int? IdAgregadorAtivo { get; set; }

        [Column("id_movimentacao")]
        public Guid? IdMovimentacao { get; set; }

        [Column("id_documento")]
        public int? IdDocumento { get; set; }

        //3. Propriedades simples

        [Column("numero_ressarcimento")]
        public string NumeroRessarcimento { get; set; }

        [Column("valor", TypeName = "decimal(12, 2)")]
        public decimal Valor { get; set; }

        [Column("data_solicitacao")]
        public DateTime DataSolicitacao { get; set; }

        [Column("data_pagamento")]
        public DateTime? DataPagamento { get; set; }

        [Column("forma_pagamento_ressarcimento")]
        public FormasPagamentoRessarcimento FormaPagamentoRessarcimento { get; set; } 

        [Column("status_ressarcimento")]
        public StatusRessarcimento StatusRessarcimento { get; set; }

        [Column("data_cancelamento")]
        public DateTime? DataCancelamento { get; set; }

        [Column("descricao")]
        public string? Descricao { get; set; }

        [Column("cancelado")]
        public bool Cancelado { get; set; }

        // Relacionamentos
        [ForeignKey(nameof(IdClienteEmpresa))]
        public Pessoa? ClienteEmpresa { get; set; }

        [ForeignKey(nameof(IdFornecedor))]
        public Pessoa? Fornecedor { get; set; }

        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }

        [ForeignKey(nameof(IdAgregadorAtivo))]
        public Agregador? AgregadorAtivo { get; set; }

        [ForeignKey(nameof(IdAtivo))]
        public Ativo? Ativo { get; set; }

        [ForeignKey(nameof(IdMovimentacao))]
        public Movimentacao? Movimentacao { get; set; }

        [ForeignKey(nameof(IdDocumento))]
        public Documento? Documento { get; set; }

        public ICollection<RessarcimentoItemVersao> RessarcimentoItemVersoes { get; set; } = [];

        public ICollection<DocumentoRessarcimento> DocumentoRessarcimentos { get; set; } = [];

        public ICollection<LiquidacaoRessarcimento> LiquidacoesRessarcimentos { get; set; } = [];
    }
}
