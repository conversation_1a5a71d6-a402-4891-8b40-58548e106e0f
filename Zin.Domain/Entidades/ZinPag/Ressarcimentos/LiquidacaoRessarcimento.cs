using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.ZinPag.Ressarcimentos
{
    [Table("liquidacoes_ressarcimentos", Schema = "zinpag")]
    public class LiquidacaoRessarcimento
    {
        [Key]
        [Column("id_liquidacao_ressarcimento")]
        public int Id { get; set; }

        [Column("id_ressarcimento")]
        public int IdRessarcimento { get; set; }

                [Column("data_ressarcimento")]
        public DateTime Data { get; set; }

                [Column("valor_ressarcido", TypeName = "decimal(10,2)")]
        public decimal Valor { get; set; }

        [ForeignKey(nameof(IdRessarcimento))]
        public virtual Ressarcimento? Ressarcimento { get; set; }
    }
}
