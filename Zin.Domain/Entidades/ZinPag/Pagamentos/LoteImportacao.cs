using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos;

[Table("lotes_importacao_pagamentos", Schema = "zinpag")]
public class LoteImportacao
{
    [Key]
    [Column("id_lote")]
    public int Id { get; set; }

    [Column("id_cliente")]
    [Required]
    [MaxLength(50)]
    public string IdCliente { get; set; } = string.Empty;

    [Column("nome_arquivo")]
    [Required]
    [MaxLength(255)]
    public string NomeArquivo { get; set; } = string.Empty;

    [Column("data_processamento")]
    public DateTime DataProcessamento { get; set; }

    [Column("usuario")]
    [Required]
    [MaxLength(100)]
    public string Usuario { get; set; } = string.Empty;

    [Column("total_pagamentos")]
    public int TotalPagamentos { get; set; }

    [Column("valor_total", TypeName = "decimal(12,2)")]
    public decimal ValorTotal { get; set; }

    [Column("status")]
    public StatusLoteImportacao Status { get; set; }

    // Relacionamentos
    public ICollection<PagamentoLoteImportacao> PagamentosLotes { get; set; } = new List<PagamentoLoteImportacao>();

    // Propriedades calculadas
    [NotMapped]
    public List<Pagamento> Pagamentos => PagamentosLotes.Select(pl => pl.Pagamento).Where(p => p != null).ToList()!;

    public void AtualizarTotais()
    {
        TotalPagamentos = PagamentosLotes.Count;
        ValorTotal = PagamentosLotes.Where(pl => pl.Pagamento != null).Sum(pl => pl.Pagamento!.Valor);
    }
}
