using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos;

[Table("lotes_importacao_pagamentos", Schema = "zinpag")]
public class LoteImportacaoPagamento
{
    [Key]
    [Column("id_lote_importacao")]
    public int Id { get; set; }

    [Column("id_cliente")]
    [Required]
    [MaxLength(50)]
    public string IdCliente { get; set; } = string.Empty;

    [Column("nome_arquivo")]
    [Required]
    [MaxLength(255)]
    public string NomeArquivo { get; set; } = string.Empty;

    [Column("data_processamento")]
    public DateTime DataProcessamento { get; set; }

    [Column("data_inicio")]
    public DateTime? DataInicio { get; set; }

    [Column("data_fim")]
    public DateTime? DataFim { get; set; }

    [Column("usuario_processamento")]
    [Required]
    [MaxLength(100)]
    public string UsuarioProcessamento { get; set; } = string.Empty;

    [Column("status")]
    public StatusLoteImportacao Status { get; set; }

    // Métricas do lote
    [Column("total_linhas_lidas")]
    public int TotalLinhasLidas { get; set; }

    [Column("total_programados")]
    public int TotalProgramados { get; set; }

    [Column("total_liquidados")]
    public int TotalLiquidados { get; set; }

    [Column("total_criados")]
    public int TotalCriados { get; set; }

    [Column("total_ignorados")]
    public int TotalIgnorados { get; set; }

    [Column("total_duplicados")]
    public int TotalDuplicados { get; set; }

    [Column("total_erros")]
    public int TotalErros { get; set; }

    // Arquivo original
    [Column("arquivo_original")]
    public byte[]? ArquivoOriginal { get; set; }

    [Column("tipo_arquivo")]
    [MaxLength(50)]
    public string TipoArquivo { get; set; } = string.Empty;

    [Column("tamanho_arquivo")]
    public long TamanhoArquivo { get; set; }

    // Relacionamentos
    public ICollection<LinhaImportacaoPagamento> Linhas { get; set; } = new List<LinhaImportacaoPagamento>();

    // Propriedades calculadas
    [NotMapped]
    public int TotalProcessados => TotalProgramados + TotalLiquidados + TotalCriados;

    [NotMapped]
    public int TotalComProblemas => TotalIgnorados + TotalDuplicados + TotalErros;

    [NotMapped]
    public bool TemErros => TotalErros > 0;

    [NotMapped]
    public bool TemIgnorados => TotalIgnorados > 0;

    [NotMapped]
    public TimeSpan? TempoProcessamento => DataFim.HasValue && DataInicio.HasValue 
        ? DataFim.Value - DataInicio.Value 
        : null;

    // Métodos de controle de estado
    public void IniciarProcessamento(string usuario)
    {
        Status = StatusLoteImportacao.Processando;
        DataInicio = DateTime.UtcNow;
        UsuarioProcessamento = usuario;
    }

    public void FinalizarProcessamento()
    {
        DataFim = DateTime.UtcNow;
        Status = TotalErros > 0 ? StatusLoteImportacao.ConcluidoComErros : StatusLoteImportacao.Concluido;
    }

    public void MarcarComoFalha()
    {
        DataFim = DateTime.UtcNow;
        Status = StatusLoteImportacao.Falha;
    }

    public void AtualizarMetricas()
    {
        TotalLinhasLidas = Linhas.Count;
        TotalProgramados = Linhas.Count(l => l.OperacaoAplicada == OperacaoLinhaPagamento.Programar && l.Status == StatusLinhaPagamento.OK);
        TotalLiquidados = Linhas.Count(l => l.OperacaoAplicada == OperacaoLinhaPagamento.Liquidar && l.Status == StatusLinhaPagamento.OK);
        TotalCriados = Linhas.Count(l => l.PagamentoCriado);
        TotalIgnorados = Linhas.Count(l => l.OperacaoAplicada == OperacaoLinhaPagamento.Ignorado);
        TotalDuplicados = Linhas.Count(l => l.OperacaoAplicada == OperacaoLinhaPagamento.Duplicado);
        TotalErros = Linhas.Count(l => l.Status == StatusLinhaPagamento.Erro);
    }
}
