using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos;

[Table("linhas_importacao_pagamentos", Schema = "zinpag")]
public class LinhaImportacaoPagamento
{
    [Key]
    [Column("id_linha_importacao")]
    public int Id { get; set; }

    [Column("id_lote_importacao")]
    public int IdLoteImportacao { get; set; }

    [Column("numero_linha")]
    public int NumeroLinha { get; set; }

    // Dados da linha do Excel
    [Column("cnpj_pagador")]
    [MaxLength(20)]
    public string? CnpjPagador { get; set; }

    [Column("cnpj_cpf_favorecido")]
    [MaxLength(20)]
    public string? CnpjCpfFavorecido { get; set; }

    [Column("numero_nf")]
    [MaxLength(50)]
    public string? NumeroNF { get; set; }

    [Column("data_programacao")]
    public DateTime? DataProgramacao { get; set; }

    [Column("data_liquidacao")]
    public DateTime? DataLiquidacao { get; set; }

    [Column("valor_pago", TypeName = "decimal(10,2)")]
    public decimal ValorPago { get; set; }

    // Dados adicionais do Excel (opcionais)
    [Column("sinistro")]
    [MaxLength(50)]
    public string? Sinistro { get; set; }

    [Column("placa")]
    [MaxLength(10)]
    public string? Placa { get; set; }

    [Column("nome_pagador")]
    [MaxLength(255)]
    public string? NomePagador { get; set; }

    [Column("favorecido")]
    [MaxLength(255)]
    public string? Favorecido { get; set; }

    [Column("forma_pagamento")]
    [MaxLength(50)]
    public string? FormaPagamento { get; set; }

    // Resultado do processamento
    [Column("operacao_aplicada")]
    public OperacaoLinhaPagamento OperacaoAplicada { get; set; }

    [Column("status")]
    public StatusLinhaPagamento Status { get; set; }

    [Column("mensagem")]
    [MaxLength(500)]
    public string Mensagem { get; set; } = string.Empty;

    [Column("id_pagamento")]
    public int? IdPagamento { get; set; }

    [Column("pagamento_encontrado")]
    public bool PagamentoEncontrado { get; set; }

    [Column("pagamento_criado")]
    public bool PagamentoCriado { get; set; }

    [Column("data_processamento")]
    public DateTime DataProcessamento { get; set; }

    [Column("hash_linha")]
    [MaxLength(64)]
    public string? HashLinha { get; set; }

    // Relacionamentos
    [ForeignKey(nameof(IdLoteImportacao))]
    public LoteImportacaoPagamento LoteImportacao { get; set; } = null!;

    [ForeignKey(nameof(IdPagamento))]
    public Pagamento? Pagamento { get; set; }

    // Métodos auxiliares
    public void MarcarComoSucesso(string mensagem, int? pagamentoId = null, bool pagamentoCriado = false, bool pagamentoEncontrado = false)
    {
        Status = StatusLinhaPagamento.OK;
        Mensagem = mensagem;
        IdPagamento = pagamentoId;
        PagamentoCriado = pagamentoCriado;
        PagamentoEncontrado = pagamentoEncontrado;
        DataProcessamento = DateTime.UtcNow;
    }

    public void MarcarComoErro(string mensagem)
    {
        Status = StatusLinhaPagamento.Erro;
        Mensagem = mensagem;
        DataProcessamento = DateTime.UtcNow;
    }

    public void MarcarComoIgnorado(string motivo)
    {
        OperacaoAplicada = OperacaoLinhaPagamento.Ignorado;
        Status = StatusLinhaPagamento.OK;
        Mensagem = motivo;
        DataProcessamento = DateTime.UtcNow;
    }

    public void MarcarComoDuplicado(string motivo)
    {
        OperacaoAplicada = OperacaoLinhaPagamento.Duplicado;
        Status = StatusLinhaPagamento.OK;
        Mensagem = motivo;
        DataProcessamento = DateTime.UtcNow;
    }

    public string GerarHashLinha()
    {
        var dados = $"{CnpjPagador}|{CnpjCpfFavorecido}|{NumeroNF}|{ValorPago}|{DataProgramacao?.ToString("yyyy-MM-dd")}|{DataLiquidacao?.ToString("yyyy-MM-dd")}";
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(dados));
        return Convert.ToHexString(hash);
    }

    public bool PodeSerReprocessada()
    {
        return Status == StatusLinhaPagamento.Erro || OperacaoAplicada == OperacaoLinhaPagamento.Ignorado;
    }
}
