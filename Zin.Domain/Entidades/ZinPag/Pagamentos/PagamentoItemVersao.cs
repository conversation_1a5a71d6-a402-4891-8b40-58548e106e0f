using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos
{
    [Table("pagamento_itens_versoes", Schema = "zinpag")]
    public class PagamentoItemVersao
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("id_pagamento")]
        public int IdPagamento { get; set; }

        [Column("id_item_versao")]
        public int IdItemVersao { get; set; }

        [ForeignKey(nameof(IdPagamento))]
        public Pagamento? Pagamento { get; set; }

        [ForeignKey(nameof(IdItemVersao))]
        public ItemVersao? ItemVersao { get; set; }
    }
}
