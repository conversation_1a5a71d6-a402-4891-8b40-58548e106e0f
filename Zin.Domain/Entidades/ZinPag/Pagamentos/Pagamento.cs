using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Entidades.ZinPag.Documentos;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos
{
    [Table("pagamentos", Schema = "zinpag")]
    public class Pagamento
    {
        [Key]
        [Column("id_pagamento")]
        public int Id { get; set; }

        // Relacionamento (FK)
        [Column("id_pessoa")]
        public int IdPessoaBeneficiaria { get; set; }

        [Column("id_agregador")]
        public int? IdAgregador { get; set; }
        
        [Column("status_pagamento")]
        [MaxLength(20)]
        public StatusPagamento StatusPagamento { get; set; }

        [Column("data_criacao")]
        public DateTime DataCriacao { get; set; }

        [Column("data_atualizacao")]
        public DateTime? DataAtualizacao { get; set; }

        [Column("cancelado")]
        public bool Cancelado { get; set; }

        [Column("data_previsao")]
        public DateTime? DataPrevisao { get; set; }

        [Required]
        [Column("valor", TypeName = "decimal(10,2)")]
        public decimal Valor { get; set; }

        [Column("forma_pagamento")]
        [Required]
        [MaxLength(50)]
        public FormaPagamento FormaPagamento { get; set; }

        [Column("descricao")]
        [MaxLength(255)]
        public string? Descricao { get; set; }

        [ForeignKey(nameof(IdPessoaBeneficiaria))]
        public Pessoa? Beneficiario { get; set; }

        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }

        public ICollection<PagamentoItemVersao> PagamentoItemVersoes { get; set; } = [];

        public ICollection<DocumentoPagamento> DocumentoPagamentos { get; set; } = [];

        public ICollection<LiquidacaoPagamento> LiquidacoesPagamentos { get; set; } = [];

    }
}

