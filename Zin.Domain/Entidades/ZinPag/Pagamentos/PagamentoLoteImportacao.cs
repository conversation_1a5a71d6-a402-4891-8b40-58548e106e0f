using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos;

[Table("pagamentos_lotes_importacao", Schema = "zinpag")]
public class PagamentoLoteImportacao
{
    [Column("id_pagamento")]
    public int IdPagamento { get; set; }

    [Column("id_lote")]
    public int IdLote { get; set; }

    [Column("numero_linha")]
    public int? NumeroLinha { get; set; }

    [Column("data_processamento")]
    public DateTime DataProcessamento { get; set; }

    // Relacionamentos
    [ForeignKey(nameof(IdPagamento))]
    public Pagamento? Pagamento { get; set; }

    [ForeignKey(nameof(IdLote))]
    public LoteImportacao? LoteImportacao { get; set; }
}
