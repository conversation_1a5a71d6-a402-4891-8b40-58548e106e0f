using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Processamento
{
    [Table("divergencias", Schema = "zinpag")]
    public class Divergencia
    {
        [Key]
        [Column("id_divergencia")]
        public Guid Id { get; set; }

        [Column("tipo_divergencia")]
        public TipoDivergencia TipoDivergencia { get; set; }

        [Column("detalhe")]
        public string? Detalhe { get; set; }

        [Column("data_registro")]
        public DateTime DataRegistro { get; set; }

        [Column("decisao")]
        public DecisaoDivergenciaOuCondicao? Decisao { get; set; }

        [Column("data_decisao")]
        public DateTime? DataDecisao { get; set; }

        [Column("usuario_decisao")]
        public string? UsuarioDecisao { get; set; }

        [Column("id_registro_processamento_item_versao")]
        public int IdRegistroProcessamentoItemVersao { get; set; }

        [ForeignKey("IdRegistroProcessamentoItemVersao")]
        public RegistroProcessamentoItemVersao? RegistroProcessamentoItemVersao { get; set; }

        public ICollection<ItemVersaoDivergencia> ItensVersao { get; set; } = new List<ItemVersaoDivergencia>();
    }
}