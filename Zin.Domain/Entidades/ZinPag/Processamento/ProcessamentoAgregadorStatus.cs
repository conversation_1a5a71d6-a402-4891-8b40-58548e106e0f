using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Processamento
{
    [Table("processamento_agregador_status", Schema = "zinpag")]
    public class ProcessamentoAgregadorStatus
    {
        [Key]
        [Column("id_processamento_agregador_status")]
        public int Id { get; set; }

        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        [Column("tipo")]
        public TipoProcessamento Tipo { get; set; }

        [Column("status")]
        public StatusProcessamento Status { get; set; }

        [Column("data_atualizacao")]
        public DateTime? DataAtualizacao { get; set; }

        [Column("detalhe")]
        public string? Detalhe { get; set; }

        [ForeignKey("IdAgregador")]
        public Agregador? Agregador { get; set; }
    }
}