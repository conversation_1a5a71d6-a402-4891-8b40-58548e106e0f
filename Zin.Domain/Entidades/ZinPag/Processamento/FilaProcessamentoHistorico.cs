using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Processamento
{
    [Table("fila_processamento_historico", Schema = "zinpag")]
    public class FilaProcessamentoHistorico
    {
        [Key]
        [Column("id_fila_processamento_historico")]
        public Guid Id { get; set; }

        [Column("status")]
        public StatusFilaProcessamento? StatusFilaProcessamento { get; set; }

        [Column("data_registro")]
        public DateTime DataRegistro { get; set; }

        [Column("id_agregador")]
        public int? IdAgregador { get; set; }

        [Column("id_item")]
        public int? IdItem { get; set; }

        [ForeignKey(nameof(IdItem))]
        public Item? Item { get; set; }

        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }
    }
}
