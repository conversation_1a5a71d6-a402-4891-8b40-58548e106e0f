using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Processamento
{
    [Table("condicoes", Schema = "zinpag")]
    public class Condicao
    {
        [Key]
        [Column("id_condicao")]
        public Guid Id { get; set; }

        [Column("detalhe")]
        public string? Detalhe { get; set; }

        [Column("apto")]
        public bool? Apto { get; set; }

        [Column("tipoconfiguracao")]
        public TipoConfiguracao? TipoConfiguracao { get; set; }

        [Column("regra")]
        public string? Regra { get; set; }

        [Column("data_registro")]
        public DateTime DataRegistro { get; set; }

        [Column("decisao")]
        public DecisaoDivergenciaOuCondicao? Decisao { get; set; }

        [Column("data_decisao")]
        public DateTime? DataDecisao { get; set; }

        [Column("usuario_decisao")]
        public string? UsuarioDecisao { get; set; }

        [Column("id_registro_processamento_item_versao")]
        public int IdRegistroProcessamentoItemVersao { get; set; }

        [ForeignKey("IdRegistroProcessamentoItemVersao")]
        public RegistroProcessamentoItemVersao? RegistroProcessamentoItemVersao { get; set; }
        public ICollection<ItemVersaoCondicao> ItensVersao { get; set; } = new List<ItemVersaoCondicao>();
    }
}