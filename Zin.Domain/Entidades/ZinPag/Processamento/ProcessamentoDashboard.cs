using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Processamento
{
    [Table("processamento_dashboard", Schema = "zinpag")]
    public class ProcessamentoDashboard
    {
        [Key]
        [Column("id_processamento_dashboard")]
        public Guid Id { get; set; }

        [Column("data_registro")]
        public DateTime DataRegistro { get; set; }

        [Column("tipo")]
        public TipoProcessamentoDashboard? Tipo { get; set; }

        [Column("dados")]
        public string? Dados { get; set; }

    }
}
