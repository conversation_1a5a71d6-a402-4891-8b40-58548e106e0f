using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Movimentacoes
{
    [Table("movimentacoes", Schema = "zinpag")]
    public class Movimentacao
    {
        // 1. Chave primária
        [Key]
        [Column("id_movimentacao")]
        public Guid Id { get; set; }

        // 2. <PERSON><PERSON> estrangeira<PERSON>
        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        // 3. Propriedades simples
        [Column("data_hora_autorizacao_item")]
        public DateTime? DataHoraAutorizacaoItem { get; set; }

        [Column("ativo")]
        public bool Ativo { get; set; }

        [Column("valor_total_a_pagar")]
        public decimal? ValorTotalAPagar{ get; set; }

        [Column("valor_total_pago")]
        public decimal? ValorTotalPago { get; set; }

        [Column("valor_total_a_ressarcir")]
        public decimal? ValorTotalARessarcir { get; set; }

        [Column("valor_total_ressarcido")]
        public decimal? ValorTotalRessarcido { get; set; }

        [Column("tipo_movimentacao")]
        public TipoItemVersao TipoMovimentacao { get; set; }


        // 4. Propriedades de navegação
        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }
        public List<Documento> Documentos { get; set; } = [];
        public List<MovimentacaoItemVersao> MovimentacoesItensVersoes { get; set; } = [];
    }
}
