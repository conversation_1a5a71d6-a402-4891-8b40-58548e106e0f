using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Domain.Entidades.ZinPag.Movimentacoes
{
    [Table("movimentacoes_itens_versoes", Schema = "zinpag")]
    public class MovimentacaoItemVersao
    {
        [Key]
        [Column("id_movimentacao_item_versao")]
        public Guid Id { get; set; }
        
        [Column("id_movimentacao")]
        public Guid IdMovimentacao { get; set; }        

        [Column("id_item_versao")]
        public int IdItemVersao { get; set; }

        [Column("ativo")]
        public bool Ativo { get; set; } = true;

        [Column("data_vinculo")]
        public DateTime DataVinculo { get; set; } = DateTime.UtcNow;

        [Column("data_desvinculo")]
        public DateTime? DataDesvinculo { get; set; } = null;

        [ForeignKey(nameof(IdMovimentacao))]
        public Movimentacao Movimentacao { get; set; } = null!;

        [ForeignKey(nameof(IdItemVersao))]
        public ItemVersao? ItemVersao { get; set; } = null!;

    }
}
