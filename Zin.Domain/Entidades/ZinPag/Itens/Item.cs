using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Itens
{
    [Table("itens", Schema = "zinpag")]
    public class Item
    {
        public Item
        (
            string codigo,
            string descricao,
            int quantidade,
            decimal valorUnitario,
            decimal valorTotal
        )
        {
            if (string.IsNullOrWhiteSpace(codigo)) throw new ArgumentException("Código Obrigatório.", nameof(codigo));
            if (string.IsNullOrWhiteSpace(descricao)) throw new ArgumentException("Descrição Obrigatória.", nameof(descricao));
            if (quantidade <= 0) throw new ArgumentException("Quantidade deve ser maior que zero.", nameof(quantidade));
            if (valorUnitario < 0) throw new ArgumentException("Valor Unitário não pode ser negativo.", nameof(valorUnitario));
            if (valorTotal < 0) throw new ArgumentException("Valor Total não pode ser negativo.", nameof(valorTotal));

            Codigo = codigo.Trim();
            Descricao = descricao.Trim();
            Quantidade = quantidade;
            ValorUnitario = valorUnitario;
            ValorTotal = valorTotal;
        }

        // 1. Chave primária
        [Key]
        [Column("id_item")]
        public int Id { get; set; }

        // 2. Chaves estrangeiras
        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        // 3. Propriedades simples
        [Column("codigo")]
        public string Codigo { get; private set; }

        [Column("descricao")]
        public string Descricao { get; private set; }

        [Column("quantidade")]
        public int Quantidade { get; private set; }

        [Column("valor_unitario")]
        public decimal ValorUnitario { get; set; }

        [Column("valor_total")]
        public decimal ValorTotal { get; private set; }

        [Column("status_processamento")]
        public StatusProcessamento StatusProcessamento { get; set; }

        [Column("status_processamento_item_duplicado")]
        public StatusProcessamento StatusProcessamentoItemDuplicado { get; set; }

        [Column("status_processamento_pagamento_duplicado")]
        public StatusProcessamento StatusProcessamentoPagamentoDuplicado { get; set; }

        [Column("status_processamento_pagamento")]
        public StatusProcessamento StatusProcessamentoPagamento { get; set; }

        [Column("status_processamento_ressarcimento")]
        public StatusProcessamento StatusProcessamentoRessarcimento { get; set; }

        // 4. Propriedades de navegação
        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }

        public ICollection<ItemVersao> Versoes { get; set; } = [];

        public bool Igual(string? codigo, string? descricao)
        {
            return string.Equals(Normalizar(Codigo), Normalizar(codigo), StringComparison.OrdinalIgnoreCase)
                && string.Equals(Normalizar(Descricao), Normalizar(descricao), StringComparison.OrdinalIgnoreCase);
        }

        private static string? Normalizar(string? s) => s?.Trim();

        public void AdicionarVersao(ItemVersao versao)
        {
            Versoes.Add(versao);
        }
    }
}
