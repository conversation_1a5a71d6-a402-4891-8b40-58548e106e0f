using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Processamento;

namespace Zin.Domain.Entidades.ZinPag.Itens
{
    [Table("item_versao_condicao", Schema = "zinpag")]
    public class ItemVersaoCondicao
    {
        [Key]
        [Column("id_item_versao_condicao")]
        public Guid Id { get; set; }

        [Column("id_item_versao")]
        public int IdItemVersao { get; set; }

        [Column("id_condicao")]
        public Guid IdCondicao { get; set; }

        [Column("data_registro")]
        public DateTime DataRegistro { get; set; }

        [ForeignKey(nameof(IdItemVersao))]
        public ItemVersao? ItemVersao { get; set; }

        [ForeignKey(nameof(IdCondicao))]
        public Condicao? Condicao { get; set; }
    }
}
