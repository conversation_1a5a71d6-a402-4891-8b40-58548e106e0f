using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Processamento;

namespace Zin.Domain.Entidades.ZinPag.Itens
{
    [Table("item_versao_divergencia", Schema = "zinpag")]
    public class ItemVersaoDivergencia
    {
        [Key]
        [Column("id_item_versao_divergencia")]
        public Guid Id { get; set; }

        [Column("id_item_versao")]
        public int IdItemVersao { get; set; }

        [Column("id_divergencia")]
        public Guid IdDivergencia { get; set; }

        [Column("data_registro")]
        public DateTime DataRegistro { get; set; }

        [ForeignKey(nameof(IdItemVersao))]
        public ItemVersao? ItemVersao { get; set; }

        [ForeignKey(nameof(IdDivergencia))]
        public Divergencia? Divergencia { get; set; }
    }
}
