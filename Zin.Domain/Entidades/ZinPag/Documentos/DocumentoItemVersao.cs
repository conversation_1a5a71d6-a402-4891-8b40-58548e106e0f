using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Domain.Entidades.ZinPag.Documentos
{
    [Table("documento_item_versao", Schema = "zinpag")]
    public class DocumentoItemVersao 
    {
        [Key]
        [Column("id_documento_item_versao")]
        public int Id { get; set; }

        [Column("id_documento")]
        public int IdDocumento { get; set; }

        [Column("id_item_versao")]
        public int IdItemVersao { get; set; }

        [ForeignKey(nameof(IdDocumento))]
        public Documento? Documento { get; set; }

        [ForeignKey(nameof(IdItemVersao))]
        public ItemVersao? ItemVersao { get; set; }
    }
}
