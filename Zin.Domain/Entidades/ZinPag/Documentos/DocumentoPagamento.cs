using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Pagamentos;

namespace Zin.Domain.Entidades.ZinPag.Documentos
{

    [Table("documentos_pagamento", Schema = "zinpag")]
    public class DocumentoPagamento
    {
        // 1. Chave primária
        [Key]
        [Column("id_documento_pagamento")]
        public int Id { get; set; }

        // 2. <PERSON><PERSON> estrangeiras
        [Column("id_documento")]
        public int IdDocumento { get; set; }

        [Column("id_pagamento")]
        public int IdPagamento { get; set; }

        // 3. Propriedades de navegação
        [ForeignKey(nameof(IdDocumento))]
        public Documento? Documento { get; set; }

        [ForeignKey(nameof(IdPagamento))]
        public Pagamento? Pagamento { get; set; }

    }
}
