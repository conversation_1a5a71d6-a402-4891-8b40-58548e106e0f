using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;

namespace Zin.Domain.Entidades.ZinPag.Documentos
{
    [Table("documentos_ressarcimento", Schema = "zinpag")]
    public class DocumentoRessarcimento
    {
        [Key]
        [Column("id_documento_ressarcimento")]
        public int Id { get; set; }

        [Column("id_documento")]
        public int IdDocumento { get; set; }

        [Column("id_ressarcimento")]
        public int IdRessarcimento { get; set; }

        [ForeignKey(nameof(IdDocumento))]
        public Documento? Documento { get; set; }

        [ForeignKey(nameof(IdRessarcimento))]
        public Ressarcimento? Ressarcimento { get; set; }
    }
}
