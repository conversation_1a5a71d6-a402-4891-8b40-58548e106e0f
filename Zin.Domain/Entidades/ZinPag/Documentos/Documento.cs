using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Documentos
{
    [Table("documentos", Schema = "zinpag")]
    public class Documento
    {
        // 1. Chave primária
        [Key]
        [Column("id_documento")]
        public int Id { get; set; }

        // 2. <PERSON><PERSON> estrangeiras
        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        [Column("id_pessoa_emitente")]
        public int IdPessoaEmitente { get; set; }

        [Column("id_pessoa_destinatario")]
        public int IdPessoaDestinatario { get; set; }

        // 3. Propriedades simples
        [Column("tipo_documento")]
        public TipoDocumento TipoDocumento { get; set; }

        [Column("numero")]
        [StringLength(50)]
        public required string? Numero { get; set; }

        [Column("data_hora_emissao")]
        public DateTime DataEmissao { get; set; }

        [Column("serie")]
        public string Serie { get; set; }

        [Column("total")]
        public required decimal ValorTotal { get; set; }

        [Column("caminho_arquivo")]
        public string? CaminhoArquivo { get; set; }

        [Column("status_documento")]
        public StatusDocumento StatusDocumento { get; set; }

        [Column("id_movimentacao")]
        public Guid? IdMovimentacao { get; set; }


        // 4. Propriedades de navegação
        [ForeignKey(nameof(IdPessoaEmitente))]
        public Pessoa? Emitente { get; set; }

        [ForeignKey(nameof(IdPessoaDestinatario))]
        public Pessoa? Destinatario { get; set; }

        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }

        [ForeignKey(nameof(IdMovimentacao))]
        public Movimentacao Movimentacao { get; set; } = null!;

        public DocumentoPagamento? DocumentoPagamento { get; set; }

        public ICollection<DocumentoItemVersao> DocumentoItemVersoes { get; set; } = [];

    }
}

