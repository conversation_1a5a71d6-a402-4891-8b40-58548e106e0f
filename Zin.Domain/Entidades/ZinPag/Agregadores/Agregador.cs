using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Agregadores
{
    [Table("agregadores", Schema = "zinpag")]
    public class Agregador
    {
        public Agregador(TipoAgregador tipoAgregador, string numero)
        {
            if (string.IsNullOrWhiteSpace(numero)) throw new ArgumentException("Número Obrigatório.", nameof(numero));

            TipoAgregador = tipoAgregador;
            Numero = numero;
        }


        // 1. <PERSON><PERSON> p<PERSON>
        [Key]
        [Column("id_agregador")]
        public int Id { get; set; }

        // 2. <PERSON><PERSON> estrang<PERSON>
        [Column("id_cliente_empresa")]
        public int IdClienteEmpresa { get; set; }

        // 3. Propriedades simples
        [Column("tipo_agregador")]
        public TipoAgregador TipoAgregador { get; set; }

        [Column("numero")]
        [StringLength(100)]
        public string Numero { get; set; }

        [Column("status_processamento")]
        public StatusProcessamento StatusProcessamento { get; set; }

        // 4. Propriedades de navegação
        [ForeignKey(nameof(IdClienteEmpresa))]
        public ClienteEmpresa? ClienteEmpresa { get; set; }

        public ICollection<Ativo> Ativos { get; set; } = [];

        public ICollection<Item> Itens { get; set; } = [];

        public ICollection<Movimentacao> Movimentacoes { get; set; } = [];

        public ICollection<Documento> Documentos { get; set; } = [];

        public void AdicionarItem(Item item) => Itens.Add(item);    

        public void DefinirClienteEmpresa(ClienteEmpresa clienteEmpresa)
        {
            ClienteEmpresa = clienteEmpresa ?? throw new ArgumentNullException(nameof(clienteEmpresa), "Cliente Empresa Obrigatório.");
            IdClienteEmpresa = clienteEmpresa.Id;
        }

        public bool EhNovo => Id == default;
    }
}