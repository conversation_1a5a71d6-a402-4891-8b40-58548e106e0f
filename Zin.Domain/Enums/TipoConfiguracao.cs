
using System.ComponentModel;

namespace Zin.Domain.Enums
{
    public enum TipoConfiguracao
    {
        Desconhecido = 0,

        [Description("Quantidade dias após prazo de entrega")]
        QuantidadeDiasAposPrazoEntrega = 100,

        [Description("Situação do veículo")]
        SituacaoVeiculo = 200,

        [Description("Situação da nota fiscal de venda")]
        SituacaoNotaFiscalVenda = 300,

        [Description("Situação da nota fiscal de devolução")]
        SituacaoNotaFiscalDevolucao = 400,

        [Description("Quantidade de dias após a exclusão")]
        QuantidadeDiasAposExclusao = 500,
    }
}


 

 

 

 
