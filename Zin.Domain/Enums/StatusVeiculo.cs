namespace Zin.Domain.Enums
{
    public enum StatusVeiculo
    {
        Desconhecido = 0,

        // Status não informado
        NaoInformado = 100,

        // Sem contato com a oficina
        SemContatoOficina = 200,

        // Veículo está em Reparos
        EmReparos = 300,

        // Reparos pendentes por falta de peça
        ReparosSuspensosFaltaPeca = 400,

        // Reparos pendentes e está Rodando com o Segurado
        ReparosPendentesRodandoComSegurado = 500,

        // Reparos concluídos aguardando a retirada pelo segurado
        ReparosConcluidosAguardandoSegurado = 600,

        // Reparos concluídos e está rodando com o segurado
        ReparosConcluidosRodandoComSegurado = 700,
    }
}