using System.Text.RegularExpressions;

namespace Zin.Domain.ValueObject
{
    public sealed record Placa
    {
        private static readonly Regex _regexPlacaAntiga =
            new(@"^[A-Z]{3}-?\d{4}$", RegexOptions.IgnoreCase | RegexOptions.Compiled);

        private static readonly Regex _regexPlacaMercosul =
            new(@"^[A-Z]{3}-?\d[A-Z]\d{2}$", RegexOptions.IgnoreCase | RegexOptions.Compiled);

        public string Valor { get; }

        private Placa(string valor) => Valor = valor;

        public static Placa Criar(string? valor)
        {
            if (string.IsNullOrWhiteSpace(valor))
                throw new ArgumentException("Placa não pode ser vazia.", nameof(valor));

            var placaNormalizada = Normalizar(valor);

            if (!EhValida(placaNormalizada))
                throw new ArgumentException($"Placa inválida: {valor}", nameof(valor));

            return new Placa(placaNormalizada);
        }

        public static bool EhValida(string? valor)
        {
            if (string.IsNullOrWhiteSpace(valor))
                return false;

            var placaNormalizada = Normalizar(valor);
            return _regexPlacaAntiga.IsMatch(placaNormalizada)
                || _regexPlacaMercosul.IsMatch(placaNormalizada);
        }

        public static string Normalizar(string valor)
        {
            if (string.IsNullOrWhiteSpace(valor)) return string.Empty;
            return valor.Trim().ToUpperInvariant();
        }

        public override string ToString() => Valor;

        public bool Equals(Placa? placa) =>
            placa is not null && Valor.Equals(placa.Valor, StringComparison.OrdinalIgnoreCase);

        public bool Equals(string? placa) =>
            !string.IsNullOrWhiteSpace(placa)
            && this.Valor == Normalizar(placa);

        public override int GetHashCode() =>
            StringComparer.OrdinalIgnoreCase.GetHashCode(Valor);

        public static implicit operator string(Placa placa) => placa.Valor;
    }
}
