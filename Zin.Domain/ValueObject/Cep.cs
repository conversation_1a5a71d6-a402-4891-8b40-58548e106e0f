using System.Text.RegularExpressions;

namespace Zin.Domain.ValueObject
{
    public sealed record Cep
    {
        public string Digitos { get; }

        private Cep(string digitos) => Digitos = digitos;

        /// <summary>
        /// Cria um Cep validado e normalizado.
        /// </summary>
        public static Cep Criar(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) 
                throw new ArgumentException("CEP não pode ser vazio.", nameof(value));

            var digitos = Normalizar(value);

            if (!EhValido(digitos))
                throw new ArgumentException("CEP inválido.", nameof(value));

            return new Cep(digitos);
        }

        /// <summary>
        /// Mantém somente os dígitos (remove pontos, traços, espaços).
        /// </summary>
        public static string Normalizar(string value)
            => Regex.Replace(value ?? "", "[^0-9]", "");

        /// <summary>
        /// Valida se o CEP tem 8 dígitos numéricos.
        /// </summary>
        /// 

        /// <summary>
        /// Formata no padrão 00000-000.
        /// </summary>
        public string Format()
            => $"{Digitos[..5]}-{Digitos[5..]}";

        public override string ToString() => Format();

        // Conversão implícita para string (sempre dígitos)

        public static bool EhValido(string? cep)
        {
            if (string.IsNullOrWhiteSpace(cep))
                return false;

            var digits = new string(cep.Where(char.IsDigit).ToArray());
            if (digits.Length != 8)
                return false;

            // evita sequências tipo 00000000, 11111111 etc.
            if (digits.Distinct().Count() == 1)
                return false;

            return true; // regras básicas; validação de existência real pode ser externa (ViaCEP, etc.)
        }

        public static implicit operator string(Cep cep) => cep.Digitos;
    }
}
