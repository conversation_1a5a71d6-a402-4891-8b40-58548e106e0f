namespace Zin.Domain.ValueObject
{
    public sealed record Endereco
    {
        public string Logradouro { get; }
        public string Numero { get; }
        public string? Complemento { get; }
        public string <PERSON><PERSON> { get; }
        public int IdCidade { get; }
        public int IdEstado { get; }
        public Cep Cep { get; }

        public Endereco
        (
            string logradouro,
            string numero,
            string? complemento,
            string bairro,
            int idCidade,
            int idEstado,
            Cep cep
        )
        {
            Logradouro = logradouro;
            Numero = numero;
            Complemento = complemento;
            Bairro = bairro;
            IdCidade = idCidade;
            IdEstado = idEstado;
            Cep = cep;
        }
    }
}
