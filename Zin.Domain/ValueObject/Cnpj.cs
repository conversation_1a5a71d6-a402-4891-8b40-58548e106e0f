using System.Text;

namespace Zin.Domain.ValueObject
{
    public sealed record Cnpj
    {
        public string Digitos { get; }

        private Cnpj(string digitos) => Digitos = digitos;

        public static Cnpj Criar(string cnpj)
        {
            if (!EhValido(cnpj))
                throw new ArgumentException("CNPJ inválido.", nameof(cnpj));

            var digitos = Normalizar(cnpj);
            return new Cnpj(digitos);
        }

        public override string ToString() => Digitos;

        public static string Normalizar(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) return string.Empty;

            var sb = new StringBuilder(14);
            foreach (var ch in value)
                if (ch >= '0' && ch <= '9') sb.Append(ch);
            return sb.ToString();
        }

        public static bool TentarNormalizar(string value, out string digits14)
        {
            digits14 = Normalizar(value);
            return digits14.Length == 14;
        }

        public static string Formatar(string value)
        {
            var d = Normalizar(value);
            if (d.Length != 14) throw new ArgumentException("CNPJ deve conter 14 dígitos.", nameof(value));
            return $"{d.Substring(0, 2)}.{d.Substring(2, 3)}.{d.Substring(5, 3)}/{d.Substring(8, 4)}-{d.Substring(12, 2)}";
        }

        public static bool EhValido(string value)
        {
            var d = Normalizar(value);
            if (d.Length != 14) return false;
            if (d.All(c => c == d[0])) return false; // 000... ou 111... etc

            var nums = d.Select(c => c - '0').ToArray();

            int[] w1 = { 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            int[] w2 = { 6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };

            int sum1 = 0;
            for (int i = 0; i < 12; i++) sum1 += nums[i] * w1[i];
            int r1 = sum1 % 11;
            int dv1 = r1 < 2 ? 0 : 11 - r1;

            int sum2 = 0;
            for (int i = 0; i < 12; i++) sum2 += nums[i] * w2[i];
            sum2 += dv1 * w2[12];
            int r2 = sum2 % 11;
            int dv2 = r2 < 2 ? 0 : 11 - r2;

            return nums[12] == dv1 && nums[13] == dv2;
        }

        /* override equals */
        public bool Equals(Cnpj? cnpj) => cnpj?.Digitos == this.Digitos;
        public bool Equals(string cnpj) => Normalizar(cnpj) == this.Digitos;

        public static implicit operator string(Cnpj cnpj) => cnpj.Digitos;
    }
}
