using System.Text.RegularExpressions;

namespace Zin.Domain.ValueObject
{
    public sealed record Email
    {
        private static readonly Regex EmailRegex = new(
            @"^[^@\s]+@[^@\s]+\.[^@\s]+$",
            RegexOptions.Compiled | RegexOptions.IgnoreCase
        );

        public string Endereco { get; }

        public Email(string endereco)
        {
            if (string.IsNullOrWhiteSpace(endereco))
                throw new ArgumentException("Email não pode ser vazio.", nameof(endereco));

            endereco = endereco.Trim().ToLowerInvariant();

            if (!EmailRegex.IsMatch(endereco))
                throw new ArgumentException("Formato de email inválido.", nameof(endereco));

            Endereco = endereco;
        }

        public override string ToString() => Endereco;
    }
}
