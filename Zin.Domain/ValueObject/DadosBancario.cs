using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zin.Domain.ValueObject
{
    public sealed record DadosBancario
    {
        public string BancoTitular { get; }
        public string BancoCpfCnpjTitular { get; }
        public string BancoNome { get; }
        public string BancoConta { get; }
        public string BancoAgencia { get; }

        public DadosBancario
        (
            string bancoTitular,
            string bancoCpfCnpjTitular,
            string bancoNome,
            string bancoConta,
            string bancoAgencia
        )
        {
            BancoTitular = bancoTitular;
            BancoCpfCnpjTitular = bancoCpfCnpjTitular;
            BancoNome = bancoNome;
            BancoConta = bancoConta;
            BancoAgencia = bancoAgencia;
        }
    }
}
