using Zin.Domain.Enums;

namespace Zin.Domain.ValueObject
{
    public sealed record Contato
    {
        public string Nome { get; }
        public string Valor { get; }
        public MeioDeContato MeioDeContato { get; }
        public bool RecebeNotificacao { get; set; }
        public string Observacao { get; }

        public Contato(string nome, string valor, MeioDeContato meioDeContato, bool recebeNotificacao, string observacao)
        {
            Nome = nome;
            Valor = valor;
            MeioDeContato = meioDeContato;
            RecebeNotificacao = recebeNotificacao;
            Observacao = observacao;
        }
    }
}