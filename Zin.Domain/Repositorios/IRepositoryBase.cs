using System.Linq.Expressions;

namespace Zin.Domain.Repositorios
{
    public interface IRepositoryBase<T, TChave> where T : class
    {
        Task  AtualizarAsync(T entidade);
        Task<IEnumerable<T>> BuscarAsync(Expression<Func<T, bool>> predicado);
        Task<T?> BuscarPorIdAsync(TChave id);
        Task<int> InserirAsync(T entidade);
        Task<int[]> InserirVariosAsync(IEnumerable<T> entidades);
        Task<IEnumerable<T>> ListarAsync();
        Task DeletarAsync(TChave id);
        Task DeletarVariosAsync(IEnumerable<T> entidades);
    }
}