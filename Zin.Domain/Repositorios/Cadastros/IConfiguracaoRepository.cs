using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Repositorios.Cadastros
{
    public interface IConfiguracaoRepository
    {
        Task<Configuracao?> BuscarPorIdAsync(Guid id);
        Task<IEnumerable<Configuracao>> BuscarAsync();
        Task<Guid> InserirAsync(Configuracao configuracao);
        Task AtualizarAsync(Configuracao configuracao);
        Task DeletarAsync(Guid id);

        Task<IEnumerable<Configuracao?>> BuscarPorTipoProcessamentoAsync(TipoProcessamento tipoProcessamento);

        Task<Regra?> BuscarRegraPorIdAsync(Guid idRegra);
        Task InserirRegraAsync(Regra regra);
        Task AtualizarRegraAsync(Regra regra);
        Task DeletarRegraAsync(Guid idRegra);

    }
}
