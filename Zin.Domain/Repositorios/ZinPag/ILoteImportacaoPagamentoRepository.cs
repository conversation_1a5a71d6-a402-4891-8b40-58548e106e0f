using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;

namespace Zin.Domain.Repositorios.ZinPag;

public interface ILoteImportacaoPagamentoRepository : IRepositoryBase<LoteImportacaoPagamento, int>
{
    Task<IEnumerable<LoteImportacaoPagamento>> BuscarPorClienteAsync(
        string idCliente,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null);

    Task<LoteImportacaoPagamento?> BuscarComLinhasPorIdAsync(int id, string idCliente);

    Task<int> ContarPorClienteAsync(
        string idCliente,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null);

    Task<IEnumerable<LoteImportacaoPagamento>> BuscarPaginadoPorClienteAsync(
        string idCliente,
        int page,
        int pageSize,
        StatusLoteImportacao? status = null,
        DateTime? dataInicio = null,
        DateTime? dataFim = null,
        string? usuario = null);
}
