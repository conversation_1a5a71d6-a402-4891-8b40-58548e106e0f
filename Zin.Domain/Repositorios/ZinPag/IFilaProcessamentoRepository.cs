using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IFilaProcessamentoRepository
    {
        Task<FilaProcessamento?> BuscarPorIdAsync(Guid id);
        Task<List<FilaProcessamento>> ListarTodosAsync();
        Task<List<FilaProcessamento>> ListarPorStatusAsync(params StatusFilaProcessamento?[] status);
        Task InserirAsync(FilaProcessamento fila);
        Task AtualizarAsync(FilaProcessamento fila);
    }
}
