using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IMovimentacoesRepository 
    {
        Task<Movimentacao?> BuscarPorIdAsync(Guid id);
        Task<Movimentacao?> BuscarAsync(Expression<Func<Movimentacao, bool>> predicate);
        Task<List<Movimentacao>> BuscarVariosAsync(Expression<Func<Movimentacao, bool>> predicate);
        Task<List<Movimentacao>> BuscarMovimentacoesPorItemVersaoAsync(int idItemVersao);
        Task<List<Movimentacao>> BuscarMovimentacoesPorItemVersaoEAgrupamentoAsync(int idItemVersao, bool agrupamentoPorData);
        Task<Movimentacao?> BuscarMovimentacaoPorDocumento(int idDocumento);
        Task<Movimentacao?> BuscarMovimentacaoAtivaPorDocumento(int idDocumento);
        Task AtualizarAsync(Movimentacao movimentacao);
        Task InserirAsync(Movimentacao movimentacao);
        Task<Movimentacao?> BuscarDetalhesMovimentacaoAsync(Guid id);
        Task<bool> ExistemMovimentacoesPorAgregadorExcluindoMovimentacaoAsync(int idAgregador, Guid movIdExcluir);
    }
}