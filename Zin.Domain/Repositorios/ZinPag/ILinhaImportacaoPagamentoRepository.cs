using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;

namespace Zin.Domain.Repositorios.ZinPag;

public interface ILinhaImportacaoPagamentoRepository : IRepositoryBase<LinhaImportacaoPagamento, int>
{
    Task<IEnumerable<LinhaImportacaoPagamento>> BuscarPorLoteAsync(int idLote);

    Task<IEnumerable<LinhaImportacaoPagamento>> BuscarPaginadoPorLoteAsync(
        int idLote,
        int page,
        int pageSize,
        StatusLinhaPagamento? status = null,
        OperacaoLinhaPagamento? operacao = null);

    Task<int> ContarPorLoteAsync(
        int idLote,
        StatusLinhaPagamento? status = null,
        OperacaoLinhaPagamento? operacao = null);

    Task<IEnumerable<LinhaImportacaoPagamento>> BuscarLinhasParaReprocessamentoAsync(int idLote);

    Task<LinhaImportacaoPagamento?> BuscarPorHashAsync(string hash);

    Task<bool> ExisteHashAsync(string hash);
}
