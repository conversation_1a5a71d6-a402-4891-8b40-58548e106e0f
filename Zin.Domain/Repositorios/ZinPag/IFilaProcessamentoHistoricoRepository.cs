using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Processamento;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IFilaProcessamentoHistoricoRepository
    {
        Task<FilaProcessamentoHistorico?> BuscarPorIdAsync(Guid id);
        Task<List<FilaProcessamentoHistorico>> ListarTodosAsync();
        Task InserirAsync(FilaProcessamentoHistorico fila);
        Task AtualizarAsync(FilaProcessamentoHistorico fila);
    }
}
