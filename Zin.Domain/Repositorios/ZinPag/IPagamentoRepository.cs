using Zin.Domain.Entidades.ZinPag.Pagamentos;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IPagamentoRepository : IRepositoryBase<Pagamento, int>
    {
        Task<IEnumerable<Pagamento>> BuscarPagamentosEmAbertoPorAgregadorAsync(int idAgregador);
        Task<Pagamento?> ObterPorDocumentoIdAsync(int documentoId);
        Task<IEnumerable<Pagamento>> BuscarPagamentosPorItemVersaoIdAsync(int idItemVersao);
        Task<IEnumerable<Pagamento>> BuscarPorAgregadorIdAsync(int agregadorId);
    }
}
