using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IProcessamentoStatusRepository
    {
        Task<IList<ProcessamentoAgregadorStatus>> BuscarPorAgregadorIdAsync(int idAgregador);
        Task<ProcessamentoAgregadorStatus?> BuscarPorAgregadorTipoAsync(int idAgregador, TipoProcessamento tipo);
        Task AtualizarStatusAsync(ProcessamentoAgregadorStatus processamentoStatus);
        Task InserirAsync(ProcessamentoAgregadorStatus status);
    }
}

