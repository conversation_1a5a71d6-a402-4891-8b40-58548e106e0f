using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IItemRepository
    {
        Task<int> InserirAsync(Item item);
        Task AtualizarAsync(Item item);
        Task<Item?> BuscarPorIdAsync(int id);
        Task<Item?> BuscarPorAgregadorCodigoEDescricaoAsync(int? agregadorId, string codigo, string descricao);
        Task<IEnumerable<Item>> ListarAsync();
        Task<IEnumerable<Item>> BuscarAsync(Expression<Func<Item, bool>> predicate);
        Task DeletarAsync(int id);
        Task<IEnumerable<Item>> BuscarPorAgregadorIdAsync(int agregadorId);
        Task<IEnumerable<Item>> BuscarItensComVersoesPorAgregadorExcluindoMovimentacaoAsync(int idAgregador, Guid movIdExcluir);
        Task<IEnumerable<Item>> BuscarItensComVersoesPorAgregadorMesmaMovimentacaoAsync(int idAgregador, Guid movId);
        Task<ItemVersao> BuscarItemVersaoAutorizacaoAsync(int idAgregador, string codigo, string descricao, DateTime dataHoraAutorizacao);
    }
}
