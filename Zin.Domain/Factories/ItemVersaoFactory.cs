using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Factories.Interfaces;
using Zin.Domain.ValueObject;

namespace Zin.Domain.Factories
{
    public class ItemVersaoFactory : IItemVersaoFactory
    {
        public ItemVersao Criar(
            Item item, 
            ItemVersao versaoAnterior, 
            PessoaJuridica fornecedor,
            PessoaJuridica oficina,
            Ativo ativo,
            int quantidade,
            decimal valorUnitario,
            decimal valorTotal,
            TipoMovimentoItem tipoMovimento,
            TipoItemVersao tipoItemVersao,
            DateTime dataCriacao,
            DateTime? dataHoraAutorizacao,
            DateTime? dataHoraMovimentacao,
            DateTime? dataEntrega,
            IEnumerable<Documento>? documentos
        )
        {
            if (item == null) throw new ArgumentNullException(nameof(item));
            if (fornecedor == null) throw new ArgumentNullException(nameof(fornecedor));

            if (versaoAnterior != null)
            {
                if (
                    !item.Codigo.Equals(versaoAnterior.Item.Codigo, StringComparison.InvariantCultureIgnoreCase)
                    || !item.Descricao.Equals(versaoAnterior.Item.Descricao, StringComparison.InvariantCultureIgnoreCase)
                )
                    throw new InvalidOperationException("Item diferente da versão anterior.");

                var cnpjFornecedor = Cnpj.Criar(fornecedor.Cnpj);
                if (!cnpjFornecedor.Equals((versaoAnterior.PessoaFornecedora as PessoaJuridica).Cnpj))
                    throw new InvalidOperationException("Fornecedor diferente da versão anterior.");

                var cnpjOficina = Cnpj.Criar(oficina.Cnpj);
                if (!cnpjOficina.Equals((versaoAnterior.Oficina as PessoaJuridica).Cnpj))
                    throw new InvalidOperationException("Oficina diferente da versão anterior.");
            }

            var numeroVersao = (item.Versoes?.Count ?? 0) + 1;

            var versao = new ItemVersao
            {
                Item = item,
                VersaoAnterior = versaoAnterior,
                NumeroVersao = numeroVersao,
                PessoaFornecedora = fornecedor,
                Oficina = oficina,
                Ativo = ativo,
                Quantidade = quantidade,
                ValorUnitario = valorUnitario,
                ValorTotal = valorTotal,
                TipoMovimento = tipoMovimento,
                TipoItemVersao = tipoItemVersao,
                DataCriacao = dataCriacao.ToUniversalTime(),
                DataHoraAutorizacao = dataHoraAutorizacao?.ToUniversalTime(),
                DataHoraMovimentacao = dataHoraMovimentacao?.ToUniversalTime(),
                DataEntrega = dataEntrega?.ToUniversalTime()
            };

            if (documentos != null)
            {
                foreach (var doc in documentos)
                {
                   var docItemVersao = new DocumentoItemVersao
                   {
                       Documento = doc,
                       ItemVersao = versao
                   };

                     versao.AdicionarDocumentoItemVersao(docItemVersao);
                }
            }

            return versao;
        }
    }
}
