using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Domain.Factories.Interfaces
{
    public interface IItemVersaoFactory
    {
        ItemVersao Criar(
            Item item,
            ItemVersao versaoAnterior,
            PessoaJuridica fornecedor,
            PessoaJuridica oficina,
            Ativo ativo,
            int quantidade,
            decimal valorUnitario,
            decimal valorTotal,
            TipoMovimentoItem tipoMovimento,
            TipoItemVersao tipoItemVersao,
            DateTime dataCriacao,
            DateTime? dataHoraAutorizacao,
            DateTime? dataHoraMovimentacao,
            DateTime? dataEntrega,
            IEnumerable<Documento>? documentos
        );
    }
}
