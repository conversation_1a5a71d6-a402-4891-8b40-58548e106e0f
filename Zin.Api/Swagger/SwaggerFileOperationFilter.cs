using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Zin.Api.Extensions
{
    // This filter allows file upload parameters to be properly displayed in Swagger UI
    public class SwaggerFileOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (operation.RequestBody == null)
                return;

            var fileParameters = context.MethodInfo.GetParameters()
                .Where(p => p.ParameterType == typeof(IFormFile) ||
                            (p.ParameterType.IsGenericType && p.ParameterType.GetGenericTypeDefinition() == typeof(IEnumerable<>) &&
                             p.ParameterType.GetGenericArguments()[0] == typeof(IFormFile)))
                .ToList();

            if (!fileParameters.Any())
                return;

            operation.RequestBody.Content["multipart/form-data"] = new OpenApiMediaType
            {
                Schema = new OpenApiSchema
                {
                    Type = "object",
                    Properties = fileParameters.ToDictionary(
                        p => p.Name,
                        p => new OpenApiSchema { Type = "string", Format = "binary" }
                    ),
                    Required = fileParameters.Select(p => p.Name).ToHashSet()
                }
            };
        }
    }
}
