using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Zin.Api.Filters
{
    public class TenantHeaderOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            operation.Parameters ??= new List<OpenApiParameter>();
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "x-tenant-id",
                In = ParameterLocation.Header,
                Required = true, // ou false, se preferir opcional
                Schema = new OpenApiSchema { Type = "string" },
                Description = "Identificador do tenant"
            });
        }
    }
}