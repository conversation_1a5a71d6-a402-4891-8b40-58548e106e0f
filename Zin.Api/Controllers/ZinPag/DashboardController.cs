using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [Route("dashboard")]
    [ApiController]
    public class DashboardController(IDashboardService dashboardService) : ControllerBase
    {
        public IDashboardService _dashboardService { get; } = dashboardService;

        [HttpGet("gerar")]
        public async Task<IActionResult> GerarInformacoesCards()
        {
            return Ok(await dashboardService.GerarInformacoesCardsAsync());
        }


        [HttpGet("cards")]
        public async Task<IActionResult> Cards()
        => Ok(await dashboardService.ObterCardsAsync());
    }
}
