using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Zin.Api.Shared;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [ApiController]
    [Route("importacoes")]
    public class ImportacaoController
    (
        IImportacaoService _importacaoService,
        IProcessamentoImportacaoService _processamentoImportacaoService
    ) : ControllerBase
    {         
        [HttpPost("importar")]
        public async Task<IActionResult> ImportaAgregador([FromBody] ImportacaoAgregadorDTO dto)
        {
            var resultado = await _importacaoService.ImportarAgregadorAsync(dto);
            return this.ToActionResult(resultado, locationFactory: () => $"/importacoes/{resultado.Conteudo!.IdImportacao}");
        }

        [HttpPost("processar-todos")]
        public async Task<IActionResult> ProcessarAgregador(int tamanhoLote)
        {
            await _processamentoImportacaoService.ProcessaImportacaoAgregadorTodosClientesAsync(tamanhoLote);
            return Ok();
        }

        [HttpPost("processar-cliente")]
        public async Task<IActionResult> ProcessarAgregadorCliente(int tamanhoLote)
        {
            if (!Request.Headers.TryGetValue("x-tenant-id", out var tenantId) || string.IsNullOrWhiteSpace(tenantId))
            {
                return BadRequest("Cabeçalho 'x-tenant-id' não informado.");
            }

            await _processamentoImportacaoService.ProcessaImportacaoAgregadorPorClienteAsync(tenantId!, tamanhoLote);
            return Ok();
        }
    }
}
