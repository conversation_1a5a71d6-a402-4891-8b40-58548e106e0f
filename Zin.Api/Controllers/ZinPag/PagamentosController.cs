using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Helpers.Clientes.Models;
using Zin.Domain.Enums;

namespace Zin.Api.Controllers.ZinPag
{
    [Route("pagamentos")]
    [ApiController]
    public class PagamentosController(
        IPagamentoService pagamentoService,
        IPagamentoImportacaoService pagamentoImportacaoService,
        IExcelMappingConfigurationProvider excelMappingConfigurationProvider,
        IPagamentoConsultaService pagamentoConsultaService) : ControllerBase
    {
        public IPagamentoService _pagamentoService { get; } = pagamentoService;
        public IPagamentoImportacaoService _pagamentoImportacaoService { get; } = pagamentoImportacaoService;
        public IExcelMappingConfigurationProvider _excelMappingConfigurationProvider { get; } = excelMappingConfigurationProvider;
        public IPagamentoConsultaService _pagamentoConsultaService { get; } = pagamentoConsultaService;

        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaPagamentoDTO dto)
        {
            var id = await _pagamentoService.CriarPagamentoAsync(dto);
            return Ok(id);
        }

        [HttpPost("importar-excel-pagamentos")]
        public async Task<IActionResult> ImportarExcel([FromForm] ImportExcelRequestDTO request)
        {
            if (HttpContext.Items["ClienteSelecionado"] is not Cliente clienteSelecionado)
                return BadRequest("x-tenant-id inválido.");

            try
            {
                var resultado = await _pagamentoImportacaoService.ImportarAsync(request.File, clienteSelecionado.Cnpj!);
                return Ok(resultado);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("cancelar/{pagamentoId:int}")]
        public async Task<IActionResult> Cancelar(int pagamentoId)
        {
            var resultado = await _pagamentoService.CancelarPagamentoAsync(pagamentoId);
            return Ok(resultado);
        }

        [HttpPost("liquidar/{pagamentoId:int}")]
        public async Task<IActionResult> LiquidarPagamento(int pagamentoId, decimal valorPago)
        {
            var resultado = await _pagamentoService.ConfirmarPagamentoManualAsync(pagamentoId, valorPago);
            return Ok(resultado);
        }

        [HttpGet("liquidacoes/{liquidacaoId:int}/comprovante")]
        public async Task<IActionResult> DownloadComprovanteLiquidacao(int liquidacaoId)
        {
            var result = await _pagamentoService.DownloadComprovanteLiquidacaoAsync(liquidacaoId);

            if (result == null)
            {
                return NotFound("Comprovante não encontrado ou liquidacao sem comprovante.");
            }

            var (fileContents, contentType, fileName) = result.Value;
            return File(fileContents, contentType, fileName);
        }

        // ===== NOVOS ENDPOINTS GET =====

        [HttpGet]
        public async Task<IActionResult> ListarPagamentos(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] StatusPagamento? status = null,
            [FromQuery] DateTime? dataInicio = null,
            [FromQuery] DateTime? dataFim = null,
            [FromQuery] string? cnpjBeneficiario = null,
            [FromQuery] string? numeroNF = null)
        {
            if (HttpContext.Items["ClienteSelecionado"] is not Cliente clienteSelecionado)
                return BadRequest("x-tenant-id inválido.");

            try
            {
                var resultado = await _pagamentoConsultaService.ListarPagamentosAsync(
                    clienteSelecionado.Id, page, pageSize, status, dataInicio, dataFim, cnpjBeneficiario, numeroNF);
                return Ok(resultado);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("lotes")]
        public async Task<IActionResult> ListarLotesImportacao(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] StatusLoteImportacao? status = null,
            [FromQuery] DateTime? dataInicio = null,
            [FromQuery] DateTime? dataFim = null,
            [FromQuery] string? usuario = null)
        {
            if (HttpContext.Items["ClienteSelecionado"] is not Cliente clienteSelecionado)
                return BadRequest("x-tenant-id inválido.");

            try
            {
                var resultado = await _pagamentoConsultaService.ListarLotesImportacaoAsync(
                    clienteSelecionado.Id, page, pageSize, status, dataInicio, dataFim, usuario);
                return Ok(resultado);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("lotes/{loteId:int}")]
        public async Task<IActionResult> ObterDetalhesLote(int loteId)
        {
            if (HttpContext.Items["ClienteSelecionado"] is not Cliente clienteSelecionado)
                return BadRequest("x-tenant-id inválido.");

            try
            {
                var resultado = await _pagamentoConsultaService.ObterDetalhesLoteAsync(loteId, clienteSelecionado.Id);
                if (resultado == null)
                    return NotFound("Lote não encontrado.");

                return Ok(resultado);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("lotes/{loteId:int}/linhas")]
        public async Task<IActionResult> ListarLinhasLote(
            int loteId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] StatusLinhaPagamento? status = null,
            [FromQuery] OperacaoLinhaPagamento? operacao = null)
        {
            if (HttpContext.Items["ClienteSelecionado"] is not Cliente clienteSelecionado)
                return BadRequest("x-tenant-id inválido.");

            try
            {
                var resultado = await _pagamentoConsultaService.ListarLinhasLoteAsync(
                    loteId, clienteSelecionado.Id, page, pageSize, status, operacao);
                return Ok(resultado);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("lotes/{loteId:int}/arquivo")]
        public async Task<IActionResult> DownloadArquivoLote(int loteId)
        {
            if (HttpContext.Items["ClienteSelecionado"] is not Cliente clienteSelecionado)
                return BadRequest("x-tenant-id inválido.");

            try
            {
                var resultado = await _pagamentoConsultaService.DownloadArquivoLoteAsync(loteId, clienteSelecionado.Id);
                if (resultado == null)
                    return NotFound("Arquivo do lote não encontrado.");

                var (fileContents, contentType, fileName) = resultado.Value;
                return File(fileContents, contentType, fileName);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}