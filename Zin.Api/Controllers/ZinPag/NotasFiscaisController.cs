using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Documentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Helpers.Clientes;

namespace Zin.Api.Controllers.ZinPag
{
    [Route("notasFiscais")]
    [ApiController]
    public class NotasFiscaisController(
        IDocumentoService documentosService, 
        IHttpContextAccessor httpContextAccessor) : ControllerBase
    {
        private readonly IDocumentoService _documentosService = documentosService;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

        [HttpPost("xml")]
        public async Task<IActionResult> Criar([FromForm] CriaDocumentoDTO dto)
        {
            var usuario = _httpContextAccessor.HttpContext?.User;
            var cnpjCliente = ClienteClaimsHelper.ExtrairCnpjBaseDeDadosDoClienteDeClaims(usuario!);
            
            var id = await _documentosService.CriarDocumentoAsync(dto, cnpjCliente);
            return Ok(id);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> CancelarNotaFiscal(int id)
        {
            var resultado = await _documentosService.CancelarDocumentoAsync(id);
            if (!resultado)
                return NotFound("Nota fiscal não encontrada ou já cancelada.");
            return NoContent();
        }
    }
}
