using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Ressarcimentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;

namespace Zin.Api.Controllers.ZinPag
{
    [Route("ressarcimentos")]
    [ApiController]
    public class RessarcimentosController(IRessarcimentoService ressarcimentoService) : ControllerBase
    {
        private readonly IRessarcimentoService _ressarcimentoService = ressarcimentoService;

        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaRessarcimentoDto dto)
        {
            var id = await _ressarcimentoService.CriarRessarcimentoAsync(dto);
            return Ok(id);
        }

        [HttpPost("gerar-cobranca")]
        public async Task<IActionResult> GerarCobranca([FromBody] GeraCobrancaDTO dto)
        {
            var cobrancaId = await _ressarcimentoService.GerarCobrancaAsync(dto);
            return Ok(new { CobrancaId = cobrancaId });
        }

        [HttpGet("cobrados")]
        public async Task<IActionResult> ListarCobrados(FiltrosDTO filtros)
        {
            var ressarcimentos = await _ressarcimentoService.ListarCobradosAsync(filtros);

            return Ok(ressarcimentos);
        }

        [HttpGet("Concluidos")]
        public async Task<IActionResult> ListarConcluidos(FiltrosDTO filtros)
        {
            var ressarcimentos = await _ressarcimentoService.ListarConcluidosAsync(filtros);

            return Ok(ressarcimentos);
        }

        [HttpGet("liquidacoes/{liquidacaoId:int}/comprovante")]
        public async Task<IActionResult> DownloadComprovanteLiquidacao(int liquidacaoId)
        {
            var result = await _ressarcimentoService.DownloadComprovanteLiquidacaoRessarcimentoAsync(liquidacaoId);

            if (result == null)
            {
                return NotFound("Comprovante não encontrado ou liquidacao sem comprovante.");
            }

            var (fileContents, contentType, fileName) = result.Value;
            return File(fileContents, contentType, fileName);
        }
    }
}
