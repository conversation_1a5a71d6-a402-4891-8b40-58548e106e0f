using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.Processos.Interfaces;

namespace Zin.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProcessosController : ControllerBase
    {
        private readonly IProcessosService _processosService;

        public ProcessosController(IProcessosService processosService)
        {
            _processosService = processosService;
        }

        /// <summary>
        /// Processa itens duplicados para todos agregadores em processamento.
        /// </summary>
        [HttpPost("item-duplicado")]
        public async Task<IActionResult> ProcessarItemDuplicado()
        {
            await _processosService.ProcessaItemDuplicadoAsync();
            return Ok(new { message = "Processamento de itens duplicados concluído." });
        }

        /// <summary>
        /// Processa pagamentos duplicados para todos agregadores em processamento.
        /// </summary>
        [HttpPost("pagamento-duplicado")]
        public async Task<IActionResult> ProcessarPagamentoDuplicado()
        {
            await _processosService.ProcessaPagamentoDuplicadoAsync();
            return Ok(new { message = "Processamento de pagamentos duplicados concluído." });
        }

        /// <summary>
        /// Processa possíveis pagamentos para todos agregadores em processamento.
        /// </summary>
        [HttpPost("possivel-pagamento")]
        public async Task<IActionResult> ProcessarPossivelPagamento()
        {
            await _processosService.ProcessaPossivelPagamentoAsync();
            return Ok(new { message = "Processamento de possíveis pagamentos concluído." });
        }

        /// <summary>
        /// Processa possíveis ressarcimentos para todos agregadores em processamento.
        /// </summary>
        [HttpPost("possivel-ressarcimento")]
        public async Task<IActionResult> ProcessarPossivelRessarcimento()
        {
            await _processosService.ProcessaPossivelRessarcimentoAsync();
            return Ok(new { message = "Processamento de possíveis ressarcimentos concluído." });
        }

        /// <summary>
        /// Processa todos os fluxos em sequência: item duplicado, pagamento duplicado, possível pagamento, possível ressarcimento.
        /// </summary>
        [HttpPost("todos")]
        public async Task<IActionResult> ProcessarTodos()
        {
            await _processosService.ProcessaItemDuplicadoAsync();
            await _processosService.ProcessaPagamentoDuplicadoAsync();
            await _processosService.ProcessaPossivelPagamentoAsync();
            await _processosService.ProcessaPossivelRessarcimentoAsync();
            return Ok(new { message = "Processamento completo de todos os fluxos concluído." });
        }
    }
}