using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Enderecos;
using Zin.Application.Services.Cadastros.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("enderecos")]
    public class EnderecosController(IEnderecoService enderecoService) : ControllerBase
    {
        private readonly IEnderecoService _enderecoService = enderecoService;

        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(int id, [FromBody] AtualizaEnderecoDto enderecoDto)
        {
            await _enderecoService.AtualizarEnderecoAsync(id, enderecoDto);
            return Ok();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Deletar(int id)
        {
            await _enderecoService.DeletarEnderecoAsync(id);
            return Ok(id);
        }
    }
}