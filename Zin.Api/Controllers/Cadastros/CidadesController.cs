using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.Cadastros.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("cidades")]
    public class CidadesController(ICidadeService cidadeService) : ControllerBase
    {
        private readonly ICidadeService _cidadeService = cidadeService;

        [HttpGet]
        public async Task<IActionResult> Listar(int idEstado)
        {
            var cidades  = await _cidadeService.ListarCidadesAsync(idEstado);
            return Ok(cidades);
        }
    }
}