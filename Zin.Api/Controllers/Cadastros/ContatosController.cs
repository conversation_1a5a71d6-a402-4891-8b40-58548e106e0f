using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Contato;
using Zin.Application.Services.Cadastros.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("contatos")]
    public class ContatosController(IContatoService contatoService) : ControllerBase
    {
        private readonly IContatoService _contatoService = contatoService;

        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(int id, [FromBody] AtualizaContatoDto contatoDto)
        {
            await _contatoService.AtualizarContatoAsync(id, contatoDto);
            return Ok();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Deletar(int id)
        {
            await _contatoService.DeletarContatoAsync(id);
            return Ok(id);
        }

        // TODO: (Filipe) Revisar pois Tipos e TiposTelefones foram removidos
        // ***** !!!POR�M: O Front end provavelmente esta esperando esses endpoints, ent�o algu�m ter� que revisar no Front ******
        //[HttpGet("tipos")]
        //public async Task<IActionResult> Tipos(int id)
        //{
        //    var tipos = _contatoService.Tipos();
        //    return Ok(tipos);
        //}

        //[HttpGet("tiposTelefones")]
        //public async Task<IActionResult> TiposTelefones()
        //{
        //    var tipos = _contatoService.TiposTelefones();
        //    return Ok(tipos);
        //}
    }
}