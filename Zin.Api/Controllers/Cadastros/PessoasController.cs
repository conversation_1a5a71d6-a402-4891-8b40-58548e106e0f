using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Pessoas;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Enums;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("pessoas")]
    public class PessoasController(IPessoaService pessoaService) : ControllerBase
    {
        private readonly IPessoaService _pessoaService = pessoaService;

        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(int id, [FromBody] AtualizaPessoaDto pessoaDto)
        {
            await _pessoaService.AtualizarPessoaAsync(id, pessoaDto);
            return Ok();
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Buscar(int id)
        {
            var pessoa = await _pessoaService.BuscarPessoaPorIdAsync(id);
            return Ok(pessoa);
        }

        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaPessoaDto dto)
        {
            var id = await _pessoaService.CriaPessoaAsync(dto);
            return Ok(id);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Deletar(int id)
        {
            await _pessoaService.DeletarPessoaAsync(id);
            return Ok(id);
        }

        [HttpGet]
        public async Task<IActionResult> Listar(TipoPessoa? tipoPessoa, string? razaoSocial, string? nomeFantasia, string? cnpj)
        {
            var pessoas = await _pessoaService.ListarPessoasAsync(tipoPessoa, razaoSocial, nomeFantasia, cnpj);
            return Ok(pessoas);
        }

        [HttpPost("incluirEndereco")]
        public async Task<IActionResult> IncluirEndereco([FromBody] CriaPessoaEnderecoDto dto)
        {
            var id = await _pessoaService.IncluirEnderecoAsync(dto);
            return Ok(id);
        }

        [HttpPost("incluirContato")]
        public async Task<IActionResult> IncluirContato([FromBody] CriaPessoaContatoDto dto)
        {
            var id = await _pessoaService.IncluirContatoAsync(dto);
            return Ok(id);
        }

        [HttpPost("alteraEnderecoPrincipal")]
        public async Task<IActionResult> AlteraEnderecoPrincipal(int idPessoa, int idEndereco)
        {
            var id = await _pessoaService.AlteraEnderecoPrincipal(idPessoa,  idEndereco);
            return Ok(id);
        }

        [HttpPost("incluirDadoBancario")]
        public async Task<IActionResult> IncluirDadoBancario([FromBody] CriaPessoaDadoBancarioDto dto)
        {
            var id = await _pessoaService.IncluirDadoBancarioAsync(dto);
            return Ok(id);
        }


        [HttpPost("alteraDadoBancarioPrincipal")]
        public async Task<IActionResult> AlteraDadoBancarioPrincipal(int idPessoa, int idDadoBancario)
        {
            var id = await _pessoaService.AlteraDadoBancarioPrincipal(idPessoa, idDadoBancario);
            return Ok(id);
        }
    }
}