using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.DadoBancario;
using Zin.Application.Services.Cadastros.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("dadoBancario")]
    public class DadoBancarioController(IDadoBancarioService dadoBancarioService) : ControllerBase
    {
        private readonly IDadoBancarioService _dadoBancarioService = dadoBancarioService;

        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(int id, [FromBody] AtualizaDadoBancarioDto dadoBancarioDto)
        {
            await _dadoBancarioService.AtualizarDadoBancarioAsync(id, dadoBancarioDto);
            return Ok();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Deletar(int id)
        {
            await _dadoBancarioService.DeletarDadoBancarioAsync(id);
            return Ok(id);
        }
    }
}