using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("api/zinpag/condicoes/comparador")]
    public class CondicaoComparadorController : ControllerBase
    {
        private readonly ICondicaoComparadorService _service;

        public CondicaoComparadorController(ICondicaoComparadorService service)
        {
            _service = service;
        }

        [HttpPost("{idConfiguracao}/verificar")]
        public async Task<IActionResult> Verificar(Guid idConfiguracao, [FromBody] ItemVersao valores)
        {
            var resultado = await _service.VerificaAsync(idConfiguracao, valores);
            return Ok(new { resultado });
        }
    }
}
