using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.Cadastros.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("estados")]
    public class EstadosController(IEstadoService estadoService) : ControllerBase
    {
        private readonly IEstadoService _estadoService = estadoService;

        [HttpGet]
        public async Task<IActionResult> Listar()
        {
            var estados = await _estadoService.ListarEstadosAsync();
            return Ok(estados);
        }
    }
}