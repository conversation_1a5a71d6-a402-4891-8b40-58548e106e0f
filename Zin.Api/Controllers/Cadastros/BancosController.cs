using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.Cadastros.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("bancos")]
    public class BancosController(IBancoService bancoService) : ControllerBase
    {
        private readonly IBancoService _bancoService = bancoService;

        [HttpGet]
        public async Task<IActionResult> Listar()
        {
            var bancos = await _bancoService.ListarBancosAsync();
            return Ok(bancos);
        }

        [HttpGet("tiposConta")]
        public async Task<IActionResult> TiposConta()
        {
            var tipos = _bancoService.TiposConta();
            return Ok(tipos);
        }
    }
}