using Npgsql;
using Serilog;
using Zin.Api.ConfigsMap;
using Zin.Api.Extensions;
using Zin.Application.Configuration;

var builder = WebApplication.CreateBuilder(args);

// Configurar Serilog manualmente
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Registrar ILogger no contêiner de serviços
builder.Services.AddSingleton(Log.Logger);

try
{
    Log.Information("Iniciando a aplicação");

    builder.Services.AdicionaServicosCustomizados(builder.Configuration, Log.Logger);

    builder.Services.Configure<CaminhosConfig>(
        builder.Configuration.GetSection("Caminhos"));

    builder.Services.AddHttpContextAccessor();

    builder.Services.Configure<List<ExcelColumnMappingConfig>>(builder.Configuration.GetSection("ExcelMappings"));

    var app = builder.Build();

    Log.Information($"Ambiente: {app.Environment.EnvironmentName}");

    app.ConfiguraApp();

        app.Run();

}
catch (Exception ex)
{
    Log.Fatal(ex, "A aplicação falhou ao iniciar");
}
finally
{
    Log.CloseAndFlush();
}