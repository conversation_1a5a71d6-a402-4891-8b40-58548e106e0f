using Microsoft.AspNetCore.Mvc;
using Zin.Application.Shared.Retorno;

namespace Zin.Api.Shared
{
    public static class ResultExtensions
    {
        /// <summary>
        /// Converte ResultadoApp<T> em IActionResult com mapeamento de status HTTP e payload padronizado.
        /// </summary>
        public static IActionResult ToActionResult<T>(
            this ControllerBase controller,
            ResultadoApp<T> resultado,
            Func<string>? locationFactory = null) // use para 201 Created
        {
            if (resultado.Sucesso)
            {
                // Sucesso sem conteúdo → 204
                if (typeof(T) == typeof(Nada) || resultado.Conteudo is null)
                    return controller.NoContent();

                // 201 Created quando informado um Location
                if (locationFactory is not null)
                    return controller.Created(locationFactory(), resultado.Conteudo);

                // 200 Ok por padrão
                return controller.Ok(resultado.Conteudo);
            }

            // Erros padronizados
            var erros = (resultado.Erros ?? Array.Empty<ErroApp>())
                .Select(e => new
                {
                    tipo = e.Tipo.ToString(),
                    codigo = e.Codigo,
                    mensagem = e.Mensagem,
                    campo = e.Campo
                });

            var payload = new { sucesso = false, erros };

            var tipoErro = resultado.Erros?.FirstOrDefault()?.Tipo;

            return tipoErro switch
            {
                TipoErroApp.Validacao => controller.UnprocessableEntity(payload),                            
                TipoErroApp.Negocio => controller.BadRequest(payload), // 400
                TipoErroApp.NaoEncontrado => controller.NotFound(payload), // 404
                TipoErroApp.Conflito => controller.Conflict(payload), // 409
                TipoErroApp.NaoAutorizado => controller.Unauthorized(),  // 401 (sem body por segurança)
                TipoErroApp.Proibido => controller.Forbid(), // 403
                TipoErroApp.Infraestrutura => controller.StatusCode(StatusCodes.Status503ServiceUnavailable, payload), // 503
                _ => controller.Problem(statusCode: 500, title: "Erro inesperado")
            };
        }
    }
}
