using Microsoft.AspNetCore.Mvc;
using Zin.Api.ViewModels;

namespace Zin.Api.Shared
{
    public static class ControllerExtensions
    {
        public static IActionResult ToActionResult<T>(this ResponseViewModel<T> response) where T : class?
        {
            switch (response.Status)
            {
                case StatusCodes.Status200OK:
                    return new OkObjectResult(response) { StatusCode = response.Status };
                case StatusCodes.Status204NoContent:
                    return new NoContentResult();
                case StatusCodes.Status400BadRequest:
                    return new BadRequestObjectResult(response) { StatusCode = response.Status };
                case StatusCodes.Status401Unauthorized:
                    return new UnauthorizedObjectResult(response) { StatusCode = response.Status };
                case StatusCodes.Status404NotFound:
                    return new NotFoundObjectResult(response) { StatusCode = response.Status };
                case StatusCodes.Status409Conflict:
                    return new ConflictObjectResult(response) { StatusCode = response.Status };
                case StatusCodes.Status422UnprocessableEntity:
                    return new UnprocessableEntityObjectResult(response) { StatusCode = response.Status };
                default:
                    return new ObjectResult(response) { StatusCode = response.Status };
            }
        }
    }
}
