namespace Zin.Api.Middlewares
{
    public class TenantHeaderMiddleware(RequestDelegate next)
    {
        private readonly RequestDelegate _next = next;

        public async Task InvokeAsync(HttpContext context)
        {
            // Ignora valida��o para rotas do Swagger
            var path = context.Request.Path.Value;
            if (path != null && (path.StartsWith("/swagger") || path.StartsWith("/favicon.ico")))
            {
                await _next(context);
                return;
            }

            if (context.Request.Headers.TryGetValue("x-tenant-id", out var tenantId))
            {
                context.Items["TenantId"] = tenantId.ToString();
            }
            else
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsync("Header x-tenant-id � obrigat�rio.");
                return;
            }

            await _next(context);
        }

    }
}