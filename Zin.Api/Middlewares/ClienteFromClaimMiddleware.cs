using Serilog;
using System.Text.Json;
using Zin.Helpers.Clientes.Models;

namespace Zin.Api.Middlewares
{
    public class ClienteFromClaimMiddleware(RequestDelegate next)
    {
        private readonly RequestDelegate _next = next;

        public async Task InvokeAsync(HttpContext context)
        {
            var path = context.Request.Path.Value;
            if (path != null && (path.StartsWith("/swagger") || path.StartsWith("/favicon.ico")))
            {
                await _next(context);
                return;
            }

            if (!context.User.Identity?.IsAuthenticated ?? true)
            {
                await _next(context);
                return;
            }

            if (!context.Items.TryGetValue("TenantId", out var tenantIdObj) || tenantIdObj is not string tenantId)
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                Log.Information("TenantId n�o encontrado no contexto.");

                if (context.RequestServices.GetService<IWebHostEnvironment>() is IWebHostEnvironment env && env.IsDevelopment())
                {
                    await context.Response.WriteAsync("TenantId n�o encontrado no contexto.");
                }

                return;
            }

            var clientesClaim = context.User.FindFirst("clientes")?.Value;
            if (string.IsNullOrEmpty(clientesClaim))
            {
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                Log.Information("Claim 'clientes' n�o encontrada no token.");

                if (context.RequestServices.GetService<IWebHostEnvironment>() is IWebHostEnvironment env && env.IsDevelopment())
                {
                    await context.Response.WriteAsync("Claim 'clientes' n�o encontrada no token.");
                }

                return;
            }

            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                };
                var clientes = JsonSerializer.Deserialize<List<Cliente>>(clientesClaim, options);
                var clienteSelecionado = clientes?.FirstOrDefault(c => c.Id == tenantId);

                if (clienteSelecionado == null)
                {
                    context.Response.StatusCode = StatusCodes.Status403Forbidden;
                    Log.Information("Usu�rio n�o autorizado para este TenantId.");

                    if (context.RequestServices.GetService<IWebHostEnvironment>() is IWebHostEnvironment env && env.IsDevelopment())
                    {
                        await context.Response.WriteAsync("Usu�rio n�o autorizado para este TenantId.");
                    }
                    return;
                }

                context.Items["ClienteSelecionado"] = clienteSelecionado;
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                Log.Error($"Erro ao processar a claim 'clientes': {ex.Message}");

                if (context.RequestServices.GetService<IWebHostEnvironment>() is IWebHostEnvironment env && env.IsDevelopment())
                {
                    await context.Response.WriteAsync($"Erro ao processar a claim 'clientes': {ex.Message}");
                }
                return;
            }

            await _next(context);
        }
    }
}
