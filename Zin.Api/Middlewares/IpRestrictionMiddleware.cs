using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Zin.Api.Middlewares
{
    public class IpRestrictionMiddleware(RequestDelegate next,  IConfiguration configuration, Serilog.ILogger logger)
    {
        private readonly RequestDelegate _next = next;
        private readonly List<string> _allowedIps = configuration.GetSection("AllowedIps").Get<List<string>>() ?? [];
        private readonly Serilog.ILogger _logger = logger;

		public async Task InvokeAsync(HttpContext context)
        {
            var remoteIp = context.Connection.RemoteIpAddress?.ToString();

			_logger.Information($"Origem da solicita��o: {remoteIp}");

			if (remoteIp == null || !_allowedIps.Contains(remoteIp))
            {
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                await context.Response.WriteAsync("Access Denied");
                return;
            }

            await _next(context);
        }
    }
}