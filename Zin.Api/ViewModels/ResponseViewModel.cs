namespace Zin.Api.ViewModels
{
    public class ResponseViewModel<T> where T : class?
    {
        public int Status { get; set; }
        public string? Mensagem { get; set; }
        public T? Dados { get; set; }

        public bool TemErro => Status != StatusCodes.Status200OK;

        public static ResponseViewModel<T> Sucesso(T? dados, string? mensagem = null, int status = StatusCodes.Status200OK)
            => new() { Status = status, Mensagem = mensagem, Dados = dados };

        public static ResponseViewModel<T> Falha(string mensagem, T? dados = null, int status = StatusCodes.Status500InternalServerError)
            => new() { Status = status, Mensagem = mensagem, Dados = dados };
    }
}
