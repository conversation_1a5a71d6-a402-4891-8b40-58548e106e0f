using Zin.Api.Middlewares;

namespace Zin.Api.Extensions
{
    public static class AppExtensions
    {
        public static WebApplication ConfiguraApp(this WebApplication app)
        {
            app.UseCors("DefaultCorsPolicy");
            
            ConfiguraAutenticacaoEAutorizacao(app);
            InicializaSwagger(app);
            ConfiguraRedirecionamentoERoteamento(app);
            MapeiaControllers(app);
            UsaMiddlewaresCustomizados(app);

            return app;
        }

        public static WebApplication UsaMiddlewaresCustomizados(this WebApplication app)
        {
            app.UseMiddleware<IpRestrictionMiddleware>();
            app.UseMiddleware<TenantHeaderMiddleware>();
            app.UseMiddleware<ClienteFromClaimMiddleware>();

            return app;
        }

        private static void InicializaSwagger(this WebApplication app)
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "SMART API v1");
            });

            if (!app.Environment.IsDevelopment())
            {
                app.UseHsts();
            }
        }

        private static void ConfiguraRedirecionamentoERoteamento(WebApplication app)
        {
            app.UseHttpsRedirection();
            app.UseRouting();
        }

        private static void ConfiguraAutenticacaoEAutorizacao(WebApplication app)
        {
            // Adicionar autentica��o e autoriza��o ao pipeline
            app.UseAuthentication();
            app.UseAuthorization();
        }

        private static void MapeiaControllers(WebApplication app)
        {
            app.MapControllers();
        }
    }
}
