{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ExcelMappings": [
    // Mapeamento .xlsx de pagamentos default (sem CNPJ definido)
    {
      "ColumnMappings": {
        "Sinistro": "A",
        "Placa": "B",
        "CnpjPagador": "C",
        "NomePagador": "D",
        "CnpjCpfFavorecido": "E",
        "Favorecido": "F",
        "NumeroNF": "G",
        "DataEmissaoNF": "H",
        "DataAutorizacaoPagamento": "I",
        "DataPagamento": "J",
        "ValorPago": "K",
        "TipoDocumento": "L",
        "StatusPagamento": "M",
        "FormaPagamento": "N",
        "BancoAgConta": "O",
        "ChavePix": "P"
      }
    },
    // <PERSON><PERSON><PERSON> definições por CNPJ
    {
      "Cnpj": "34345678000195",
      "ColumnMappings": {
        "Sinistro": "A",
        "Placa": "B",
        "CnpjPagador": "C",
        "NomePagador": "D",
        "CnpjCpfFavorecido": "E",
        "Favorecido": "F",
        "NumeroNF": "G",
        "DataEmissaoNF": "H",
        "DataAutorizacaoPagamento": "I",
        "DataPagamento": "J",
        "ValorPago": "K",
        "TipoDocumento": "L",
        "StatusPagamento": "M",
        "FormaPagamento": "N",
        "BancoAgConta": "O",
        "ChavePix": "P"
      }
    }
  ],
  "JwtSecretVariableName": "JWT_SECRET_DEV",
  "AesKeyVariableName": "AES_KEY_DEV",
  "AesIvVariableName": "AES_IV_DEV",
  "SiteConfig": {
    "BaseUrl": "http://localhost:5010/"
  },
  "NotificationService": {
    "BaseUrl": "http://localhost:5118" //alterar em prod
  },
  "UseMockApp": false,
  "Caminhos": {
    "Documentos": "C:\\Zinpag\\Documentos"
  },
  "CorsSettings": {
    "AllowedOrigins": [
      "https://zin.sistemaprismatec.com.br",
      "http://localhost:5010",
      "http://localhost:4200"
    ]
  },
  "UsarDadosMockados": false,
  "IpRestriction": {
    "Enabled": false,
    "AllowedIps": [
      "::1",
      "127.0.0.1"
    ]
  }
}
